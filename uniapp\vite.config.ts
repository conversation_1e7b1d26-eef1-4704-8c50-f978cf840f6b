import { defineConfig, loadEnv } from "vite";
import uni from "@dcloudio/vite-plugin-uni";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '');

  return {
    plugins: [uni()],
    define: {
      // 将环境变量注入到应用中
      'process.env.NODE_ENV': JSON.stringify(env.NODE_ENV || mode),
    },
    envPrefix: 'VITE_', // 只有以 VITE_ 开头的环境变量会被暴露给客户端
  };
});
