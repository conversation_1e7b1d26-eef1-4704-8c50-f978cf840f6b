"use strict";
const common_vendor = require("../common/vendor.js");
const utils_audio = require("../utils/audio.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "SettingsModal",
  props: {
    visible: { type: Boolean }
  },
  emits: ["close", "settingsChange"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const settings = common_vendor.reactive({
      backgroundMusic: true,
      soundEffects: true,
      vibration: true
    });
    const initSettings = () => {
      const currentSettings = utils_audio.audioManager.getSettings();
      Object.assign(settings, currentSettings);
    };
    common_vendor.watch(() => props.visible, (newVisible) => {
      if (newVisible) {
        initSettings();
      }
    });
    const handleBackgroundMusicChange = (event) => {
      const enabled = event.detail.value;
      settings.backgroundMusic = enabled;
      utils_audio.audioManager.updateSettings({ backgroundMusic: enabled });
      if (enabled) {
        utils_audio.audioManager.playBackgroundMusic("main");
      } else {
        utils_audio.audioManager.stopBackgroundMusic();
      }
      emit("settingsChange", { ...settings });
      console.log("背景音乐设置已更改:", enabled);
    };
    const handleSoundEffectsChange = (event) => {
      const enabled = event.detail.value;
      settings.soundEffects = enabled;
      utils_audio.audioManager.updateSettings({ soundEffects: enabled });
      emit("settingsChange", { ...settings });
      console.log("音效设置已更改:", enabled);
    };
    const handleVibrationChange = (event) => {
      const enabled = event.detail.value;
      settings.vibration = enabled;
      utils_audio.audioManager.updateSettings({ vibration: enabled });
      if (enabled) {
        utils_audio.audioManager.vibrate("short");
      }
      emit("settingsChange", { ...settings });
      console.log("震动设置已更改:", enabled);
    };
    const testAudio = () => {
      utils_audio.audioManager.playSoundEffect("click");
      setTimeout(() => {
        utils_audio.audioManager.playSoundEffect("success");
      }, 500);
      utils_audio.audioManager.vibrate("short");
      common_vendor.index.showToast({
        title: "音效测试完成",
        icon: "none",
        duration: 1500
      });
    };
    const closeModal = () => {
      emit("close");
    };
    const handleOverlayClick = () => {
      closeModal();
    };
    initSettings();
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: _ctx.visible
      }, _ctx.visible ? {
        b: common_vendor.o(closeModal),
        c: settings.backgroundMusic,
        d: common_vendor.o(handleBackgroundMusicChange),
        e: settings.soundEffects,
        f: common_vendor.o(handleSoundEffectsChange),
        g: settings.vibration,
        h: common_vendor.o(handleVibrationChange),
        i: common_vendor.o(testAudio),
        j: common_vendor.o(closeModal),
        k: common_vendor.o(() => {
        }),
        l: common_vendor.o(handleOverlayClick)
      } : {});
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-67bc804a"]]);
wx.createComponent(Component);
