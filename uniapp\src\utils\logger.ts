/**
 * 全局日志工具
 * 根据环境自动控制日志输出
 * 只在 development 环境下输出日志
 */

import { getCurrentEnvironment } from './env'

/**
 * 检查是否应该输出日志
 */
function shouldLog(): boolean {
  return getCurrentEnvironment() === 'development'
}

/**
 * 条件性日志输出
 */
export const logger = {
  /**
   * 普通日志
   */
  log: (...args: any[]) => {
    if (shouldLog()) {
      console.log(...args)
    }
  },

  /**
   * 信息日志
   */
  info: (...args: any[]) => {
    if (shouldLog()) {
      console.info(...args)
    }
  },

  /**
   * 警告日志
   */
  warn: (...args: any[]) => {
    if (shouldLog()) {
      console.warn(...args)
    }
  },

  /**
   * 错误日志（总是输出，但在生产环境下简化）
   */
  error: (...args: any[]) => {
    if (shouldLog()) {
      console.error(...args)
    } else {
      // 生产环境下只输出简化的错误信息
      console.error('An error occurred')
    }
  },

  /**
   * 调试日志
   */
  debug: (...args: any[]) => {
    if (shouldLog()) {
      console.debug('[DEBUG]', ...args)
    }
  },

  /**
   * 性能测试
   */
  time: (label: string) => {
    if (shouldLog()) {
      console.time(label)
    }
  },

  /**
   * 结束性能测试
   */
  timeEnd: (label: string) => {
    if (shouldLog()) {
      console.timeEnd(label)
    }
  },

  /**
   * 分组开始
   */
  group: (label?: string) => {
    if (shouldLog()) {
      console.group(label)
    }
  },

  /**
   * 分组结束
   */
  groupEnd: () => {
    if (shouldLog()) {
      console.groupEnd()
    }
  },

  /**
   * 表格输出
   */
  table: (data: any) => {
    if (shouldLog()) {
      console.table(data)
    }
  },

  /**
   * 断言
   */
  assert: (condition: boolean, ...args: any[]) => {
    if (shouldLog()) {
      console.assert(condition, ...args)
    }
  }
}

/**
 * 创建带前缀的日志器
 */
export function createLogger(prefix: string) {
  return {
    log: (...args: any[]) => logger.log(`[${prefix}]`, ...args),
    info: (...args: any[]) => logger.info(`[${prefix}]`, ...args),
    warn: (...args: any[]) => logger.warn(`[${prefix}]`, ...args),
    error: (...args: any[]) => logger.error(`[${prefix}]`, ...args),
    debug: (...args: any[]) => logger.debug(`[${prefix}]`, ...args),
    time: (label: string) => logger.time(`[${prefix}] ${label}`),
    timeEnd: (label: string) => logger.timeEnd(`[${prefix}] ${label}`),
    group: (label?: string) => logger.group(label ? `[${prefix}] ${label}` : `[${prefix}]`),
    groupEnd: () => logger.groupEnd(),
    table: (data: any) => logger.table(data),
    assert: (condition: boolean, ...args: any[]) => logger.assert(condition, `[${prefix}]`, ...args)
  }
}

/**
 * 页面级别的日志器
 */
export const pageLogger = {
  index: createLogger('首页'),
  game: createLogger('游戏'),
  login: createLogger('登录'),
  debug: createLogger('调试'),
  levelSelection: createLogger('关卡选择'),
  memberCenter: createLogger('会员中心')
}

/**
 * API级别的日志器
 */
export const apiLogger = createLogger('API')

/**
 * 工具级别的日志器
 */
export const utilLogger = createLogger('工具')

/**
 * 音频级别的日志器
 */
export const audioLogger = createLogger('音频')

/**
 * 分享级别的日志器
 */
export const shareLogger = createLogger('分享')

// 默认导出通用日志器
export default logger
