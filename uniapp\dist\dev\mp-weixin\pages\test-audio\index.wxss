/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.test-audio-container.data-v-0a20da84 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea, #764ba2);
  padding: 40rpx 20rpx;
}
.header.data-v-0a20da84 {
  text-align: center;
  margin-bottom: 40rpx;
}
.title.data-v-0a20da84 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}
.subtitle.data-v-0a20da84 {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.status-card.data-v-0a20da84 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.status-title.data-v-0a20da84 {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 20rpx;
}
.status-info.data-v-0a20da84 {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.status-item.data-v-0a20da84 {
  font-size: 24rpx;
  color: #4a5568;
  line-height: 1.4;
}
.control-section.data-v-0a20da84 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.section-title.data-v-0a20da84 {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 20rpx;
}
.button-grid.data-v-0a20da84 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
.control-btn.data-v-0a20da84 {
  flex: 1;
  min-width: 200rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s;
}
.control-btn.primary.data-v-0a20da84 {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #ffffff;
}
.control-btn.secondary.data-v-0a20da84 {
  background: #f8f9fa;
  color: #495057;
  border: 2rpx solid #dee2e6;
}
.control-btn.danger.data-v-0a20da84 {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: #ffffff;
}
.control-btn.effect.data-v-0a20da84 {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  color: #ffffff;
}
.control-btn.vibrate.data-v-0a20da84 {
  background: linear-gradient(135deg, #ffa726, #ff7043);
  color: #ffffff;
}
.control-btn.config.data-v-0a20da84 {
  background: linear-gradient(135deg, #ab47bc, #8e24aa);
  color: #ffffff;
}
.control-btn.data-v-0a20da84:active {
  transform: scale(0.95);
  opacity: 0.8;
}
.settings-list.data-v-0a20da84 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.setting-item.data-v-0a20da84 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #e2e8f0;
}
.setting-item.data-v-0a20da84:last-child {
  border-bottom: none;
}
.setting-label.data-v-0a20da84 {
  font-size: 26rpx;
  color: #2d3748;
  font-weight: 500;
}
.config-info.data-v-0a20da84 {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}
.config-item.data-v-0a20da84 {
  font-size: 22rpx;
  color: #4a5568;
  line-height: 1.4;
  word-break: break-all;
}
.back-btn-container.data-v-0a20da84 {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
}
.back-btn.data-v-0a20da84 {
  background: rgba(0, 0, 0, 0.3);
  color: #ffffff;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s;
}
.back-btn.data-v-0a20da84:active {
  background: rgba(0, 0, 0, 0.5);
  transform: scale(0.95);
}
.back-text.data-v-0a20da84 {
  color: #ffffff;
}