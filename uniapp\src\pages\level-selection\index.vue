<template>
  <view class="level-selection-container">
    <!-- 头部信息 -->
    <view class="header-section">
      <view class="header-content">
        <text class="page-title">选择关卡</text>
        <text class="page-subtitle">选择你想要挑战的关卡开始游戏吧！</text>
      </view>
      
      <!-- 用户进度信息 -->
      <view v-if="userInfo" class="progress-card">
        <view class="progress-item">
          <text class="progress-label">已解锁</text>
          <text class="progress-value">{{ userInfo.unlockedLevels || 0 }}</text>
        </view>
        <view class="progress-item">
          <text class="progress-label">已完成</text>
          <text class="progress-value">{{ userInfo.completedLevelIds?.length || 0 }}</text>
        </view>
        <view class="progress-item">
          <text class="progress-label">总关卡</text>
          <text class="progress-value">{{ levels.length }}</text>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="levelState.isLoading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载关卡...</text>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="levelState.error" class="error-container">
      <view class="error-icon">⚠️</view>
      <text class="error-title">加载失败</text>
      <text class="error-text">{{ levelState.error }}</text>
      <button class="retry-btn" @click="loadLevels">
        <text class="retry-text">重试</text>
      </button>
    </view>

    <!-- 关卡列表 -->
    <view v-else class="levels-container">
      <view class="levels-grid">
        <view
          v-for="level in levels"
          :key="level.id"
          class="level-card"
          :class="{
            'level-locked': level.locked,
            'level-completed': level.completed
          }"
          @click="selectLevel(level)"
        >
          <!-- 关卡主要内容区域 -->
          <view class="level-main-content">
            <!-- 关卡编号 -->
            <view class="level-number">{{ level.levelNumber }}</view>

            <!-- 关卡信息 -->
            <view class="level-info">
              <text class="level-name">{{ level.name }}</text>
              <text class="level-desc">{{ level.description }}</text>
            </view>

            <!-- 关卡状态和星级 -->
            <view class="level-status">
              <!-- 未解锁状态 -->
              <view v-if="level.locked" class="status-badge locked">
                <text class="status-text">🔒 未解锁</text>
              </view>
              <!-- 已完成状态 - 显示星级 -->
              <view v-else-if="level.completed" class="status-badge completed">
                <view class="level-stars">
                  <text
                    v-for="star in (level.maxStars || 0)"
                    :key="star"
                    class="star star-filled"
                  >
                    ⭐
                  </text>
                </view>
              </view>
              <!-- 可开始状态 -->
              <view v-else class="status-badge available">
                <text class="status-text">开始</text>
              </view>
            </view>
          </view>
          <!-- 关卡标签（显示在卡片下方，不可点击） -->
          <view v-if="level.tags && level.tags.length > 0" class="level-tags">
            <view
              v-for="tag in level.tags.slice(0, 3)"
              :key="tag.id"
              class="tag-item"
              :class="{ 'tag-vip': tag.isVip }"
              :style="{ backgroundColor: tag.color || '#f0f0f0' }"
            >
              <text class="tag-text">{{ tag.name }}</text>
              <text v-if="tag.isVip" class="tag-vip-icon">👑</text>
            </view>
            <view v-if="level.tags.length > 3" class="tag-more">
              <text class="tag-more-text">+{{ level.tags.length - 3 }}</text>
            </view>
          </view>

        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, nextTick } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import weixinApi from '../../api/weixin'
import { audioManager } from '../../utils/audio'
import { withLoading, createLoadingState } from '../../api/utils'
import { checkLoginAndRedirect } from '../../utils/auth'
import type { LevelInfo, UserInfo, levelState, DailyStatusResponse, ExtendedLevelInfo, UserStarStats } from '../../api/types'

// 状态管理
const levelState = reactive<levelState>({
  level: [],
  currentLevel: null,
  ...createLoadingState()
})

const userInfo = ref<UserInfo | null>(null)
const dailyStatus = ref<DailyStatusResponse | null>(null)
const userStarStats = ref<UserStarStats | null>(null)

// 页面参数
const scrollToLevelId = ref<string | null>(null)

// 计算属性：格式化的关卡列表
const levels = computed(() => {
  return levelState.level.map((level, index) => {
    const extendedLevel = level as ExtendedLevelInfo
    return {
      id: level.id,
      levelNumber: String(index + 1).padStart(2, '0'),
      name: level.name,
      description: level.description,
      library: getLibraryForLevel(level),
      locked: !level.isUnlocked,
      completed: level.isCompleted,
      difficulty: level.difficulty,
      maxStars: extendedLevel.userStars || 0,
      tags: extendedLevel.tags || [], // 使用 tagIds 字段
      isFavorited: extendedLevel.isFavorited || false // 添加收藏状态
    }
  })
})

// 计算属性：是否有锁定的关卡
const hasLockedLevels = computed(() => {
  return levels.value.some(level => level.locked)
})

// 词库数据（与首页保持一致）
const libraries = [
  {
    name: '基础词汇',
    words: [
      { english: 'apple', chinese: '苹果' },
      { english: 'book', chinese: '书' },
      { english: 'cat', chinese: '猫' },
      { english: 'dog', chinese: '狗' },
      { english: 'egg', chinese: '鸡蛋' },
      { english: 'fish', chinese: '鱼' },
      { english: 'girl', chinese: '女孩' },
      { english: 'hat', chinese: '帽子' }
    ]
  },
  {
    name: '动物世界',
    words: [
      { english: 'elephant', chinese: '大象' },
      { english: 'tiger', chinese: '老虎' },
      { english: 'lion', chinese: '狮子' },
      { english: 'monkey', chinese: '猴子' },
      { english: 'rabbit', chinese: '兔子' },
      { english: 'bird', chinese: '鸟' },
      { english: 'horse', chinese: '马' },
      { english: 'sheep', chinese: '羊' }
    ]
  },
  {
    name: '颜色彩虹',
    words: [
      { english: 'red', chinese: '红色' },
      { english: 'blue', chinese: '蓝色' },
      { english: 'green', chinese: '绿色' },
      { english: 'yellow', chinese: '黄色' },
      { english: 'purple', chinese: '紫色' },
      { english: 'orange', chinese: '橙色' },
      { english: 'black', chinese: '黑色' },
      { english: 'white', chinese: '白色' }
    ]
  },
  {
    name: '数字王国',
    words: [
      { english: 'one', chinese: '一' },
      { english: 'two', chinese: '二' },
      { english: 'three', chinese: '三' },
      { english: 'four', chinese: '四' },
      { english: 'five', chinese: '五' },
      { english: 'six', chinese: '六' },
      { english: 'seven', chinese: '七' },
      { english: 'eight', chinese: '八' }
    ]
  }
]

/**
 * 页面加载
 */
onLoad(async (options) => {
  // 检查登录状态
  const isLoggedIn = await checkLoginAndRedirect({
    toastMessage: '请先登录以选择关卡',
    redirectUrl: '/pages/login/index?redirect=' + encodeURIComponent('/pages/level-selection/index')
  })

  if (!isLoggedIn) {
    return
  }

  // 获取滚动到指定关卡的参数
  if (options?.scrollToLevel) {
    scrollToLevelId.value = options.scrollToLevel
    console.log('需要滚动到关卡:', scrollToLevelId.value)
  }
})

/**
 * 页面初始化
 */
onMounted(async () => {
  await initializePage()

  // 如果需要滚动到指定关卡，在数据加载完成后执行滚动
  if (scrollToLevelId.value) {
    await nextTick()
    scrollToLevel(scrollToLevelId.value)
  }
})

/**
 * 初始化页面数据
 */
const initializePage = async () => {
  await withLoading(levelState, async () => {
    // 1. 加载用户信息
    await loadUserInfo()
    
    // 2. 加载关卡列表
    await loadLevels()
    
    // 3. 加载每日状态
    await loadDailyStatus()
  }, {
    errorMessage: '页面加载失败，请重试'
  })
}

/**
 * 加载用户信息
 */
const loadUserInfo = async () => {
  try {
    const info = await weixinApi.getUserInfo()
    userInfo.value = info
    console.log('用户信息加载成功:', info)
  } catch (error) {
    console.error('加载用户信息失败:', error)
    // 用户信息加载失败不影响关卡显示
  }
}

/**
 * 加载关卡列表
 */
const loadLevels = async () => {
  try {
    // 优先尝试获取扩展关卡信息（包含星级和标签）
    try {
      const extendedLevels = await weixinApi.getExtendedLevels()
      levelState.level = extendedLevels
      console.log('扩展关卡列表加载成功:', extendedLevels)
      return
    } catch (extendedError) {
      console.warn('扩展关卡API不可用，使用基础API:', extendedError)
    }

    // 备用：使用基础关卡API
    const apiLevels = await weixinApi.getLevels()
    levelState.level = apiLevels
    console.log('基础关卡列表加载成功:', apiLevels)
  } catch (error) {
    console.error('加载关卡列表失败:', error)

    // 如果API加载失败，使用本地备用数据
    console.log('使用本地备用关卡数据')
    levelState.level = createFallbackLevels()
  }
}

/**
 * 加载每日状态
 */
const loadDailyStatus = async () => {
  try {
    const status = await weixinApi.getDailyStatus()
    dailyStatus.value = status
    console.log('每日状态加载成功:', status)
  } catch (error) {
    console.error('加载每日状态失败:', error)
    // 每日状态加载失败不影响关卡显示
  }
}

/**
 * 创建备用关卡数据
 */
const createFallbackLevels = (): LevelInfo[] => {
  return [
    {
      id: 'fallback-level-1',
      name: '第1关 - 基础词汇',
      difficulty: 1,
      description: '小学基础词汇练习',
      isUnlocked: true,
      isCompleted: false,
      createdAt: new Date().toISOString()
    },
    {
      id: 'fallback-level-2',
      name: '第2关 - 动物世界',
      difficulty: 2,
      description: '认识可爱的动物',
      isUnlocked: true,
      isCompleted: false,
      createdAt: new Date().toISOString()
    },
    {
      id: 'fallback-level-3',
      name: '第3关 - 颜色彩虹',
      difficulty: 3,
      description: '学习各种颜色',
      isUnlocked: false,
      isCompleted: false,
      createdAt: new Date().toISOString()
    },
    {
      id: 'fallback-level-4',
      name: '第4关 - 数字王国',
      difficulty: 4,
      description: '掌握数字表达',
      isUnlocked: false,
      isCompleted: false,
      createdAt: new Date().toISOString()
    }
  ]
}

/**
 * 根据关卡获取对应的词库
 */
const getLibraryForLevel = (level: LevelInfo) => {
  const difficultyIndex = (level.difficulty - 1) % libraries.length
  return libraries[difficultyIndex]
}

/**
 * 选择关卡
 */
const selectLevel = async (level: any) => {
  if (level.locked) {
    // 播放失败音效
    audioManager.playSoundEffect('fail')

    // 检查是否可以解锁
    await checkCanUnlock(level)
    return
  }

  // 播放点击音效
  audioManager.playSoundEffect('click')

  console.log('Selected level:', level)

  // 将选择的关卡信息存储到本地
  uni.setStorageSync('selectedLibrary', JSON.stringify(level.library))
  uni.setStorageSync('selectedLevel', JSON.stringify(level))

  // 直接跳转到游戏页面，不显示确认弹窗
  uni.navigateTo({
    url: '/pages/game/index'
  })
}

/**
 * 滚动到指定关卡
 */
const scrollToLevel = (levelId: string) => {
  try {
    console.log('开始滚动到关卡:', levelId)

    // 查找目标关卡的索引
    const targetIndex = levels.value.findIndex(level => level.id.toString() === levelId.toString())

    if (targetIndex === -1) {
      console.warn('未找到目标关卡:', levelId)
      return
    }

    console.log('找到目标关卡索引:', targetIndex)

    // 计算滚动位置（每个关卡卡片大约200rpx高度 + 间距）
    const scrollTop = targetIndex * 220 // 200rpx卡片高度 + 20rpx间距

    // 执行滚动
    uni.pageScrollTo({
      scrollTop: scrollTop,
      duration: 800,
      success: () => {
        console.log('滚动到关卡成功:', levelId)

        // 可选：高亮显示目标关卡
        setTimeout(() => {
          uni.showToast({
            title: `已定位到第${targetIndex + 1}关`,
            icon: 'none',
            duration: 1500
          })
        }, 500)
      },
      fail: (error) => {
        console.error('滚动到关卡失败:', error)
      }
    })
  } catch (error) {
    console.error('滚动到关卡出错:', error)
  }
}

/**
 * 检查是否可以解锁关卡
 */
const checkCanUnlock = async (level: any) => {
  try {
    // 刷新每日状态
    await loadDailyStatus()
    
    const status = dailyStatus.value
    if (!status) {
      uni.showToast({
        title: '该关卡尚未解锁',
        icon: 'none',
        duration: 1500
      })
      return
    }

    // VIP用户无限制
    if (status.isVip) {
      uni.showToast({
        title: '该关卡尚未解锁',
        icon: 'none',
        duration: 1500
      })
      return
    }

    // 检查解锁次数
    if (status.remainingUnlocks > 0) {
      uni.showModal({
        title: '解锁关卡',
        content: `是否使用1次解锁机会来解锁这个关卡？\n剩余解锁次数：${status.remainingUnlocks}`,
        confirmText: '解锁',
        cancelText: '取消',
        success: async (res) => {
          if (res.confirm) {
            // TODO: 调用解锁关卡的API
            uni.showToast({
              title: '关卡解锁成功！',
              icon: 'success'
            })
            
            // 刷新关卡列表
            await loadLevels()
          }
        }
      })
    } else {
      // 没有解锁次数，提示升级VIP或分享获取
      uni.showModal({
        title: '解锁次数不足',
        content: '今日解锁次数已用完\n• 升级VIP可无限解锁\n• 分享游戏可获得额外解锁机会',
        confirmText: '升级VIP',
        cancelText: '分享获取',
        success: (res) => {
          if (res.confirm) {
            showVipPackages()
          } else {
            // TODO: 触发分享功能
            uni.showToast({
              title: '请分享游戏给好友',
              icon: 'none'
            })
          }
        }
      })
    }
  } catch (error) {
    console.error('检查解锁状态失败:', error)
    uni.showToast({
      title: '该关卡尚未解锁',
      icon: 'none',
      duration: 1500
    })
  }
}

/**
 * 显示VIP套餐
 */
const showVipPackages = () => {
  // TODO: 实现VIP套餐显示逻辑
  uni.showToast({
    title: 'VIP功能开发中',
    icon: 'none'
  })
}

/**
 * 切换收藏状态
 */
const toggleFavorite = async (level: any) => {
  try {
    // 播放点击音效
    audioManager.playSoundEffect('click')

    if (level.isFavorited) {
      // 取消收藏
      await weixinApi.removeFavorite(level.id)
      level.isFavorited = false

      uni.showToast({
        title: '已取消收藏',
        icon: 'success',
        duration: 1500
      })
    } else {
      // 添加收藏
      await weixinApi.addFavorite(level.id)
      level.isFavorited = true

      uni.showToast({
        title: '已添加收藏',
        icon: 'success',
        duration: 1500
      })
    }

    // 播放成功音效
    audioManager.playSoundEffect('complete')
  } catch (error) {
    console.error('收藏操作失败:', error)

    // 播放失败音效
    audioManager.playSoundEffect('fail')

    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
      duration: 1500
    })
  }
}

/**
 * 返回首页
 */
const goBack = () => {
  // 播放点击音效
  audioManager.playSoundEffect('click')

  uni.navigateBack({
    delta: 1
  })
}
</script>

<style lang="scss" scoped>
.level-selection-container {
  min-height: 100vh;
  background: #fdf9f9;
  padding: 40rpx 20rpx;
}

.header-section {
  margin-bottom: 40rpx;
}

.header-content {
  text-align: center;
  margin-bottom: 32rpx;
  padding: 32rpx 24rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: #4a5568;
  font-weight: 500;
  line-height: 1.4;
}

.progress-card {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 24rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.progress-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.progress-label {
  font-size: 22rpx;
  color: #111;
}

.progress-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #111;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 32rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.error-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #111;
  margin-bottom: 16rpx;
}

.error-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.retry-btn {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: #ffffff;
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
  box-shadow: 0 8rpx 20rpx rgba(116, 185, 255, 0.3);
}

.retry-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.retry-text {
  color: inherit;
}

.levels-container {
  margin-bottom: 40rpx;
}

.levels-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.level-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.level-main-content {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.level-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.15);
}

.level-card.level-locked {
  opacity: 0.7;
}

.level-number {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  flex-shrink: 0;
}

.level-locked .level-number {
  background: #bbb;
  box-shadow: 0 4rpx 12rpx rgba(187, 187, 187, 0.3);
}

.level-completed .level-number {
  background: linear-gradient(135deg, #00b894, #00a085);
  box-shadow: 0 4rpx 12rpx rgba(0, 184, 148, 0.3);
}

.level-info {
  flex: 1;
  min-width: 0;
}

.level-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.level-desc {
  display: block;
  font-size: 24rpx;
  color: #718096;
  line-height: 1.3;
}

.level-locked .level-name,
.level-locked .level-desc {
  color: #999;
}

.level-status {
  flex-shrink: 0;
}

.status-badge {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  text-align: center;
}

.status-badge.locked {
  background: #f0f0f0;
}

.status-badge.completed {
  background: #d1f2eb;
}

.status-badge.available {
  background: #e3f2fd;
}

.status-text {
  font-size: 22rpx;
  font-weight: 500;
}

.locked .status-text {
  color: #999;
}

.completed .status-text {
  color: #00b894;
}

.available .status-text {
  color: #667eea;
}

/* 星级显示样式 */
.level-stars {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.star {
  font-size: 20rpx;
  color: #ddd;
  transition: color 0.2s;
}

.star-filled {
  color: #ffd700;
}

/* 收藏按钮样式 */
.favorite-btn {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transition: all 0.2s;
}

.favorite-btn:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 1);
}

.favorite-icon {
  font-size: 24rpx;
  transition: all 0.2s;
}

.favorite-icon.favorited {
  animation: heartbeat 0.6s ease-in-out;
}

@keyframes heartbeat {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.vip-promotion {
  margin-bottom: 40rpx;
}

.vip-card {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  padding: 32rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.vip-header {
  text-align: center;
  margin-bottom: 24rpx;
}

.vip-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #111;
  margin-bottom: 8rpx;
}

.vip-subtitle {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

.vip-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #ffd700, #ffb347);
  border-radius: 40rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  box-shadow: 0 8rpx 20rpx rgba(255, 215, 0, 0.3);
  transition: all 0.2s;
}

.vip-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.vip-btn-text {
  color: inherit;
}

.bottom-actions {
  display: flex;
  justify-content: center;
  padding-top: 20rpx;
}

.back-btn {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: #ffffff;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
  box-shadow: 0 8rpx 20rpx rgba(116, 185, 255, 0.3);
}

.back-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.back-text {
  color: inherit;
}

/* 关卡标签样式 */
.level-tags {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  background: #f0f0f0;
  pointer-events: none; /* 标签不可点击 */
}

.tag-vip {
  background: linear-gradient(135deg, #ffd700, #ffb347);
}

.tag-text {
  font-size: 20rpx;
  color: #666;
  font-weight: 400;
}

.tag-vip .tag-text {
  color: #8b4513;
  font-weight: 500;
}

.tag-vip-icon {
  font-size: 16rpx;
}

.tag-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  background: #e0e0e0;
  pointer-events: none;
}

.tag-more-text {
  font-size: 20rpx;
  color: #999;
  font-weight: 400;
}
</style>
