"use strict";
const common_vendor = require("../../common/vendor.js");
const api_weixin = require("../../api/weixin.js");
const utils_audio = require("../../utils/audio.js");
const utils_auth = require("../../utils/auth.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const activationCode = common_vendor.ref("");
    const isRedeeming = common_vendor.ref(false);
    const showResultModal = common_vendor.ref(false);
    const redeemResult = common_vendor.ref({
      success: false,
      message: ""
    });
    const canRedeem = common_vendor.computed(() => {
      return activationCode.value.trim().length > 0 && !isRedeeming.value;
    });
    common_vendor.onLoad(async () => {
      const isLoggedIn = await utils_auth.checkLoginAndRedirect({
        toastMessage: "请先登录以兑换激活码",
        redirectUrl: "/pages/login/index"
      });
      if (!isLoggedIn) {
        return;
      }
    });
    const redeemCode = async () => {
      if (!canRedeem.value) {
        return;
      }
      const code = activationCode.value.trim();
      if (!code) {
        common_vendor.index.showToast({
          title: "请输入激活码",
          icon: "none"
        });
        return;
      }
      try {
        isRedeeming.value = true;
        utils_audio.audioManager.playSoundEffect("click");
        const result = await api_weixin.weixinApi.redeemActivationCode(code);
        redeemResult.value = result;
        showResultModal.value = true;
        if (result.success) {
          utils_audio.audioManager.playSoundEffect("complete");
          utils_audio.audioManager.vibrate("short");
          activationCode.value = "";
        } else {
          utils_audio.audioManager.playSoundEffect("fail");
        }
      } catch (error) {
        console.error("兑换激活码失败:", error);
        utils_audio.audioManager.playSoundEffect("fail");
        redeemResult.value = {
          success: false,
          message: "兑换失败，请检查网络连接或稍后重试"
        };
        showResultModal.value = true;
      } finally {
        isRedeeming.value = false;
      }
    };
    const closeResultModal = () => {
      showResultModal.value = false;
      if (redeemResult.value.success)
        ;
    };
    const goBack = () => {
      utils_audio.audioManager.playSoundEffect("click");
      common_vendor.index.navigateBack();
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: isRedeeming.value,
        b: activationCode.value,
        c: common_vendor.o(($event) => activationCode.value = $event.detail.value),
        d: common_vendor.t(isRedeeming.value ? "兑换中..." : "立即兑换"),
        e: !canRedeem.value ? 1 : "",
        f: !canRedeem.value,
        g: common_vendor.o(redeemCode),
        h: showResultModal.value
      }, showResultModal.value ? common_vendor.e({
        i: common_vendor.t(redeemResult.value.success ? "🎉" : "❌"),
        j: common_vendor.t(redeemResult.value.success ? "兑换成功！" : "兑换失败"),
        k: common_vendor.t(redeemResult.value.message),
        l: redeemResult.value.success && redeemResult.value.package
      }, redeemResult.value.success && redeemResult.value.package ? {
        m: common_vendor.t(redeemResult.value.package.name),
        n: common_vendor.t(redeemResult.value.package.description)
      } : {}, {
        o: common_vendor.o(closeResultModal)
      }) : {}, {
        p: common_vendor.o(goBack)
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-9c6f8688"]]);
wx.createPage(MiniProgramPage);
