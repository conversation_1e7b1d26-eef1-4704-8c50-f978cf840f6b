# 英语单词游戏小程序二期开发计划

## 📋 项目概述

**项目名称**: 英语单词游戏小程序二期功能开发  
**开发周期**: 预计4-6周  
**主要目标**: 增强用户体验，提升商业价值，完善功能生态

## 🎯 功能模块总览

### 功能模块1：标签闯关系统
- **核心价值**: 个性化学习路径，提升用户粘性
- **商业价值**: 会员专享标签，促进付费转化
- **技术难度**: ⭐⭐⭐

### 功能模块2：关卡星级系统  
- **核心价值**: 成就感和竞争性，增强游戏性
- **商业价值**: 提升用户留存和活跃度
- **技术难度**: ⭐⭐⭐⭐

### 功能模块3：激活码系统
- **核心价值**: 营销推广工具，灵活的会员发放
- **商业价值**: 多渠道获客，精准营销
- **技术难度**: ⭐⭐⭐

### 功能模块4：收藏功能
- **核心价值**: 个人化内容管理，提升用户体验
- **商业价值**: 会员专享功能，增加付费动机
- **技术难度**: ⭐⭐

## 🚀 详细开发计划

## 功能模块1：标签闯关系统

### 1.1 关卡标签展示优化

**开发时间**: 2-3天  
**优先级**: 高

#### 需求描述
在关卡列表页面每个关卡下方展示相关标签（不可点击），优化视觉展示效果。

#### 技术实现
```vue
<!-- 关卡卡片中添加标签展示 -->
<view class="level-tags">
  <view 
    v-for="tag in level.tags" 
    :key="tag.id"
    class="tag-item"
    :class="{ 'tag-vip': tag.isVip }"
  >
    <text class="tag-text">{{ tag.name }}</text>
    <text v-if="tag.isVip" class="tag-vip-icon">👑</text>
  </view>
</view>
```

#### 样式设计
- **普通标签**: 浅灰色背景，深灰色文字
- **VIP标签**: 金色背景，深色文字，皇冠图标
- **布局**: 水平排列，自动换行，适当间距

#### 数据结构
```typescript
interface LevelTag {
  id: string
  name: string
  isVip: boolean
  color?: string
}

interface LevelInfo {
  // 现有字段...
  tags: LevelTag[]
}
```

### 1.2 标签选择闯关功能

**开发时间**: 3-4天  
**优先级**: 高

#### 需求描述
实现标签点击功能，会员专享标签的友好提示和预览功能。

#### 功能设计
1. **标签点击交互**
   - 普通标签：直接进入标签关卡列表
   - VIP标签：显示友好提示弹窗

2. **VIP提示优化**
   ```javascript
   const showVipTagPrompt = (tag) => {
     uni.showModal({
       title: '会员专享标签',
       content: `"${tag.name}"为会员专享，解锁所有主题可享受更全面的挑战！立即开通会员？`,
       showCancel: true,
       cancelText: '稍后再说',
       confirmText: '去开通',
       success: (res) => {
         if (res.confirm) {
           // 跳转到会员中心
           uni.navigateTo({
             url: '/pages/member-center/index'
           })
         }
       }
     })
   }
   ```

3. **预览功能**
   - 允许查看VIP标签下前10道题
   - 预览后再次提示开通会员

### 1.3 标签关卡列表页面

**开发时间**: 4-5天  
**优先级**: 中

#### 页面设计
```vue
<template>
  <view class="tag-levels-container">
    <!-- 标签信息头部 -->
    <view class="tag-header">
      <view class="tag-info">
        <text class="tag-name">{{ tagInfo.name }}</text>
        <text class="tag-desc">{{ tagInfo.description }}</text>
      </view>
      <view class="tag-stats">
        <text class="stats-text">共{{ levels.length }}关</text>
      </view>
    </view>

    <!-- 关卡列表 -->
    <view class="levels-list">
      <view 
        v-for="level in levels"
        :key="level.id"
        class="level-item"
        @click="selectLevel(level)"
      >
        <!-- 继承原有关卡卡片设计 -->
        <!-- 添加星级显示 -->
      </view>
    </view>
  </view>
</template>
```

#### 功能特性
- 继承星级系统显示
- 支持关卡筛选和排序
- 显示完成进度
- 支持返回和导航

## 功能模块2：关卡星级系统

### 2.1 首页星级展示

**开发时间**: 2-3天  
**优先级**: 高

#### 设计方案
```vue
<!-- 首页星级统计卡片 -->
<view class="star-stats-card">
  <view class="stats-header">
    <text class="stats-title">🌟 我的成就</text>
  </view>
  <view class="stats-content">
    <view class="star-item">
      <text class="star-icon">⭐</text>
      <text class="star-count">{{ userStats.totalStars }}</text>
      <text class="star-label">总星数</text>
    </view>
    <view class="star-item">
      <text class="star-icon">🏆</text>
      <text class="star-count">{{ userStats.threeStarLevels }}</text>
      <text class="star-label">三星关卡</text>
    </view>
  </view>
</view>
```

#### 数据统计
```typescript
interface UserStarStats {
  totalStars: number        // 总星数
  threeStarLevels: number   // 三星关卡数
  twoStarLevels: number     // 二星关卡数
  oneStarLevels: number     // 一星关卡数
  completedLevels: number   // 完成关卡数
}
```

### 2.2 关卡列表星级显示

**开发时间**: 2-3天  
**优先级**: 高

#### 显示逻辑
```vue
<!-- 关卡星级显示 -->
<view class="level-stars">
  <view v-if="level.completed" class="stars-display">
    <text 
      v-for="star in 3" 
      :key="star"
      class="star"
      :class="{ 'star-filled': star <= level.maxStars }"
    >
      ⭐
    </text>
  </view>
  <view v-else class="stars-placeholder">
    <text class="placeholder-text">未开始</text>
  </view>
</view>
```

#### 状态管理
- **未开始**: 显示灰色星星或"未开始"文字
- **已完成**: 显示实际获得的最高星级
- **可重玩**: 允许刷新星级记录

### 2.3 游戏计时器和星级评定

**开发时间**: 5-6天  
**优先级**: 高

#### 计时器UI设计
```vue
<view class="game-timer">
  <view class="timer-circle" :class="timerColorClass">
    <text class="timer-text">{{ formatTime(remainingTime) }}</text>
  </view>
  <view class="timer-progress">
    <view 
      class="progress-bar" 
      :style="{ width: progressWidth + '%' }"
    ></view>
  </view>
</view>
```

#### 星级评定规则
```javascript
const calculateStars = (completionTime, timeLimit) => {
  const ratio = completionTime / timeLimit
  
  if (ratio <= 0.5) return 3      // 50%以内时间完成 = 3星
  if (ratio <= 0.75) return 2     // 75%以内时间完成 = 2星
  if (ratio <= 1.0) return 1      // 时间内完成 = 1星
  return 0                        // 超时 = 0星
}
```

#### 超时处理
```javascript
const handleTimeout = () => {
  // 停止游戏
  gameState.isPlaying = false
  
  // 重置关卡
  resetLevel()
  
  // 显示超时提示
  uni.showModal({
    title: '时间到！',
    content: '超过时间限制，关卡已重置。再试一次吧！',
    showCancel: false,
    confirmText: '重新开始',
    success: () => {
      startLevel()
    }
  })
}
```

## 功能模块3：激活码系统

### 3.1 激活码输入界面

**开发时间**: 3-4天  
**优先级**: 中

#### 界面设计
```vue
<view class="activation-section">
  <view class="section-header">
    <text class="section-title">🎁 激活码兑换</text>
    <text class="section-desc">输入激活码获取会员权益</text>
  </view>
  
  <view class="input-group">
    <input 
      v-model="activationCode"
      class="code-input"
      placeholder="请输入8位激活码"
      maxlength="8"
      @blur="validateFormat"
    />
    <button class="paste-btn" @click="pasteCode">粘贴</button>
  </view>
  
  <view class="action-buttons">
    <button class="get-code-btn" @click="getActivationCode">
      {{ getCodeButtonText }}
    </button>
    <button 
      class="redeem-btn" 
      :disabled="!isValidCode"
      @click="redeemCode"
    >
      立即兑换
    </button>
  </view>
</view>
```

#### 验证逻辑
```javascript
const validateFormat = () => {
  const codePattern = /^[A-Z0-9]{8}$/
  isValidCode.value = codePattern.test(activationCode.value)
  
  if (activationCode.value && !isValidCode.value) {
    uni.showToast({
      title: '激活码格式不正确',
      icon: 'none'
    })
  }
}
```

### 3.2 激活码兑换逻辑

**开发时间**: 4-5天  
**优先级**: 中

#### 兑换流程
```javascript
const redeemCode = async () => {
  try {
    uni.showLoading({ title: '验证中...' })
    
    // 1. 验证激活码
    const result = await weixinApi.redeemActivationCode(activationCode.value)
    
    if (result.success) {
      // 2. 更新用户权益
      await refreshUserInfo()
      
      // 3. 显示成功提示
      uni.showModal({
        title: '兑换成功！',
        content: `恭喜您获得${result.package.name}！`,
        showCancel: false,
        confirmText: '太棒了'
      })
      
      // 4. 清空输入
      activationCode.value = ''
    } else {
      throw new Error(result.message)
    }
  } catch (error) {
    uni.showModal({
      title: '兑换失败',
      content: error.message || '激活码无效或已使用',
      showCancel: false,
      confirmText: '知道了'
    })
  } finally {
    uni.hideLoading()
  }
}
```

## 功能模块4：收藏功能

### 4.1 关卡收藏功能

**开发时间**: 3-4天  
**优先级**: 低

#### 结算页面收藏按钮
```vue
<view class="game-result-actions">
  <!-- 现有按钮 -->
  <button class="action-btn replay-btn" @click="replayLevel">
    <text class="btn-text">重新挑战</text>
  </button>
  
  <!-- 新增收藏按钮 -->
  <button 
    class="action-btn favorite-btn"
    :class="{ 'favorited': level.isFavorited }"
    @click="toggleFavorite"
  >
    <text class="btn-icon">{{ level.isFavorited ? '❤️' : '🤍' }}</text>
    <text class="btn-text">{{ level.isFavorited ? '已收藏' : '收藏' }}</text>
  </button>
</view>
```

#### 收藏状态管理
```javascript
const toggleFavorite = async () => {
  try {
    if (level.isFavorited) {
      await weixinApi.removeFavorite(level.id)
      level.isFavorited = false
      uni.showToast({ title: '取消收藏', icon: 'success' })
    } else {
      await weixinApi.addFavorite(level.id)
      level.isFavorited = true
      uni.showToast({ title: '收藏成功', icon: 'success' })
    }
  } catch (error) {
    uni.showToast({ title: '操作失败', icon: 'none' })
  }
}
```

### 4.2 首页收藏夹入口

**开发时间**: 3-4天  
**优先级**: 低

#### 首页按钮设计
```vue
<!-- 在现有底部按钮区域添加 -->
<view class="bottom-buttons">
  <!-- 现有按钮 -->
  <button class="bottom-btn settings-btn" @click="showSettings">
    <text class="btn-icon">⚙️</text>
    <text class="btn-text">设置</text>
  </button>
  
  <!-- 新增收藏夹按钮 -->
  <button class="bottom-btn favorites-btn" @click="openFavorites">
    <text class="btn-icon">❤️</text>
    <text class="btn-text">收藏夹</text>
  </button>
  
  <button class="bottom-btn member-btn" @click="goToMemberCenter">
    <text class="btn-icon">👑</text>
    <text class="btn-text">会员中心</text>
  </button>
</view>
```

#### 会员权限控制
```javascript
const openFavorites = () => {
  if (!dailyStatus.value?.isVip) {
    uni.showModal({
      title: '会员专享功能',
      content: '收藏为会员专享，解锁所有主题可享受更全面的功能！立即开通会员？',
      showCancel: true,
      cancelText: '稍后再说',
      confirmText: '去开通',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/member-center/index'
          })
        }
      }
    })
  } else {
    uni.navigateTo({
      url: '/pages/favorites/index'
    })
  }
}
```

## 📊 开发时间估算

| 功能模块 | 子功能 | 预计时间 | 优先级 |
|----------|--------|----------|--------|
| 标签闯关系统 | 1.1 关卡标签展示 | 2-3天 | 高 |
| | 1.2 标签选择闯关 | 3-4天 | 高 |
| | 1.3 标签关卡列表 | 4-5天 | 中 |
| 星级系统 | 2.1 首页星级展示 | 2-3天 | 高 |
| | 2.2 关卡列表星级 | 2-3天 | 高 |
| | 2.3 游戏计时星级 | 5-6天 | 高 |
| 激活码系统 | 3.1 激活码输入 | 3-4天 | 中 |
| | 3.2 兑换逻辑 | 4-5天 | 中 |
| 收藏功能 | 4.1 关卡收藏 | 3-4天 | 低 |
| | 4.2 收藏夹入口 | 3-4天 | 低 |

**总计**: 31-41天（约6-8周）

## 🎯 开发优先级建议

### 第一阶段（高优先级）- 2-3周
1. 星级系统完整实现
2. 关卡标签展示和选择功能

### 第二阶段（中优先级）- 2-3周  
1. 激活码系统
2. 标签关卡列表页面

### 第三阶段（低优先级）- 1-2周
1. 收藏功能完整实现

## 📝 总结

小程序二期开发将显著提升游戏的用户体验和商业价值：

1. **用户体验提升**: 标签系统和星级系统增加游戏的个性化和成就感
2. **商业价值增长**: 会员专享功能和激活码系统提供更多变现渠道  
3. **功能完善**: 收藏功能完善用户的个人化体验

建议按优先级分阶段开发，确保核心功能优先上线，逐步完善整体功能生态。
