"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_audio = require("../../utils/audio.js");
const composables_useGlobalConfig = require("../../composables/useGlobalConfig.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const playStatus = common_vendor.ref({
      isPlaying: false,
      isPaused: false,
      currentSrc: "",
      musicType: ""
    });
    const settings = common_vendor.ref({
      backgroundMusic: true,
      soundEffects: true,
      vibration: true
    });
    const {
      getBackgroundMusicUrl,
      initializeGlobalConfig
    } = composables_useGlobalConfig.useGlobalConfig();
    const globalMusicUrls = common_vendor.ref({
      main: "",
      game: "",
      menu: ""
    });
    let statusTimer = null;
    common_vendor.onMounted(async () => {
      await initializeGlobalConfig();
      loadSettings();
      updateGlobalMusicUrls();
      startStatusTimer();
    });
    common_vendor.onUnmounted(() => {
      if (statusTimer) {
        clearInterval(statusTimer);
      }
    });
    const loadSettings = () => {
      settings.value = utils_audio.audioManager.getSettings();
    };
    const updateGlobalMusicUrls = () => {
      globalMusicUrls.value = {
        main: getBackgroundMusicUrl("main"),
        game: getBackgroundMusicUrl("game"),
        menu: getBackgroundMusicUrl("menu")
      };
    };
    const startStatusTimer = () => {
      statusTimer = setInterval(() => {
        playStatus.value = utils_audio.audioManager.getPlayStatus();
      }, 500);
    };
    const playMainMusic = () => {
      utils_audio.audioManager.playBackgroundMusic("main");
    };
    const playGameMusic = () => {
      utils_audio.audioManager.playBackgroundMusic("game");
    };
    const playMenuMusic = () => {
      utils_audio.audioManager.playBackgroundMusic("menu");
    };
    const pauseMusic = () => {
      utils_audio.audioManager.pauseBackgroundMusic();
    };
    const resumeMusic = () => {
      utils_audio.audioManager.resumeBackgroundMusic();
    };
    const stopMusic = () => {
      utils_audio.audioManager.stopBackgroundMusic();
    };
    const playClickSound = () => {
      utils_audio.audioManager.playSoundEffect("click");
    };
    const playSuccessSound = () => {
      utils_audio.audioManager.playSoundEffect("success");
    };
    const playFailSound = () => {
      utils_audio.audioManager.playSoundEffect("fail");
    };
    const playUnlockSound = () => {
      utils_audio.audioManager.playSoundEffect("unlock");
    };
    const playCompleteSound = () => {
      utils_audio.audioManager.playSoundEffect("complete");
    };
    const shortVibrate = () => {
      utils_audio.audioManager.vibrate("short");
    };
    const longVibrate = () => {
      utils_audio.audioManager.vibrate("long");
    };
    const toggleBackgroundMusic = (event) => {
      const value = event.detail.value;
      utils_audio.audioManager.updateSettings({ backgroundMusic: value });
      loadSettings();
    };
    const toggleSoundEffects = (event) => {
      const value = event.detail.value;
      utils_audio.audioManager.updateSettings({ soundEffects: value });
      loadSettings();
    };
    const toggleVibration = (event) => {
      const value = event.detail.value;
      utils_audio.audioManager.updateSettings({ vibration: value });
      loadSettings();
    };
    const testGlobalConfig = () => {
      const mainUrl = getBackgroundMusicUrl("main");
      utils_audio.audioManager.playBackgroundMusic("main", mainUrl);
    };
    const refreshConfig = async () => {
      await initializeGlobalConfig();
      updateGlobalMusicUrls();
      common_vendor.index.showToast({
        title: "配置已刷新",
        icon: "success"
      });
    };
    const goBack = () => {
      common_vendor.index.navigateBack({
        delta: 1
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.t(playStatus.value.isPlaying ? "是" : "否"),
        b: common_vendor.t(playStatus.value.isPaused ? "是" : "否"),
        c: common_vendor.t(playStatus.value.musicType || "无"),
        d: common_vendor.t(playStatus.value.currentSrc || "无"),
        e: common_vendor.o(playMainMusic),
        f: common_vendor.o(playGameMusic),
        g: common_vendor.o(playMenuMusic),
        h: common_vendor.o(pauseMusic),
        i: common_vendor.o(resumeMusic),
        j: common_vendor.o(stopMusic),
        k: common_vendor.o(playClickSound),
        l: common_vendor.o(playSuccessSound),
        m: common_vendor.o(playFailSound),
        n: common_vendor.o(playUnlockSound),
        o: common_vendor.o(playCompleteSound),
        p: common_vendor.o(shortVibrate),
        q: common_vendor.o(longVibrate),
        r: settings.value.backgroundMusic,
        s: common_vendor.o(toggleBackgroundMusic),
        t: settings.value.soundEffects,
        v: common_vendor.o(toggleSoundEffects),
        w: settings.value.vibration,
        x: common_vendor.o(toggleVibration),
        y: common_vendor.t(globalMusicUrls.value.main),
        z: common_vendor.t(globalMusicUrls.value.game),
        A: common_vendor.t(globalMusicUrls.value.menu),
        B: common_vendor.o(testGlobalConfig),
        C: common_vendor.o(refreshConfig),
        D: common_vendor.o(goBack)
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-0a20da84"]]);
wx.createPage(MiniProgramPage);
