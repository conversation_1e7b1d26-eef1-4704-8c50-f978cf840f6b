<template>
  <view class="tag-selection-container">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">选择需要挑战的考点</text>
      <text class="page-subtitle">选择你想挑战的数学考点类型</text>
    </view>

    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载标签...</text>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="error" class="error-container">
      <view class="error-icon">⚠️</view>
      <text class="error-title">加载失败</text>
      <text class="error-text">{{ error }}</text>
      <button class="retry-btn" @click="loadTags">
        <text class="retry-text">重试</text>
      </button>
    </view>

    <!-- 空状态 -->
    <view v-else-if="tags.length === 0" class="empty-container">
      <view class="empty-icon">🏷️</view>
      <text class="empty-title">暂无标签</text>
      <text class="empty-text">当前没有可用的标签挑战</text>
    </view>

    <!-- 标签列表 -->
    <view v-else class="tags-list">
      <view class="list-header">
        <text class="list-title">共{{ tags.length }}个标签</text>
      </view>
      
      <view class="tags-grid">
        <view
          v-for="tag in tags"
          :key="tag.id"
          class="tag-card"
          :class="{
            'tag-vip': tag.isVip,
            'tag-inactive': !tag.isActive
          }"
          @click="selectTag(tag)"
        >
          <!-- 标签图标 -->
          <view class="tag-icon">
            <text class="icon-text">{{ tag.icon || '🏷️' }}</text>
            <text v-if="tag.isVip" class="vip-crown">👑</text>
          </view>
          
          <!-- 标签信息 -->
          <view class="tag-info">
            <text class="tag-name">{{ tag.name }}</text>
            <text class="tag-desc">{{ tag.description }}</text>
            
            <!-- 标签统计 -->
            <view class="tag-stats">
              <view class="stat-item">
                <text class="stat-label">关卡数</text>
                <text class="stat-value">{{ tag.levelCount || 0 }}</text>
              </view>
              <view class="stat-item">
                <text class="stat-label">难度</text>
                <text class="stat-value">{{ getDifficultyText(tag.difficulty) }}</text>
              </view>
            </view>
          </view>
          
          <!-- 标签状态 -->
          <view class="tag-status">
            <view v-if="!tag.isActive" class="status-badge inactive">
              <text class="status-text">未激活</text>
            </view>
            <view v-else-if="tag.isVip && !isVipUser" class="status-badge vip-only">
              <text class="status-text">VIP专享</text>
            </view>
            <view v-else class="status-badge available">
              <text class="status-text">开始挑战</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部返回按钮 -->
    <view class="bottom-actions">
      <button class="back-btn" @click="goBack">
        <text class="back-text">返回</text>
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import weixinApi from '../../api/weixin'
import { audioManager } from '../../utils/audio'
import { checkLoginAndRedirect } from '../../utils/auth'
import type { LevelTag, UserInfo } from '../../api/types'

// 状态管理
const isLoading = ref(false)
const error = ref<string | null>(null)
const tags = ref<LevelTag[]>([])
const userInfo = ref<UserInfo | null>(null)

// 计算属性
const isVipUser = computed(() => {
  return userInfo.value?.isVip || false
})

/**
 * 页面加载
 */
onLoad(async () => {
  // 检查登录状态
  const isLoggedIn = await checkLoginAndRedirect({
    toastMessage: '请先登录以查看标签挑战',
    redirectUrl: '/pages/login/index'
  })

  if (!isLoggedIn) {
    return
  }

  // 获取用户信息
  try {
    const storedUserInfo = uni.getStorageSync('userInfo')
    if (storedUserInfo) {
      userInfo.value = JSON.parse(storedUserInfo)
    }
  } catch (error) {
    console.warn('获取用户信息失败:', error)
  }

  await loadTags()
})

/**
 * 加载标签列表
 */
const loadTags = async () => {
  try {
    isLoading.value = true
    error.value = null

    const response = await weixinApi.getActiveTags()
    tags.value = response

    console.log('标签列表加载成功:', response)
  } catch (err) {
    console.error('加载标签列表失败:', err)
    error.value = '加载失败，请重试'
  } finally {
    isLoading.value = false
  }
}

/**
 * 选择标签
 */
const selectTag = async (tag: LevelTag) => {
  // 检查标签状态
  if (!tag.isActive) {
    // 播放失败音效
    audioManager.playSoundEffect('fail')
    
    uni.showToast({
      title: '该标签尚未激活',
      icon: 'none',
      duration: 1500
    })
    return
  }

  // 检查VIP权限
  if (tag.isVip && !isVipUser.value) {
    // 播放失败音效
    audioManager.playSoundEffect('fail')
    
    uni.showToast({
      title: '该标签需要VIP权限',
      icon: 'none',
      duration: 1500
    })
    return
  }

  // 播放点击音效
  audioManager.playSoundEffect('click')

  console.log('Selected tag:', tag)

  // 跳转到标签关卡页面
  uni.navigateTo({
    url: `/pages/tag-levels/index?tagId=${tag.id}`
  })
}

/**
 * 获取难度文本
 */
const getDifficultyText = (difficulty?: number): string => {
  if (!difficulty) return '普通'
  
  switch (difficulty) {
    case 1: return '简单'
    case 2: return '普通'
    case 3: return '困难'
    case 4: return '专家'
    case 5: return '大师'
    default: return '普通'
  }
}

/**
 * 返回上一页
 */
const goBack = () => {
  // 播放点击音效
  audioManager.playSoundEffect('click')
  
  uni.navigateBack()
}
</script>

<style scoped>
.tag-selection-container {
  min-height: 100vh;
  background: #fdf9f9;
  padding: 32rpx 24rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 48rpx;
  padding: 32rpx 24rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: #4a5568;
  font-weight: 500;
}

/* 加载和错误状态 */
.loading-container, .error-container, .empty-container {
  text-align: center;
  padding: 80rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text, .error-title, .error-text, .empty-title, .empty-text {
  color: #111;
  font-size: 28rpx;
  margin-bottom: 16rpx;
}

.error-icon, .empty-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.retry-btn {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: #ffffff;
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  border: none;
  font-size: 26rpx;
  box-shadow: 0 8rpx 20rpx rgba(116, 185, 255, 0.3);
}

.retry-text {
  color: inherit;
}

/* 标签列表 */
.tags-list {
  flex: 1;
}

.list-header {
  margin-bottom: 24rpx;
}

.list-title {
  color: #718096;
  font-size: 26rpx;
  font-weight: 500;
}

.tags-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  padding: 0 8rpx;
}

.tag-card {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 16rpx;
  padding: 20rpx 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(102, 126, 234, 0.1);
  transition: all 0.2s;
  position: relative;
}

.tag-card:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.tag-vip {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 179, 71, 0.1));
  border: 2rpx solid rgba(255, 215, 0, 0.3);
}

.tag-inactive {
  opacity: 0.6;
}

.tag-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex-shrink: 0;
}

.icon-text {
  font-size: 32rpx;
  color: #ffffff;
}

.vip-crown {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  font-size: 20rpx;
  background: #ffd700;
  border-radius: 50%;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tag-info {
  flex: 1;
}

/* 新的标签内容样式 */
.tag-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
}

.tag-content .tag-name {
  font-size: 24rpx;
  font-weight: 500;
  color: #1a202c;
  margin-bottom: 4rpx;
  line-height: 1.2;
}

.tag-count {
  font-size: 20rpx;
  color: #667eea;
  font-weight: 400;
}

/* VIP标识 */
.vip-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background: linear-gradient(135deg, #ffd700, #ffb347);
  border-radius: 8rpx;
  padding: 2rpx 6rpx;
}

.vip-text {
  font-size: 16rpx;
  color: #8b4513;
  font-weight: bold;
}

/* 保留原有样式以兼容 */
.tag-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #1a202c;
  margin-bottom: 8rpx;
  letter-spacing: 0.5rpx;
}

.tag-desc {
  display: block;
  font-size: 24rpx;
  color: #4a5568;
  margin-bottom: 12rpx;
  font-weight: 400;
}

.tag-stats {
  display: flex;
  gap: 24rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 20rpx;
  color: #a0aec0;
  margin-bottom: 4rpx;
}

.stat-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #4a5568;
}

/* 标签状态样式 */
.tag-status {
  flex-shrink: 0;
}

.status-badge {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  text-align: center;
}

.status-badge.inactive {
  background: #f0f0f0;
}

.status-badge.vip-only {
  background: linear-gradient(135deg, #ffd700, #ffb347);
}

.status-badge.available {
  background: #e3f2fd;
}

.status-text {
  font-size: 22rpx;
  font-weight: 500;
}

/* 底部按钮 */
.bottom-actions {
  display: flex;
  justify-content: center;
  padding-top: 32rpx;
}

.back-btn {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: #ffffff;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
  box-shadow: 0 8rpx 20rpx rgba(116, 185, 255, 0.3);
}

.back-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.back-text {
  color: inherit;
}
</style>
