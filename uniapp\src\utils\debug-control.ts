/**
 * 调试功能控制工具
 * 统一管理所有调试相关功能的显示和行为
 * 只在 development 环境下启用
 */

import { getCurrentEnvironment } from './env'

/**
 * 检查是否应该显示调试功能
 * 严格限制只在 development 环境下显示
 */
export function shouldShowDebugFeatures(): boolean {
  const currentEnv = getCurrentEnvironment()
  
  // 只有在 development 环境下才显示调试功能
  const isDebugEnabled = currentEnv === 'development'
  
  // #ifdef MP-WEIXIN
  // 微信小程序还需要检查是否在开发工具中
  try {
    const accountInfo = uni.getAccountInfoSync()
    const envVersion = accountInfo.miniProgram.envVersion
    const isInDevTool = envVersion === 'develop'
    return isDebugEnabled && isInDevTool
  } catch (error) {
    // 只在开发环境下输出警告
    if (isDebugEnabled) {
      console.warn('获取微信小程序环境信息失败:', error)
    }
    return false
  }
  // #endif
  
  // #ifdef H5
  // H5环境下只检查构建环境
  return isDebugEnabled
  // #endif
  
  // #ifdef APP-PLUS
  // App环境下检查调试模式
  try {
    return isDebugEnabled && plus.runtime.isDebugMode
  } catch (error) {
    return isDebugEnabled
  }
  // #endif
  
  // 其他平台默认只检查构建环境
  return isDebugEnabled
}

/**
 * 检查是否应该显示调试日志
 */
export function shouldShowDebugLogs(): boolean {
  return shouldShowDebugFeatures()
}

/**
 * 检查是否应该显示调试按钮
 */
export function shouldShowDebugButtons(): boolean {
  return shouldShowDebugFeatures()
}

/**
 * 检查是否应该显示调试页面
 */
export function shouldShowDebugPages(): boolean {
  return shouldShowDebugFeatures()
}

/**
 * 检查是否应该启用性能监控
 */
export function shouldEnablePerformanceMonitoring(): boolean {
  return shouldShowDebugFeatures()
}

/**
 * 条件性调试日志输出
 * 只在允许调试的环境下输出
 */
export function debugLog(message: string, ...args: any[]): void {
  if (shouldShowDebugLogs()) {
    console.log(`[DEBUG] ${message}`, ...args)
  }
}

/**
 * 条件性调试错误输出
 */
export function debugError(message: string, ...args: any[]): void {
  if (shouldShowDebugLogs()) {
    console.error(`[DEBUG ERROR] ${message}`, ...args)
  }
}

/**
 * 条件性调试警告输出
 */
export function debugWarn(message: string, ...args: any[]): void {
  if (shouldShowDebugLogs()) {
    console.warn(`[DEBUG WARN] ${message}`, ...args)
  }
}

/**
 * 条件性性能测试
 */
export async function debugPerformance<T>(
  label: string, 
  fn: () => Promise<T> | T
): Promise<T> {
  if (!shouldEnablePerformanceMonitoring()) {
    return await fn()
  }
  
  const startTime = performance.now()
  if (shouldShowDebugLogs()) {
    console.time(`[PERF] ${label}`)
  }

  try {
    const result = await fn()
    const endTime = performance.now()
    if (shouldShowDebugLogs()) {
      console.timeEnd(`[PERF] ${label}`)
      console.log(`[PERF] ${label} 执行时间: ${(endTime - startTime).toFixed(2)}ms`)
    }
    return result
  } catch (error) {
    const endTime = performance.now()
    if (shouldShowDebugLogs()) {
      console.timeEnd(`[PERF] ${label}`)
      console.error(`[PERF ERROR] ${label} 执行失败，耗时: ${(endTime - startTime).toFixed(2)}ms`, error)
    }
    throw error
  }
}

/**
 * 获取调试环境信息
 */
export function getDebugEnvironmentInfo(): {
  environment: string
  shouldShowDebug: boolean
  platform: string
  details: any
} {
  const currentEnv = getCurrentEnvironment()
  const shouldShow = shouldShowDebugFeatures()
  
  let platform = 'unknown'
  let details: any = {}
  
  // #ifdef MP-WEIXIN
  platform = 'mp-weixin'
  try {
    const accountInfo = uni.getAccountInfoSync()
    details = {
      envVersion: accountInfo.miniProgram.envVersion,
      version: accountInfo.miniProgram.version
    }
  } catch (error) {
    details = { error: 'Failed to get account info' }
  }
  // #endif
  
  // #ifdef H5
  platform = 'h5'
  if (typeof location !== 'undefined') {
    details = {
      hostname: location.hostname,
      port: location.port,
      protocol: location.protocol
    }
  }
  // #endif
  
  // #ifdef APP-PLUS
  platform = 'app-plus'
  try {
    details = {
      isDebugMode: plus.runtime.isDebugMode,
      version: plus.runtime.version
    }
  } catch (error) {
    details = { error: 'Failed to get runtime info' }
  }
  // #endif
  
  return {
    environment: currentEnv,
    shouldShowDebug: shouldShow,
    platform,
    details
  }
}

/**
 * 在控制台输出调试环境信息
 */
export function logDebugEnvironmentInfo(): void {
  if (!shouldShowDebugLogs()) {
    return
  }
  
  const info = getDebugEnvironmentInfo()
  if (shouldShowDebugLogs()) {
    console.log('🔧 调试环境信息:')
    console.log(`  环境: ${info.environment}`)
    console.log(`  平台: ${info.platform}`)
    console.log(`  调试功能: ${info.shouldShowDebug ? '启用' : '禁用'}`)
    console.log(`  详细信息:`, info.details)
  }
}
