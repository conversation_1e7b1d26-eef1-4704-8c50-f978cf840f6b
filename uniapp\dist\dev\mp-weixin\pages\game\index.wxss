
.page-box.data-v-131fc1ab {
    height: calc(100vh - 44px);
    background-color: #fdf9f9;
}
.game-page-container.data-v-131fc1ab {
    padding: 16rpx;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}
.card.data-v-131fc1ab {
    /* Common card style */
    background-color: white;
    border-radius: 24rpx; /* rounded-xl or 2xl */
    padding: 16rpx;
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}
.library-name.data-v-131fc1ab {
    display: block;
    font-size: 36rpx; /* text-xl */
    font-weight: 600; /* font-semibold */
    color: #111; /* text-purple-700 */
    margin-bottom: 8rpx;
}

  /* 删除了关卡选择相关的样式 */
.game-area.data-v-131fc1ab {
    /* padding already from .card */
    margin-top: 32rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}
.game-info-bar.data-v-131fc1ab {
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 100%;
    padding: 10rpx 0;
    margin-bottom: 20rpx;
    font-size: 28rpx;
    color: #333;
}
.sync-status.data-v-131fc1ab {
    font-size: 24rpx !important;
    color: #74b9ff !important;
    font-weight: 500;
}

  /* H5环境提示样式 */
.h5-mock-tip.data-v-131fc1ab {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 8rpx 16rpx;
    border-radius: 12rpx;
    font-size: 20rpx;
    font-weight: bold;
    box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
    animation: h5Glow-131fc1ab 2s ease-in-out infinite alternate;
}
@keyframes h5Glow-131fc1ab {
0% { box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}
100% { box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.6);
}
}
.replay-btn.data-v-131fc1ab {
    background: linear-gradient(135deg, #fd79a8, #e84393);
    color: white;
    padding: 8rpx 16rpx;
    border-radius: 16rpx;
    font-size: 24rpx;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 60rpx;
    text-align: center;
    box-shadow: 0 2rpx 8rpx rgba(232, 67, 147, 0.3);
}
.replay-btn.data-v-131fc1ab:hover {
    transform: scale(1.05);
    box-shadow: 0 4rpx 12rpx rgba(232, 67, 147, 0.4);
}
.replay-btn-text.data-v-131fc1ab {
    color: white;
    font-size: 24rpx;
    font-weight: 500;
}
.replay-btn-disabled.data-v-131fc1ab {
    opacity: 0.5;
    cursor: not-allowed;
}
.replay-btn-disabled.data-v-131fc1ab:hover {
    transform: none;
    box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}
.debug-btn.data-v-131fc1ab {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    padding: 8rpx 16rpx;
    border-radius: 16rpx;
    font-size: 20rpx;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 60rpx;
    text-align: center;
    box-shadow: 0 2rpx 8rpx rgba(108, 117, 125, 0.3);
}
.debug-btn.data-v-131fc1ab:hover {
    transform: scale(1.05);
    box-shadow: 0 4rpx 12rpx rgba(108, 117, 125, 0.4);
}
.debug-btn-text.data-v-131fc1ab {
    color: white;
    font-size: 20rpx;
    font-weight: 500;
}

  /* 网格调试按钮样式 */
.grid-debug-btn.data-v-131fc1ab {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 12rpx 20rpx;
    border-radius: 20rpx;
    font-size: 20rpx;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2rpx 8rpx rgba(40, 167, 69, 0.3);
    margin-left: 10rpx;
}
.grid-debug-btn.data-v-131fc1ab:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 4rpx 12rpx rgba(40, 167, 69, 0.4);
}
.grid-debug-btn-text.data-v-131fc1ab {
    color: white;
    font-size: 20rpx;
    font-weight: 500;
}

  /* 网格线样式 */
.grid-overlay.data-v-131fc1ab {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}
.grid-line.data-v-131fc1ab {
    display: flex;
    align-items: center;
    justify-content: center;
}
.grid-label.data-v-131fc1ab {
    font-size: 20rpx;
    color: rgba(255, 0, 0, 0.8);
    font-weight: bold;
    text-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.8);
}
.game-board.data-v-131fc1ab {
    flex: 1;
    width: 100%;
    max-width: 750rpx;
    min-height: 800rpx; /* 增加最小高度，确保有足够空间 */
    height: auto;
    box-sizing: border-box;
    position: relative;
    margin: 0 auto 20rpx;
    padding: 25rpx; /* 增加内边距 */
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20rpx;
    overflow: visible; /* 改为visible，避免裁剪卡片 */
    box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    border: 2rpx solid rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

  /* 检查匹配状态的样式 */
.game-board.checking-match.data-v-131fc1ab {
    opacity: 0.8;
    pointer-events: none; /* 禁用所有点击事件 */
}
.game-board.checking-match.data-v-131fc1ab::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    z-index: 10;
    animation: checkingPulse-131fc1ab 0.5s ease-in-out infinite alternate;
    border-radius: 20rpx;
}
@keyframes checkingPulse-131fc1ab {
0% { opacity: 0.1;
}
100% { opacity: 0.3;
}
}

  /* 加载状态样式 */
.loading-container.data-v-131fc1ab {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 750rpx;
    min-height: 800rpx;
    height: auto;
    box-sizing: border-box;
    margin: 0 auto 20rpx;
    padding: 25rpx;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20rpx;
    box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.loading-spinner.data-v-131fc1ab {
    width: 60rpx;
    height: 60rpx;
    border: 6rpx solid rgba(0, 0, 0, 0.1);
    border-top: 6rpx solid #007AFF;
    border-radius: 50%;
    animation: spin-131fc1ab 1s linear infinite;
    margin-bottom: 30rpx;
}
@keyframes spin-131fc1ab {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.loading-text.data-v-131fc1ab {
    color: #666;
    font-size: 32rpx;
    text-align: center;
    font-weight: 500;
}

  /* 错误状态样式 */
.error-container.data-v-131fc1ab {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 750rpx;
    min-height: 800rpx;
    height: auto;
    box-sizing: border-box;
    margin: 0 auto 20rpx;
    padding: 25rpx;
    background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
    border-radius: 20rpx;
    box-shadow: inset 0 2rpx 8rpx rgba(255, 0, 0, 0.1);
}
.error-text.data-v-131fc1ab {
    color: #e53e3e;
    font-size: 32rpx;
    text-align: center;
    font-weight: 500;
    margin-bottom: 40rpx;
}
.retry-btn.data-v-131fc1ab {
    background: linear-gradient(135deg, #e53e3e, #c53030);
    color: white;
    padding: 20rpx 40rpx;
    border-radius: 25rpx;
    font-size: 28rpx;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4rpx 12rpx rgba(229, 62, 62, 0.3);
}
.retry-btn.data-v-131fc1ab:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 6rpx 16rpx rgba(229, 62, 62, 0.4);
}
.retry-btn-text.data-v-131fc1ab {
    color: white;
    font-size: 28rpx;
    font-weight: 500;
}

  /* 游戏加载状态样式 */
.game-loading-container.data-v-131fc1ab {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 750rpx;
    min-height: 800rpx;
    height: auto;
    box-sizing: border-box;
    margin: 0 auto 20rpx;
    padding: 50rpx 25rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20rpx;
    box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}
.game-loading-container.data-v-131fc1ab::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.1) 75%);
    background-size: 40rpx 40rpx;
    animation: moveBackground-131fc1ab 2s linear infinite;
}
@keyframes moveBackground-131fc1ab {
0% { transform: translateX(0);
}
100% { transform: translateX(40rpx);
}
}
.game-loading-content.data-v-131fc1ab {
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 1;
    position: relative;
}
.game-loading-spinner.data-v-131fc1ab {
    position: relative;
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 40rpx;
}
.spinner-ring.data-v-131fc1ab {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 6rpx solid transparent;
    border-radius: 50%;
    animation: spinRing-131fc1ab 2s linear infinite;
}
.spinner-ring.data-v-131fc1ab:nth-child(1) {
    border-top-color: #ffffff;
    animation-delay: 0s;
}
.spinner-ring.data-v-131fc1ab:nth-child(2) {
    border-right-color: #ffffff;
    animation-delay: 0.5s;
    width: 80%;
    height: 80%;
    top: 10%;
    left: 10%;
}
.spinner-ring.data-v-131fc1ab:nth-child(3) {
    border-bottom-color: #ffffff;
    animation-delay: 1s;
    width: 60%;
    height: 60%;
    top: 20%;
    left: 20%;
}
@keyframes spinRing-131fc1ab {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.game-loading-title.data-v-131fc1ab {
    color: white;
    font-size: 40rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 20rpx;
    text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}
.game-loading-subtitle.data-v-131fc1ab {
    color: rgba(255, 255, 255, 0.9);
    font-size: 28rpx;
    text-align: center;
    margin-bottom: 60rpx;
    text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.2);
}
.loading-progress.data-v-131fc1ab {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 400rpx;
}
.progress-bar.data-v-131fc1ab {
    width: 100%;
    height: 8rpx;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4rpx;
    overflow: hidden;
    margin-bottom: 20rpx;
}
.progress-fill.data-v-131fc1ab {
    height: 100%;
    background: linear-gradient(90deg, #ffffff, #f0f8ff);
    border-radius: 4rpx;
    transition: width 0.3s ease-out;
    box-shadow: 0 0 10rpx rgba(255, 255, 255, 0.5);
    position: relative;
    overflow: hidden;
}
.progress-fill.data-v-131fc1ab::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: progressShine-131fc1ab 2s ease-in-out infinite;
}
@keyframes progressShine-131fc1ab {
0% { transform: translateX(-100%);
}
100% { transform: translateX(100%);
}
}
.progress-text.data-v-131fc1ab {
    color: rgba(255, 255, 255, 0.8);
    font-size: 24rpx;
    text-align: center;
}
.board-tile.data-v-131fc1ab {
    border-radius: 16rpx;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.15), 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
    border: 2rpx solid rgba(255, 255, 255, 0.8);
    padding: 12rpx 8rpx;
    -webkit-backdrop-filter: blur(10rpx);
            backdrop-filter: blur(10rpx);
    position: absolute;
    z-index: 2; /* 确保卡片在网格线之上 */
    width: 160rpx;
    height: 35rpx;
    line-height: 35rpx;
}
.board-tile.data-v-131fc1ab:hover {
    transform: scale(1.05) translateZ(0);
    box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.2), 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
    z-index: 2;
}
.board-tile.selected.data-v-131fc1ab {
    transform: scale(1.08) translateZ(0);
    border-color: #ff6b35;
    box-shadow: 0 12rpx 24rpx rgba(255, 107, 53, 0.4), 0 6rpx 12rpx rgba(255, 107, 53, 0.2);
    z-index: 3;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 107, 53, 0.1));
}
.board-tile.matched.data-v-131fc1ab {
    opacity: 0.7;
    transform: scale(0.95) translateZ(0);
    border-color: #28a745;
    box-shadow: 0 4rpx 12rpx rgba(40, 167, 69, 0.3);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(40, 167, 69, 0.1));
    z-index: 0;
}

  /* 调试模式样式 */
.board-tile.debug-mode.data-v-131fc1ab {
    border: 3rpx dashed #dc3545 !important;
    background: rgba(220, 53, 69, 0.1) !important;
}
.board-tile.debug-mode.data-v-131fc1ab::before {
    content: attr(data-position);
    position: absolute;
    top: -25rpx;
    left: 0;
    font-size: 16rpx;
    color: #dc3545;
    background: rgba(255, 255, 255, 0.9);
    padding: 2rpx 6rpx;
    border-radius: 8rpx;
    white-space: nowrap;
    z-index: 10;
}
.tile-word.data-v-131fc1ab {
    font-weight: bold;
    color: #333;
    margin-bottom: 6rpx;
    text-align: center;
    line-height: 1.2;
    word-break: break-word;
}

  /* 根据卡片大小调整英文字体大小 */
.tile-short .tile-word.data-v-131fc1ab {
    font-size: 24rpx;
}
.tile-medium .tile-word.data-v-131fc1ab {
    font-size: 22rpx;
}
.tile-long .tile-word.data-v-131fc1ab {
    font-size: 20rpx;
}
.tile-chinese.data-v-131fc1ab {
    color: #666;
    text-align: center;
    line-height: 1.2;
    word-break: break-word;
}

  /* 中文卡片专用样式 */
.tile-chinese-only.data-v-131fc1ab {
    font-weight: bold;
    color: #333;
    text-align: center;
    line-height: 1.2;
    word-break: break-word;
    font-size: 28rpx;
}



  /* 根据卡片大小调整中文字体大小 */
.tile-short .tile-chinese.data-v-131fc1ab {
    font-size: 20rpx;
}
.tile-medium .tile-chinese.data-v-131fc1ab {
    font-size: 18rpx;
}
.tile-long .tile-chinese.data-v-131fc1ab {
    font-size: 16rpx;
}

  /* 中文卡片字体大小调整 */
.tile-short .tile-chinese-only.data-v-131fc1ab {
    font-size: 26rpx;
}
.tile-medium .tile-chinese-only.data-v-131fc1ab {
    font-size: 24rpx;
}
.tile-long .tile-chinese-only.data-v-131fc1ab {
    font-size: 22rpx;
}
.game-controls.data-v-131fc1ab {
    display: flex;
    justify-content: center;
    gap: 24rpx;
    width: 100%;
    margin-top: 20rpx;
}
.control-button.data-v-131fc1ab {
    padding: 12rpx 24rpx;
    font-size: 24rpx;
    border-radius: 20rpx;
    border: none;
    background-color: #007aff;
    color: white;
    transition: background-color 0.3s ease;
    min-width: 120rpx;
}
.control-button.data-v-131fc1ab:disabled {
    background-color: #ccc;
    color: #999;
}
.control-button.data-v-131fc1ab:hover:not(:disabled) {
    background-color: #0056b3;
}
.game-start-text.data-v-131fc1ab {
    display: block;
    font-size: 36rpx; /* text-xl */
    font-weight: bold;
}
  /* Modal Styles (simplified) */
.modal-overlay.data-v-131fc1ab {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}
.modal-content.data-v-131fc1ab {
    background-color: white;
    padding: 40rpx;
    border-radius: 16rpx;
    text-align: center;
    min-width: 500rpx;
}
.modal-title.data-v-131fc1ab {
    font-size: 36rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    display: block;
}

  /* 游戏完成信息样式 */
.game-completion-info.data-v-131fc1ab {
    margin: 24rpx 0;
    padding: 24rpx;
    background: linear-gradient(135deg, #fff9e6, #fff3cd);
    border-radius: 16rpx;
    border: 2rpx solid rgba(255, 193, 7, 0.2);
}
.stars-display.data-v-131fc1ab {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20rpx;
}
.stars-label.data-v-131fc1ab {
    font-size: 24rpx;
    color: #856404;
    margin-bottom: 12rpx;
    font-weight: 500;
}
.stars-container.data-v-131fc1ab {
    display: flex;
    gap: 8rpx;
}
.star-icon.data-v-131fc1ab {
    font-size: 32rpx;
    color: #ddd;
    transition: color 0.3s;
}
.star-filled.data-v-131fc1ab {
    color: #ffd700;
}
.completion-time.data-v-131fc1ab {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12rpx 0;
    border-top: 1rpx solid rgba(255, 193, 7, 0.3);
}
.time-label.data-v-131fc1ab {
    font-size: 24rpx;
    color: #856404;
    font-weight: 500;
}
.time-value.data-v-131fc1ab {
    font-size: 28rpx;
    color: #6c5ce7;
    font-weight: bold;
}

  /* 游戏计时器样式 */
.game-timer.data-v-131fc1ab {
    color: #6c5ce7;
    font-weight: 500;
    transition: all 0.3s ease;
}

  /* 时间警告样式（剩余10秒时） */
.game-timer.time-warning.data-v-131fc1ab {
    color: #e74c3c;
    font-weight: bold;
    animation: timeWarning-131fc1ab 1s infinite;
}
@keyframes timeWarning-131fc1ab {
0%, 100% { opacity: 1;
}
50% { opacity: 0.6;
}
}

  /* 收藏功能样式 */
.favorite-section.data-v-131fc1ab {
    margin-top: 20rpx;
    padding-top: 20rpx;
    border-top: 1rpx solid rgba(255, 193, 7, 0.3);
}
.favorite-btn.data-v-131fc1ab {
    width: 100%;
    height: 80rpx;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 2rpx solid #dee2e6;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    transition: all 0.3s;
}
.favorite-btn.favorited.data-v-131fc1ab {
    background: linear-gradient(135deg, #ffe6f0, #ffd6e7);
    border-color: #ff69b4;
}
.favorite-btn.data-v-131fc1ab:active {
    transform: scale(0.98);
    opacity: 0.9;
}
.favorite-icon.data-v-131fc1ab {
    font-size: 28rpx;
    transition: all 0.3s;
}
.favorite-text.data-v-131fc1ab {
    font-size: 26rpx;
    color: #495057;
    font-weight: 500;
}
.favorite-btn.favorited .favorite-text.data-v-131fc1ab {
    color: #d63384;
}
.favorite-btn.favorited .favorite-icon.data-v-131fc1ab {
    animation: heartbeat-131fc1ab 0.6s ease-in-out;
}
@keyframes heartbeat-131fc1ab {
0% { transform: scale(1);
}
50% { transform: scale(1.2);
}
100% { transform: scale(1);
}
}
.modal-buttons.data-v-131fc1ab {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    margin-top: 32rpx;
}
.modal-button.data-v-131fc1ab {
    padding: 16rpx 32rpx;
    font-size: 28rpx;
    border-radius: 24rpx;
    border: none;
    background-color: #f0f0f0;
    color: #333;
    transition: background-color 0.3s ease;
}
.modal-button.primary.data-v-131fc1ab {
    background-color: #007aff;
    color: white;
}
.modal-button.data-v-131fc1ab:hover {
    opacity: 0.8;
}

  /* 关卡详情信息样式 */
.level-detail-info.data-v-131fc1ab {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 24rpx;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
}
.level-header.data-v-131fc1ab {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12rpx;
}
.level-name.data-v-131fc1ab {
    font-size: 32rpx;
    font-weight: bold;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.level-difficulty.data-v-131fc1ab {
    font-size: 24rpx;
    background: rgba(255, 255, 255, 0.2);
    padding: 6rpx 12rpx;
    border-radius: 12rpx;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}
.level-description.data-v-131fc1ab {
    font-size: 26rpx;
    opacity: 0.9;
    margin-bottom: 16rpx;
    line-height: 1.4;
}
.level-stats.data-v-131fc1ab {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.stats-item.data-v-131fc1ab {
    font-size: 22rpx;
    background: rgba(255, 255, 255, 0.15);
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

  /* 备用：词库信息样式 */
.selected-library-info.data-v-131fc1ab {
    color: white;
    padding: 20rpx;
    border-radius: 15rpx;
    margin-bottom: 20rpx;
    text-align: center;
}
.library-name.data-v-131fc1ab {
    font-size: 32rpx;
    font-weight: bold;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

  /* 右上角设置按钮样式 */
.floating-settings-btn.data-v-131fc1ab {
     width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 998;
    transition: all 0.3s ease;
    position: absolute;
    top: 20rpx;
    right: 20rpx;
}
.floating-settings-btn.data-v-131fc1ab:active {
    transform: scale(0.9);
}
.settings-icon.data-v-131fc1ab {
    font-size: 32rpx;
    color: #ffffff;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

  /* 悬浮调试按钮样式 */
.floating-debug-btn.data-v-131fc1ab {
    position: fixed;
    bottom: 100rpx;
    right: 40rpx;
    width: 100rpx;
    height: 100rpx;
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 20rpx rgba(255, 107, 107, 0.4);
    z-index: 999;
    transition: all 0.3s ease;
    animation: debugPulse-131fc1ab 2s infinite;
}
.floating-debug-btn.data-v-131fc1ab:active {
    transform: scale(0.9);
    box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.6);
}
.debug-icon.data-v-131fc1ab {
    font-size: 36rpx;
    color: #ffffff;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}
@keyframes debugPulse-131fc1ab {
0%, 100% {
      transform: scale(1);
      box-shadow: 0 8rpx 20rpx rgba(255, 107, 107, 0.4);
}
50% {
      transform: scale(1.05);
      box-shadow: 0 12rpx 24rpx rgba(255, 107, 107, 0.6);
}
}
