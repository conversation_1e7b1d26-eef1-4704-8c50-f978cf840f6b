/**
 * 背景音乐管理工具类
 * 基于uniapp官方音频API实现
 * 文档：https://uniapp.dcloud.net.cn/api/media/audio-context.html
 */

import type { GameSettings } from '../api/types'

// 内部音频上下文
let innerAudioContext: UniApp.InnerAudioContext | null = null
let currentAudioSrc: string = ''
let isPlaying: boolean = false
let isPaused: boolean = false
let currentMusicType: string = ''

// 默认背景音乐文件路径
const DEFAULT_BACKGROUND_MUSIC = {
  main: '/static/audio/background-main.mp3',      // 主背景音乐
  game: '/static/audio/background-game.mp3',     // 游戏背景音乐
  menu: '/static/audio/background-menu.mp3'      // 菜单背景音乐
}

// 音效文件路径
const SOUND_EFFECTS = {
  click: '/static/audio/click.mp3',               // 点击音效
  success: '/static/audio/success.mp3',          // 成功音效
  fail: '/static/audio/fail.mp3',                // 失败音效
  unlock: '/static/audio/unlock.mp3',            // 解锁音效
  complete: '/static/audio/complete.mp3'         // 完成音效
}

// 音频错误码映射
const AUDIO_ERROR_CODES: Record<string, string> = {
  '10001': '系统错误',
  '10002': '网络错误',
  '10003': '文件错误',
  '10004': '格式错误',
  '-1': '未知错误',
  '-99': '音频实例冲突错误'
}

// 音效实例缓存
const soundEffectInstances: Map<string, UniApp.InnerAudioContext> = new Map()

/**
 * 音频管理器类
 */
class AudioManager {
  private static instance: AudioManager
  private settings: GameSettings = {
    backgroundMusic: true,
    soundEffects: true,
    vibration: true
  }

  private constructor() {
    this.loadSettings()
  }

  /**
   * 获取单例实例
   */
  static getInstance(): AudioManager {
    if (!AudioManager.instance) {
      AudioManager.instance = new AudioManager()
    }
    return AudioManager.instance
  }

  /**
   * 加载设置
   */
  private loadSettings(): void {
    try {
      const savedSettings = uni.getStorageSync('gameSettings')
      if (savedSettings) {
        this.settings = { ...this.settings, ...JSON.parse(savedSettings) }
      }
      console.log('音频设置加载成功:', this.settings)
    } catch (error) {
      console.error('加载音频设置失败:', error)
    }
  }

  /**
   * 保存设置
   */
  private saveSettings(): void {
    try {
      uni.setStorageSync('gameSettings', JSON.stringify(this.settings))
      console.log('音频设置保存成功:', this.settings)
    } catch (error) {
      console.error('保存音频设置失败:', error)
    }
  }

  /**
   * 获取当前设置
   */
  getSettings(): GameSettings {
    return { ...this.settings }
  }

  /**
   * 更新设置
   */
  updateSettings(newSettings: Partial<GameSettings>): void {
    this.settings = { ...this.settings, ...newSettings }
    this.saveSettings()

    // 如果关闭背景音乐，停止当前播放
    if (!this.settings.backgroundMusic && isPlaying) {
      this.stopBackgroundMusic()
    }

    console.log('音频设置已更新:', this.settings)
  }

  /**
   * 创建内部音频上下文
   */
  private createInnerAudioContext(src: string): UniApp.InnerAudioContext {
    // 如果已有相同音频源的实例，直接返回
    if (innerAudioContext && currentAudioSrc === src) {
      return innerAudioContext
    }

    // 销毁旧的音频上下文
    this.destroyInnerAudioContext()

    // 创建新的内部音频上下文
    innerAudioContext = uni.createInnerAudioContext()
    currentAudioSrc = src

    // 设置音频属性
    innerAudioContext.src = src
    innerAudioContext.loop = true
    innerAudioContext.autoplay = false
    innerAudioContext.volume = 0.5
    innerAudioContext.obeyMuteSwitch = true

    // 设置事件监听
    this.setupAudioEventListeners(innerAudioContext)

    console.log('创建内部音频上下文:', src)
    return innerAudioContext
  }

  /**
   * 设置音频事件监听器
   */
  private setupAudioEventListeners(audioContext: UniApp.InnerAudioContext): void {
    // 音频可以播放时
    audioContext.onCanplay(() => {
      console.log('音频可以播放:', currentAudioSrc)
    })

    // 音频开始播放
    audioContext.onPlay(() => {
      isPlaying = true
      isPaused = false
      console.log('背景音乐开始播放:', currentAudioSrc)
    })

    // 音频暂停
    audioContext.onPause(() => {
      isPaused = true
      console.log('背景音乐暂停:', currentAudioSrc)
    })

    // 音频停止
    audioContext.onStop(() => {
      isPlaying = false
      isPaused = false
      console.log('背景音乐停止:', currentAudioSrc)
    })

    // 音频播放结束
    audioContext.onEnded(() => {
      isPlaying = false
      isPaused = false
      console.log('背景音乐播放结束:', currentAudioSrc)
    })

    // 音频播放错误
    audioContext.onError((error) => {
      const errorCode = error.errCode || error.code || 'unknown'
      const errorMsg = AUDIO_ERROR_CODES[errorCode] || '未知错误'
      console.error('背景音乐播放错误:', {
        code: errorCode,
        message: errorMsg,
        src: currentAudioSrc,
        error
      })

      isPlaying = false
      isPaused = false

      // 处理特定错误
      if (errorCode === -99) {
        console.warn('检测到音频实例冲突，尝试重新创建')
        setTimeout(() => {
          this.destroyInnerAudioContext()
        }, 100)
      }
    })

    // 音频加载中
    audioContext.onWaiting(() => {
      console.log('音频加载中:', currentAudioSrc)
    })

    // 音频进度更新
    audioContext.onTimeUpdate(() => {
      // 可以在这里处理播放进度
    })
  }

  /**
   * 销毁内部音频上下文
   */
  private destroyInnerAudioContext(): void {
    if (innerAudioContext) {
      try {
        // 停止并销毁（不需要手动移除事件监听器，destroy会自动处理）
        innerAudioContext.stop()
        innerAudioContext.destroy()
        console.log('销毁音频上下文:', currentAudioSrc)
      } catch (error) {
        console.error('销毁音频上下文失败:', error)
      }

      innerAudioContext = null
      currentAudioSrc = ''
      isPlaying = false
      isPaused = false
      currentMusicType = ''
    }
  }

  /**
   * 播放背景音乐
   */
  playBackgroundMusic(musicType: string = 'main', customUrl?: string): void {
    if (!this.settings.backgroundMusic) {
      console.log('背景音乐已关闭，跳过播放')
      return
    }

    try {
      // 使用自定义URL或默认音乐
      const src = customUrl || (DEFAULT_BACKGROUND_MUSIC as any)[musicType] || DEFAULT_BACKGROUND_MUSIC.main

      // 如果当前正在播放相同音乐，不重复播放
      if (isPlaying && currentAudioSrc === src) {
        console.log('相同背景音乐正在播放中:', musicType)
        return
      }

      // 如果是暂停状态且是相同音乐，恢复播放
      if (isPaused && currentAudioSrc === src && innerAudioContext) {
        innerAudioContext.play()
        currentMusicType = musicType
        return
      }

      // 创建新的音频上下文并播放
      const context = this.createInnerAudioContext(src)
      context.play()
      currentMusicType = musicType

      console.log('开始播放背景音乐:', musicType, src)
    } catch (error) {
      console.error('播放背景音乐失败:', error)
    }
  }

  /**
   * 暂停背景音乐
   */
  pauseBackgroundMusic(): void {
    if (innerAudioContext && isPlaying) {
      innerAudioContext.pause()
      console.log('背景音乐已暂停')
    }
  }

  /**
   * 恢复背景音乐
   */
  resumeBackgroundMusic(): void {
    if (innerAudioContext && isPaused && this.settings.backgroundMusic) {
      innerAudioContext.play()
      console.log('背景音乐已恢复')
    }
  }

  /**
   * 停止背景音乐
   */
  stopBackgroundMusic(): void {
    if (innerAudioContext) {
      innerAudioContext.stop()
      console.log('背景音乐已停止')
    }
  }

  /**
   * 播放音效
   */
  playSoundEffect(effectType: keyof typeof SOUND_EFFECTS): void {
    if (!this.settings.soundEffects) {
      console.log('音效已关闭，跳过播放')
      return
    }

    try {
      const src = SOUND_EFFECTS[effectType]

      // 检查是否已有该音效的实例
      let effectContext = soundEffectInstances.get(effectType)

      if (!effectContext) {
        // 创建新的音效实例
        effectContext = uni.createInnerAudioContext()
        effectContext.src = src
        effectContext.volume = 0.6
        effectContext.loop = false
        effectContext.obeyMuteSwitch = true

        // 设置事件监听
        effectContext.onPlay(() => {
          console.log('音效播放:', effectType)
        })

        effectContext.onError((error) => {
          console.error('音效播放失败:', effectType, error)
          soundEffectInstances.delete(effectType)
        })

        effectContext.onEnded(() => {
          console.log('音效播放结束:', effectType)
        })

        // 缓存实例
        soundEffectInstances.set(effectType, effectContext)
      }

      // 播放音效
      effectContext.play()
    } catch (error) {
      console.error('播放音效失败:', effectType, error)
    }
  }

  /**
   * 触发震动
   */
  vibrate(type: 'short' | 'long' = 'short'): void {
    if (!this.settings.vibration) {
      console.log('震动已关闭，跳过震动')
      return
    }

    try {
      if (type === 'short') {
        uni.vibrateShort({
          success: () => {
            console.log('短震动触发成功')
          },
          fail: (error) => {
            console.error('短震动触发失败:', error)
          }
        })
      } else {
        uni.vibrateLong({
          success: () => {
            console.log('长震动触发成功')
          },
          fail: (error) => {
            console.error('长震动触发失败:', error)
          }
        })
      }
    } catch (error) {
      console.error('震动触发失败:', error)
    }
  }

  /**
   * 页面显示时恢复音频
   */
  onPageShow(): void {
    if (this.settings.backgroundMusic && isPaused) {
      this.resumeBackgroundMusic()
    }
  }

  /**
   * 页面隐藏时暂停音频
   */
  onPageHide(): void {
    if (isPlaying) {
      this.pauseBackgroundMusic()
    }
  }

  /**
   * 销毁音频资源
   */
  destroy(): void {
    // 销毁背景音乐
    this.destroyInnerAudioContext()

    // 销毁所有音效实例
    soundEffectInstances.forEach((context, key) => {
      try {
        context.destroy()
      } catch (error) {
        console.error('销毁音效实例失败:', key, error)
      }
    })
    soundEffectInstances.clear()

    console.log('音频资源已销毁')
  }

  /**
   * 获取播放状态
   */
  getPlayStatus(): { isPlaying: boolean; isPaused: boolean; currentSrc: string; musicType: string } {
    return {
      isPlaying,
      isPaused,
      currentSrc: currentAudioSrc,
      musicType: currentMusicType
    }
  }
}

// 导出单例实例
export const audioManager = AudioManager.getInstance()

// 导出音乐和音效常量
export { DEFAULT_BACKGROUND_MUSIC as BACKGROUND_MUSIC, SOUND_EFFECTS }