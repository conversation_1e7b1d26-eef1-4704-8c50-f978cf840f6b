"use strict";
const common_vendor = require("../../common/vendor.js");
require("../../api/request.js");
const api_weixin = require("../../api/weixin.js");
const utils_share = require("../../utils/share.js");
const utils_debugControl = require("../../utils/debug-control.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const isDebugAllowed = common_vendor.ref(false);
    const currentEnvironment = common_vendor.ref("");
    const currentOpenid = common_vendor.ref("");
    const newOpenid = common_vendor.ref("");
    const testLevelId = common_vendor.ref("level-uuid-123");
    const testUserId = common_vendor.ref("12345678");
    const apiResult = common_vendor.ref("等待API调用...");
    common_vendor.onMounted(() => {
      checkDebugAccess();
      if (isDebugAllowed.value) {
        refreshOpenid();
      }
    });
    const checkDebugAccess = () => {
      try {
        isDebugAllowed.value = utils_debugControl.shouldShowDebugPages();
        const envInfo = utils_debugControl.getDebugEnvironmentInfo();
        currentEnvironment.value = envInfo.environment;
        if (!isDebugAllowed.value) {
          utils_debugControl.debugLog("调试页面访问被拒绝，当前环境:" + envInfo.environment);
        }
      } catch (error) {
        utils_debugControl.debugError("检查调试权限失败:", error);
        isDebugAllowed.value = false;
      }
    };
    const goBack = () => {
      common_vendor.index.navigateBack({
        fail: () => {
          common_vendor.index.switchTab({
            url: "/pages/index/index"
          });
        }
      });
    };
    const refreshOpenid = () => {
      currentOpenid.value = api_weixin.weixinApi.getOpenid();
    };
    const setNewOpenid = () => {
      if (newOpenid.value.trim()) {
        api_weixin.weixinApi.setOpenid(newOpenid.value.trim());
        refreshOpenid();
        newOpenid.value = "";
        common_vendor.index.showToast({
          title: "OpenID 已设置",
          icon: "success"
        });
      }
    };
    const testGetUserInfo = async () => {
      try {
        apiResult.value = "正在获取用户信息...";
        const userInfo = await api_weixin.weixinApi.getUserInfo();
        apiResult.value = JSON.stringify(userInfo, null, 2);
      } catch (error) {
        apiResult.value = `错误: ${error.message}`;
      }
    };
    const testGetlevel = async () => {
      try {
        apiResult.value = "正在获取关卡列表...";
        const level = await api_weixin.weixinApi.getlevel();
        apiResult.value = JSON.stringify(level, null, 2);
      } catch (error) {
        apiResult.value = `错误: ${error.message}`;
      }
    };
    const testWeixinLoginCode = async () => {
      try {
        apiResult.value = "正在获取微信登录凭证...";
        const loginResult = await api_weixin.weixinApi.weixinLogin();
        apiResult.value = JSON.stringify(loginResult, null, 2);
      } catch (error) {
        apiResult.value = `错误: ${error.message}`;
      }
    };
    const testCheckSession = async () => {
      try {
        apiResult.value = "正在检查登录状态...";
        const isValid = await api_weixin.weixinApi.checkSession();
        apiResult.value = JSON.stringify({ isValid, message: isValid ? "登录状态有效" : "登录状态已过期" }, null, 2);
      } catch (error) {
        apiResult.value = `错误: ${error.message}`;
      }
    };
    const testWeixinLogin = async () => {
      try {
        apiResult.value = "正在执行完整微信登录流程（只使用uni.login）...";
        const loginResponse = await api_weixin.weixinApi.performWeixinLogin({
          phone: "",
          nickname: "测试用户",
          avatarUrl: ""
        });
        apiResult.value = JSON.stringify(loginResponse, null, 2);
      } catch (error) {
        apiResult.value = `错误: ${error.message}`;
      }
    };
    const testBindPhone = async () => {
      try {
        apiResult.value = "正在绑定手机号...";
        const userInfo = await api_weixin.weixinApi.bindPhone({
          openid: api_weixin.weixinApi.getOpenid(),
          phone: "",
          nickname: "测试用户",
          avatarUrl: "https://via.placeholder.com/100x100"
        });
        apiResult.value = JSON.stringify(userInfo, null, 2);
      } catch (error) {
        apiResult.value = `错误: ${error.message}`;
      }
    };
    const testCheckConfig = async () => {
      try {
        apiResult.value = "正在检查微信配置...";
        const config = await api_weixin.weixinApi.checkWeixinConfig();
        apiResult.value = JSON.stringify(config, null, 2);
      } catch (error) {
        apiResult.value = `错误: ${error.message}`;
      }
    };
    const testGetLevelDetail = async () => {
      try {
        if (!testLevelId.value.trim()) {
          apiResult.value = "请输入关卡ID";
          return;
        }
        apiResult.value = "正在获取关卡详情...";
        const levelDetail = await api_weixin.weixinApi.getLevelDetail(testLevelId.value.trim());
        apiResult.value = JSON.stringify(levelDetail, null, 2);
      } catch (error) {
        apiResult.value = `错误: ${error.message}`;
      }
    };
    const testCompleteLevel = async () => {
      try {
        if (!testUserId.value.trim() || !testLevelId.value.trim()) {
          apiResult.value = "请输入用户ID和关卡ID";
          return;
        }
        apiResult.value = "正在调用通关接口...";
        const userInfo = await api_weixin.weixinApi.completeLevel(testUserId.value.trim(), testLevelId.value.trim());
        apiResult.value = JSON.stringify(userInfo, null, 2);
      } catch (error) {
        apiResult.value = `错误: ${error.message}`;
      }
    };
    const testStartGame = async () => {
      try {
        if (!testUserId.value.trim() || !testLevelId.value.trim()) {
          apiResult.value = "请输入用户ID和关卡ID";
          return;
        }
        apiResult.value = "正在调用开始游戏接口...";
        const userInfo = await api_weixin.weixinApi.startGame(testUserId.value.trim(), testLevelId.value.trim());
        apiResult.value = JSON.stringify(userInfo, null, 2);
      } catch (error) {
        apiResult.value = `错误: ${error.message}`;
      }
    };
    const testGetShareConfig = async () => {
      try {
        apiResult.value = "正在获取分享配置...";
        const shareConfig = await api_weixin.weixinApi.getShareConfig({
          page: "pages/index/index",
          levelId: testLevelId.value.trim() || void 0,
          userId: testUserId.value.trim() || void 0
        });
        apiResult.value = JSON.stringify(shareConfig, null, 2);
      } catch (error) {
        apiResult.value = `错误: ${error.message}`;
      }
    };
    const testShareToWeixin = async () => {
      try {
        apiResult.value = "正在测试微信分享...";
        await utils_share.shareUtils.shareToWeixin({
          scene: "WXSceneSession",
          type: 0,
          params: {
            page: "pages/index/index",
            levelId: testLevelId.value.trim() || void 0,
            userId: testUserId.value.trim() || void 0
          }
        });
        apiResult.value = "微信分享调用成功（请查看微信分享界面）";
      } catch (error) {
        apiResult.value = `错误: ${error.message}`;
      }
    };
    const testSystemShare = async () => {
      try {
        apiResult.value = "正在测试系统分享...";
        await utils_share.shareUtils.shareWithSystem({
          page: "pages/index/index",
          levelId: testLevelId.value.trim() || void 0,
          userId: testUserId.value.trim() || void 0
        });
        apiResult.value = "系统分享调用成功（请查看系统分享界面）";
      } catch (error) {
        apiResult.value = `错误: ${error.message}`;
      }
    };
    const testGetShareReward = async () => {
      try {
        if (!testUserId.value.trim()) {
          apiResult.value = "请输入用户ID";
          return;
        }
        apiResult.value = "正在获取分享奖励...";
        const rewardResponse = await api_weixin.weixinApi.getShareReward({
          userId: testUserId.value.trim(),
          shareType: "app_message",
          page: "pages/index/index",
          levelId: testLevelId.value.trim() || void 0,
          timestamp: Date.now()
        });
        apiResult.value = JSON.stringify(rewardResponse, null, 2);
        if (rewardResponse.success) {
          common_vendor.index.showToast({
            title: `获得${rewardResponse.reward.description}！`,
            icon: "success",
            duration: 3e3
          });
        }
      } catch (error) {
        apiResult.value = `错误: ${error.message}`;
      }
    };
    const testCheckDailyShare = async () => {
      try {
        if (!testUserId.value.trim()) {
          apiResult.value = "请输入用户ID";
          return;
        }
        const hasSharedToday = utils_share.shareUtils.checkDailyShareReward(testUserId.value.trim());
        const today = (/* @__PURE__ */ new Date()).toDateString();
        apiResult.value = JSON.stringify({
          userId: testUserId.value.trim(),
          date: today,
          hasSharedToday,
          message: hasSharedToday ? "今日已获取过分享奖励" : "今日尚未获取分享奖励"
        }, null, 2);
        common_vendor.index.showToast({
          title: hasSharedToday ? "今日已分享" : "今日未分享",
          icon: hasSharedToday ? "success" : "none",
          duration: 2e3
        });
      } catch (error) {
        apiResult.value = `错误: ${error.message}`;
      }
    };
    const testResetDailyShare = async () => {
      try {
        if (!testUserId.value.trim()) {
          apiResult.value = "请输入用户ID";
          return;
        }
        const today = (/* @__PURE__ */ new Date()).toDateString();
        const storageKey = `daily_share_reward_${testUserId.value.trim()}_${today}`;
        common_vendor.index.removeStorageSync(storageKey);
        apiResult.value = JSON.stringify({
          userId: testUserId.value.trim(),
          date: today,
          action: "已重置今日分享状态",
          storageKey
        }, null, 2);
        common_vendor.index.showToast({
          title: "分享状态已重置",
          icon: "success",
          duration: 2e3
        });
      } catch (error) {
        apiResult.value = `错误: ${error.message}`;
      }
    };
    const testMultipleShareReward = async () => {
      try {
        if (!testUserId.value.trim()) {
          apiResult.value = "请输入用户ID";
          return;
        }
        apiResult.value = "正在测试防重复执行机制...\n";
        const promises = [];
        for (let i = 0; i < 5; i++) {
          promises.push(
            utils_share.shareUtils.claimShareReward({
              userId: testUserId.value.trim(),
              shareType: "app_message",
              page: "pages/debug/index",
              timestamp: Date.now() + i
            })
          );
        }
        await Promise.all(promises);
        apiResult.value += "\n测试完成：已连续发起5次分享奖励请求\n";
        apiResult.value += "如果防重复机制正常工作，应该只有第一次请求被处理\n";
        apiResult.value += '其他请求应该被跳过并显示"正在处理中"的日志';
        common_vendor.index.showToast({
          title: "防重复测试完成",
          icon: "success",
          duration: 2e3
        });
      } catch (error) {
        apiResult.value = `错误: ${error.message}`;
      }
    };
    const testDailyStatus = async () => {
      try {
        if (!testUserId.value.trim()) {
          apiResult.value = "请输入用户ID";
          return;
        }
        apiResult.value = "正在获取每日状态...\n";
        const dailyStatus = await api_weixin.weixinApi.getDailyStatus();
        apiResult.value = JSON.stringify({
          action: "获取每日状态",
          result: dailyStatus,
          analysis: {
            isVip: dailyStatus.isVip ? "VIP用户，无限制" : "普通用户",
            unlockStatus: `${dailyStatus.dailyUnlockCount}/${dailyStatus.dailyUnlockLimit}`,
            remaining: dailyStatus.remainingUnlocks,
            canUnlock: dailyStatus.canUnlock,
            shareStatus: dailyStatus.dailyShared ? "今日已分享" : "今日未分享"
          }
        }, null, 2);
        common_vendor.index.showToast({
          title: "每日状态获取成功",
          icon: "success",
          duration: 2e3
        });
      } catch (error) {
        apiResult.value = `错误: ${error.message}`;
      }
    };
    const testNewShareAPI = async () => {
      try {
        if (!testUserId.value.trim()) {
          apiResult.value = "请输入用户ID";
          return;
        }
        apiResult.value = "正在测试新分享API...\n";
        const shareResponse = await api_weixin.weixinApi.shareForReward();
        apiResult.value = JSON.stringify({
          action: "新分享API测试",
          result: shareResponse,
          analysis: {
            status: shareResponse.status,
            message: shareResponse.message,
            unlockInfo: shareResponse.status === "success" ? {
              dailyUnlockCount: shareResponse.dailyUnlockCount,
              dailyUnlockLimit: shareResponse.dailyUnlockLimit,
              remainingUnlocks: shareResponse.remainingUnlocks,
              isVip: shareResponse.isVip,
              totalShares: shareResponse.totalShares
            } : null
          }
        }, null, 2);
        common_vendor.index.showToast({
          title: "新分享API测试完成",
          icon: "success",
          duration: 2e3
        });
      } catch (error) {
        apiResult.value = `错误: ${error.message}`;
      }
    };
    const testVipPackages = async () => {
      try {
        apiResult.value = "正在获取VIP套餐列表...\n";
        const vipPackages = await api_weixin.weixinApi.getVipPackages();
        apiResult.value = JSON.stringify({
          action: "获取VIP套餐列表",
          result: vipPackages,
          analysis: {
            packageCount: vipPackages.length,
            packages: vipPackages.map((pkg) => ({
              id: pkg.id,
              name: pkg.name,
              price: `¥${(pkg.price / 100).toFixed(2)}`,
              duration: `${pkg.duration}天`,
              isActive: pkg.isActive
            }))
          }
        }, null, 2);
        common_vendor.index.showToast({
          title: "VIP套餐获取成功",
          icon: "success",
          duration: 2e3
        });
      } catch (error) {
        apiResult.value = `错误: ${error.message}`;
      }
    };
    const testVipPayment = async () => {
      try {
        if (!testUserId.value.trim()) {
          apiResult.value = "请输入用户ID";
          return;
        }
        apiResult.value = "正在测试VIP支付...\n";
        const vipPackages = await api_weixin.weixinApi.getVipPackages();
        if (vipPackages.length === 0) {
          apiResult.value = "没有可用的VIP套餐";
          return;
        }
        const testPackage = vipPackages[0];
        apiResult.value += `选择测试套餐: ${testPackage.name}
`;
        apiResult.value += `价格: ¥${(testPackage.price / 100).toFixed(2)}
`;
        apiResult.value += `时长: ${testPackage.duration}天

`;
        const paymentParams = await api_weixin.weixinApi.createPayment({
          openid: testUserId.value.trim(),
          packageId: testPackage.id
        });
        apiResult.value += "支付参数创建成功:\n";
        apiResult.value += JSON.stringify({
          action: "创建VIP支付订单",
          packageInfo: {
            id: testPackage.id,
            name: testPackage.name,
            price: testPackage.price,
            duration: testPackage.duration
          },
          paymentParams: {
            appId: paymentParams.appId,
            timeStamp: paymentParams.timeStamp,
            nonceStr: paymentParams.nonceStr,
            package: paymentParams.package,
            signType: paymentParams.signType,
            paySign: paymentParams.paySign ? "已生成" : "未生成"
          }
        }, null, 2);
        common_vendor.index.showToast({
          title: "VIP支付测试完成",
          icon: "success",
          duration: 2e3
        });
      } catch (error) {
        apiResult.value = `错误: ${error.message}`;
      }
    };
    const goToIconTest = () => {
      common_vendor.index.navigateTo({
        url: "/pages/test-icons/index"
      });
    };
    const goToAudioTest = () => {
      common_vendor.index.navigateTo({
        url: "/pages/test-audio/index"
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: !isDebugAllowed.value
      }, !isDebugAllowed.value ? {
        b: common_vendor.o(goBack)
      } : {
        c: common_vendor.t(currentEnvironment.value),
        d: common_vendor.t(currentOpenid.value),
        e: common_vendor.o(refreshOpenid),
        f: newOpenid.value,
        g: common_vendor.o(($event) => newOpenid.value = $event.detail.value),
        h: common_vendor.o(setNewOpenid),
        i: common_vendor.o(testWeixinLoginCode),
        j: common_vendor.o(testCheckSession),
        k: common_vendor.o(testWeixinLogin),
        l: common_vendor.o(testBindPhone),
        m: common_vendor.o(testCheckConfig),
        n: common_vendor.o(testGetUserInfo),
        o: common_vendor.o(testGetlevel),
        p: common_vendor.o(testGetLevelDetail),
        q: common_vendor.o(testCompleteLevel),
        r: common_vendor.o(testStartGame),
        s: common_vendor.o(testGetShareConfig),
        t: common_vendor.o(testShareToWeixin),
        v: common_vendor.o(testSystemShare),
        w: common_vendor.o(testGetShareReward),
        x: common_vendor.o(testCheckDailyShare),
        y: common_vendor.o(testResetDailyShare),
        z: common_vendor.o(testMultipleShareReward),
        A: common_vendor.o(testDailyStatus),
        B: common_vendor.o(testNewShareAPI),
        C: common_vendor.o(testVipPackages),
        D: common_vendor.o(testVipPayment),
        E: common_vendor.o(goToIconTest),
        F: common_vendor.o(goToAudioTest),
        G: testLevelId.value,
        H: common_vendor.o(($event) => testLevelId.value = $event.detail.value),
        I: testUserId.value,
        J: common_vendor.o(($event) => testUserId.value = $event.detail.value),
        K: common_vendor.t(apiResult.value)
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-14d3e3ed"]]);
wx.createPage(MiniProgramPage);
