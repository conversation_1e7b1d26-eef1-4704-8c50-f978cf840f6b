<view class="favorites-container data-v-b2f01737"><view class="page-header data-v-b2f01737"><text class="page-title data-v-b2f01737">我的收藏</text><text class="page-subtitle data-v-b2f01737">收藏的关卡列表</text></view><view wx:if="{{a}}" class="loading-container data-v-b2f01737"><view class="loading-spinner data-v-b2f01737"></view><text class="loading-text data-v-b2f01737">正在加载收藏...</text></view><view wx:elif="{{b}}" class="error-container data-v-b2f01737"><view class="error-icon data-v-b2f01737">⚠️</view><text class="error-title data-v-b2f01737">加载失败</text><text class="error-text data-v-b2f01737">{{c}}</text><button class="retry-btn data-v-b2f01737" bindtap="{{d}}"><text class="retry-text data-v-b2f01737">重试</text></button></view><view wx:elif="{{e}}" class="empty-container data-v-b2f01737"><view class="empty-title data-v-b2f01737">还没有收藏的关卡</view><view class="empty-text data-v-b2f01737">在游戏中完成关卡后，可以收藏喜欢的关卡</view><view class="empty-hint data-v-b2f01737">收藏的关卡会显示在这里，方便你随时回顾挑战</view><view class="empty-actions data-v-b2f01737"><button class="browse-btn primary data-v-b2f01737" bindtap="{{f}}"><text class="browse-text data-v-b2f01737">去选择关卡</text></button><button class="browse-btn secondary data-v-b2f01737" bindtap="{{g}}"><text class="browse-text data-v-b2f01737">标签挑战</text></button></view></view><view wx:else class="favorites-list data-v-b2f01737"><view class="list-header data-v-b2f01737"><text class="list-title data-v-b2f01737">共{{h}}个收藏</text></view><view class="levels-grid data-v-b2f01737"><view wx:for="{{i}}" wx:for-item="level" wx:key="j" class="{{['level-card', 'data-v-b2f01737', level.k && 'level-locked', level.l && 'level-completed']}}" bindtap="{{level.m}}"><view class="level-number data-v-b2f01737">{{level.a}}</view><view class="level-info data-v-b2f01737"><text class="level-name data-v-b2f01737">{{level.b}}</text><text class="level-desc data-v-b2f01737">{{level.c}}</text><view wx:if="{{level.d}}" class="level-tags data-v-b2f01737"><view wx:for="{{level.e}}" wx:for-item="tag" wx:key="c" class="{{['tag-item', 'data-v-b2f01737', tag.d && 'tag-vip']}}"><text class="tag-text data-v-b2f01737">{{tag.a}}</text><text wx:if="{{tag.b}}" class="tag-vip-icon data-v-b2f01737">👑</text></view></view></view><view class="level-status data-v-b2f01737"><view wx:if="{{level.f}}" class="status-badge locked data-v-b2f01737"><text class="status-text data-v-b2f01737">🔒 未解锁</text></view><view wx:elif="{{level.g}}" class="status-badge completed data-v-b2f01737"><view class="level-stars data-v-b2f01737"><text wx:for="{{level.h}}" wx:for-item="star" wx:key="a" class="star star-filled data-v-b2f01737"> ⭐ </text></view></view><view wx:else class="status-badge available data-v-b2f01737"><text class="status-text data-v-b2f01737">开始</text></view></view><view class="unfavorite-btn data-v-b2f01737" catchtap="{{level.i}}"><text class="unfavorite-icon data-v-b2f01737">💔</text></view></view></view></view><view class="bottom-actions data-v-b2f01737"><button class="back-btn data-v-b2f01737" bindtap="{{j}}"><text class="back-text data-v-b2f01737">返回</text></button></view></view>