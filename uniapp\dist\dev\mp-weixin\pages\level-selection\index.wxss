/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.level-selection-container.data-v-5578dee7 {
  min-height: 100vh;
  background: #fdf9f9;
  padding: 40rpx 20rpx;
}
.header-section.data-v-5578dee7 {
  margin-bottom: 40rpx;
}
.header-content.data-v-5578dee7 {
  text-align: center;
  margin-bottom: 32rpx;
  padding: 32rpx 24rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.page-title.data-v-5578dee7 {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.page-subtitle.data-v-5578dee7 {
  display: block;
  font-size: 28rpx;
  color: #4a5568;
  font-weight: 500;
  line-height: 1.4;
}
.progress-card.data-v-5578dee7 {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 24rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.progress-item.data-v-5578dee7 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}
.progress-label.data-v-5578dee7 {
  font-size: 22rpx;
  color: #111;
}
.progress-value.data-v-5578dee7 {
  font-size: 36rpx;
  font-weight: bold;
  color: #111;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.loading-container.data-v-5578dee7 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}
.loading-spinner.data-v-5578dee7 {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #ffffff;
  border-radius: 50%;
  animation: spin-5578dee7 1s linear infinite;
  margin-bottom: 32rpx;
}
@keyframes spin-5578dee7 {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.loading-text.data-v-5578dee7 {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}
.error-container.data-v-5578dee7 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}
.error-icon.data-v-5578dee7 {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}
.error-title.data-v-5578dee7 {
  font-size: 32rpx;
  font-weight: bold;
  color: #111;
  margin-bottom: 16rpx;
}
.error-text.data-v-5578dee7 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 40rpx;
  line-height: 1.5;
}
.retry-btn.data-v-5578dee7 {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: #ffffff;
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
  box-shadow: 0 8rpx 20rpx rgba(116, 185, 255, 0.3);
}
.retry-btn.data-v-5578dee7:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}
.retry-text.data-v-5578dee7 {
  color: inherit;
}
.levels-container.data-v-5578dee7 {
  margin-bottom: 40rpx;
}
.levels-grid.data-v-5578dee7 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.level-card.data-v-5578dee7 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}
.level-main-content.data-v-5578dee7 {
  display: flex;
  align-items: center;
  gap: 24rpx;
}
.level-card.data-v-5578dee7:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.15);
}
.level-card.level-locked.data-v-5578dee7 {
  opacity: 0.7;
}
.level-number.data-v-5578dee7 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  flex-shrink: 0;
}
.level-locked .level-number.data-v-5578dee7 {
  background: #bbb;
  box-shadow: 0 4rpx 12rpx rgba(187, 187, 187, 0.3);
}
.level-completed .level-number.data-v-5578dee7 {
  background: linear-gradient(135deg, #00b894, #00a085);
  box-shadow: 0 4rpx 12rpx rgba(0, 184, 148, 0.3);
}
.level-info.data-v-5578dee7 {
  flex: 1;
  min-width: 0;
}
.level-name.data-v-5578dee7 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 8rpx;
  line-height: 1.2;
}
.level-desc.data-v-5578dee7 {
  display: block;
  font-size: 24rpx;
  color: #718096;
  line-height: 1.3;
}
.level-locked .level-name.data-v-5578dee7,
.level-locked .level-desc.data-v-5578dee7 {
  color: #999;
}
.level-status.data-v-5578dee7 {
  flex-shrink: 0;
}
.status-badge.data-v-5578dee7 {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  text-align: center;
}
.status-badge.locked.data-v-5578dee7 {
  background: #f0f0f0;
}
.status-badge.completed.data-v-5578dee7 {
  background: #d1f2eb;
}
.status-badge.available.data-v-5578dee7 {
  background: #e3f2fd;
}
.status-text.data-v-5578dee7 {
  font-size: 22rpx;
  font-weight: 500;
}
.locked .status-text.data-v-5578dee7 {
  color: #999;
}
.completed .status-text.data-v-5578dee7 {
  color: #00b894;
}
.available .status-text.data-v-5578dee7 {
  color: #667eea;
}

/* 星级显示样式 */
.level-stars.data-v-5578dee7 {
  display: flex;
  align-items: center;
  gap: 4rpx;
}
.star.data-v-5578dee7 {
  font-size: 20rpx;
  color: #ddd;
  transition: color 0.2s;
}
.star-filled.data-v-5578dee7 {
  color: #ffd700;
}

/* 收藏按钮样式 */
.favorite-btn.data-v-5578dee7 {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transition: all 0.2s;
}
.favorite-btn.data-v-5578dee7:active {
  transform: scale(0.9);
  background: rgb(255, 255, 255);
}
.favorite-icon.data-v-5578dee7 {
  font-size: 24rpx;
  transition: all 0.2s;
}
.favorite-icon.favorited.data-v-5578dee7 {
  animation: heartbeat-5578dee7 0.6s ease-in-out;
}
@keyframes heartbeat-5578dee7 {
0% {
    transform: scale(1);
}
50% {
    transform: scale(1.2);
}
100% {
    transform: scale(1);
}
}
.vip-promotion.data-v-5578dee7 {
  margin-bottom: 40rpx;
}
.vip-card.data-v-5578dee7 {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  padding: 32rpx;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}
.vip-header.data-v-5578dee7 {
  text-align: center;
  margin-bottom: 24rpx;
}
.vip-title.data-v-5578dee7 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #111;
  margin-bottom: 8rpx;
}
.vip-subtitle.data-v-5578dee7 {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}
.vip-btn.data-v-5578dee7 {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #ffd700, #ffb347);
  border-radius: 40rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  box-shadow: 0 8rpx 20rpx rgba(255, 215, 0, 0.3);
  transition: all 0.2s;
}
.vip-btn.data-v-5578dee7:active {
  transform: scale(0.98);
  opacity: 0.9;
}
.vip-btn-text.data-v-5578dee7 {
  color: inherit;
}
.bottom-actions.data-v-5578dee7 {
  display: flex;
  justify-content: center;
  padding-top: 20rpx;
}
.back-btn.data-v-5578dee7 {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: #ffffff;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
  box-shadow: 0 8rpx 20rpx rgba(116, 185, 255, 0.3);
}
.back-btn.data-v-5578dee7:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}
.back-text.data-v-5578dee7 {
  color: inherit;
}

/* 关卡标签样式 */
.level-tags.data-v-5578dee7 {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}
.tag-item.data-v-5578dee7 {
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  background: #f0f0f0;
  pointer-events: none; /* 标签不可点击 */
}
.tag-vip.data-v-5578dee7 {
  background: linear-gradient(135deg, #ffd700, #ffb347);
}
.tag-text.data-v-5578dee7 {
  font-size: 20rpx;
  color: #666;
  font-weight: 400;
}
.tag-vip .tag-text.data-v-5578dee7 {
  color: #8b4513;
  font-weight: 500;
}
.tag-vip-icon.data-v-5578dee7 {
  font-size: 16rpx;
}
.tag-more.data-v-5578dee7 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  background: #e0e0e0;
  pointer-events: none;
}
.tag-more-text.data-v-5578dee7 {
  font-size: 20rpx;
  color: #999;
  font-weight: 400;
}