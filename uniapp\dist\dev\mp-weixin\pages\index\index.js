"use strict";
const common_vendor = require("../../common/vendor.js");
const api_weixin = require("../../api/weixin.js");
const api_utils = require("../../api/utils.js");
const utils_share = require("../../utils/share.js");
const utils_audio = require("../../utils/audio.js");
const utils_debugControl = require("../../utils/debug-control.js");
const utils_auth = require("../../utils/auth.js");
const composables_useGlobalConfig = require("../../composables/useGlobalConfig.js");
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  _easycom_uni_icons2();
}
const _easycom_uni_icons = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons.js";
if (!Math) {
  (_easycom_uni_icons + SettingsModal)();
}
const SettingsModal = () => "../../components/SettingsModal.js";
const _sfc_defineComponent = common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const levelState = common_vendor.reactive({
      level: [],
      currentLevel: null,
      ...api_utils.createLoadingState()
    });
    const userInfo = common_vendor.ref(null);
    const dailyStatus = common_vendor.ref(null);
    const lastLevelInfo = common_vendor.ref(null);
    const vipPackages = common_vendor.ref([]);
    const isLoadingVipPackages = common_vendor.ref(false);
    const userStarStats = common_vendor.ref(null);
    const showSettingsModal = common_vendor.ref(false);
    const gameSettings = common_vendor.ref({
      backgroundMusic: true,
      soundEffects: true,
      vibration: true
    });
    const {
      getBackgroundMusicUrl,
      showHelpModal,
      initializeGlobalConfig
    } = composables_useGlobalConfig.useGlobalConfig();
    const isDevelopment = common_vendor.ref(false);
    const isRefreshing = common_vendor.ref(false);
    let lastUserInfoString = "";
    let isHandlingShareReward = false;
    common_vendor.computed(() => {
      return levelState.level.map((level2, index) => ({
        id: level2.id,
        levelNumber: String(index + 1).padStart(2, "0"),
        name: level2.name,
        description: level2.description,
        library: getLibraryForLevel(level2),
        // 根据关卡获取对应词库
        locked: !level2.isUnlocked,
        completed: level2.isCompleted
      }));
    });
    const libraries = [
      {
        id: 1,
        name: "小学基础词汇",
        words: [
          { english: "apple", chinese: "苹果", phonetic: "/ˈæpl/" },
          { english: "book", chinese: "书", phonetic: "/bʊk/" },
          { english: "cat", chinese: "猫", phonetic: "/kæt/" },
          { english: "dog", chinese: "狗", phonetic: "/dɔːɡ/" },
          { english: "egg", chinese: "鸡蛋", phonetic: "/eɡ/" },
          { english: "fish", chinese: "鱼", phonetic: "/fɪʃ/" },
          { english: "green", chinese: "绿色", phonetic: "/ɡriːn/" },
          { english: "house", chinese: "房子", phonetic: "/haʊs/" },
          { english: "ice", chinese: "冰", phonetic: "/aɪs/" },
          { english: "jump", chinese: "跳", phonetic: "/dʒʌmp/" }
        ]
      },
      {
        id: 2,
        name: "动物世界",
        words: [
          { english: "lion", chinese: "狮子", phonetic: "/ˈlaɪən/" },
          { english: "tiger", chinese: "老虎", phonetic: "/ˈtaɪɡər/" },
          { english: "elephant", chinese: "大象", phonetic: "/ˈelɪfənt/" },
          { english: "monkey", chinese: "猴子", phonetic: "/ˈmʌŋki/" },
          { english: "rabbit", chinese: "兔子", phonetic: "/ˈræbɪt/" },
          { english: "bird", chinese: "鸟", phonetic: "/bɜːrd/" },
          { english: "horse", chinese: "马", phonetic: "/hɔːrs/" },
          { english: "cow", chinese: "牛", phonetic: "/kaʊ/" }
        ]
      },
      {
        id: 3,
        name: "颜色彩虹",
        words: [
          { english: "red", chinese: "红色", phonetic: "/red/" },
          { english: "blue", chinese: "蓝色", phonetic: "/bluː/" },
          { english: "yellow", chinese: "黄色", phonetic: "/ˈjeloʊ/" },
          { english: "green", chinese: "绿色", phonetic: "/ɡriːn/" },
          { english: "purple", chinese: "紫色", phonetic: "/ˈpɜːrpl/" },
          { english: "orange", chinese: "橙色", phonetic: "/ˈɔːrɪndʒ/" },
          { english: "pink", chinese: "粉色", phonetic: "/pɪŋk/" },
          { english: "black", chinese: "黑色", phonetic: "/blæk/" },
          { english: "white", chinese: "白色", phonetic: "/waɪt/" }
        ]
      },
      {
        id: 4,
        name: "数字王国",
        words: [
          { english: "one", chinese: "一", phonetic: "/wʌn/" },
          { english: "two", chinese: "二", phonetic: "/tuː/" },
          { english: "three", chinese: "三", phonetic: "/θriː/" },
          { english: "four", chinese: "四", phonetic: "/fɔːr/" },
          { english: "five", chinese: "五", phonetic: "/faɪv/" },
          { english: "six", chinese: "六", phonetic: "/sɪks/" },
          { english: "seven", chinese: "七", phonetic: "/ˈsevn/" },
          { english: "eight", chinese: "八", phonetic: "/eɪt/" },
          { english: "nine", chinese: "九", phonetic: "/naɪn/" },
          { english: "ten", chinese: "十", phonetic: "/ten/" }
        ]
      }
    ];
    const getLibraryForLevel = (level2) => {
      const difficultyIndex = (level2.difficulty - 1) % libraries.length;
      return libraries[difficultyIndex];
    };
    common_vendor.onMounted(async () => {
      await initializeGlobalConfig();
      await initializePage();
      loadLastLevelInfo();
      startVisibilityCheck();
      initAudioSettings();
      checkDevelopmentEnvironment();
    });
    common_vendor.onShow(() => {
      console.log("首页显示");
      utils_audio.audioManager.onPageShow();
      const settings = utils_audio.audioManager.getSettings();
      if (settings.backgroundMusic) {
        utils_audio.audioManager.playBackgroundMusic("main");
      }
    });
    common_vendor.onHide(() => {
      console.log("首页隐藏");
      utils_audio.audioManager.onPageHide();
    });
    const startVisibilityCheck = () => {
      const currentUserInfo = common_vendor.index.getStorageSync("userInfo");
      lastUserInfoString = currentUserInfo || "";
      setInterval(async () => {
        const newUserInfo = common_vendor.index.getStorageSync("userInfo");
        if (newUserInfo !== lastUserInfoString) {
          console.log("检测到用户信息更新，刷新首页数据");
          lastUserInfoString = newUserInfo;
          await refreshPageData();
        }
      }, 2e3);
      console.log("页面可见性检查已启动");
    };
    const initializePage = async () => {
      await api_utils.withLoading(levelState, async () => {
        await loadUserInfo();
        await loadlevel();
      }, {
        errorMessage: "页面加载失败，请重试"
      });
    };
    const refreshPageData = async () => {
      if (isRefreshing.value) {
        return;
      }
      try {
        isRefreshing.value = true;
        console.log("开始刷新首页数据...");
        await loadUserInfo();
        await refreshlevel();
        console.log("首页数据刷新完成");
      } catch (error) {
        console.error("刷新页面数据失败:", error);
      } finally {
        isRefreshing.value = false;
      }
    };
    const loadUserInfo = async () => {
      try {
        try {
          const freshUserInfo = await api_weixin.weixinApi.refreshUserInfo();
          if (freshUserInfo) {
            userInfo.value = freshUserInfo;
            console.log("从服务器获取到最新用户信息:", freshUserInfo);
            await loadDailyStatus();
            await loadUserStarStats();
            return;
          }
        } catch (apiError) {
          console.warn("从服务器获取用户信息失败，使用本地缓存:", apiError);
        }
        const localUserInfo = api_weixin.weixinApi.getLocalUserInfo();
        if (localUserInfo) {
          userInfo.value = localUserInfo;
          console.log("使用本地缓存用户信息:", localUserInfo);
          try {
            await loadDailyStatus();
            await loadUserStarStats();
          } catch (error) {
            console.warn("加载每日状态和星级统计失败:", error);
          }
        } else {
          console.warn("未找到用户信息，可能需要重新登录");
        }
      } catch (error) {
        console.error("加载用户信息失败:", error);
      }
    };
    const loadDailyStatus = async () => {
      try {
        const status = await api_weixin.weixinApi.getDailyStatus();
        dailyStatus.value = status;
        console.log("每日状态加载成功:", status);
        showDailyStatusInfo(status);
      } catch (error) {
        console.error("加载每日状态失败:", error);
      }
    };
    const loadUserStarStats = async () => {
      try {
        const stats = await api_weixin.weixinApi.getUserStarStats();
        userStarStats.value = stats;
        console.log("用户星级统计加载成功:", stats);
      } catch (error) {
        console.error("加载用户星级统计失败:", error);
        userStarStats.value = {
          totalStars: 0,
          threeStarLevels: 0,
          twoStarLevels: 0,
          oneStarLevels: 0,
          completedLevels: 0
        };
      }
    };
    const showDailyStatusInfo = (status) => {
      if (status.isVip) {
        console.log("VIP用户，无解锁限制");
        return;
      }
      const remainingText = status.remainingUnlocks > 0 ? `今日还可解锁 ${status.remainingUnlocks} 次` : "今日解锁次数已用完";
      console.log(`每日状态: ${remainingText}`);
      if (status.remainingUnlocks <= 3 && !status.dailyShared) {
        setTimeout(() => {
          common_vendor.index.showModal({
            title: "解锁提醒",
            content: `${remainingText}，分享游戏可获得5次额外解锁机会！`,
            showCancel: true,
            cancelText: "稍后再说",
            confirmText: "立即分享",
            success: (res) => {
              if (res.confirm) {
                triggerShare();
              }
            }
          });
        }, 1e3);
      }
    };
    const triggerShare = () => {
      common_vendor.index.showShareMenu({
        withShareTicket: true
      });
    };
    const initAudioSettings = () => {
      try {
        const settings = utils_audio.audioManager.getSettings();
        gameSettings.value = { ...settings };
        console.log("音频设置初始化完成:", settings);
        if (settings.backgroundMusic) {
          const musicUrl = getBackgroundMusicUrl("main");
          console.log("播放首页背景音乐:", musicUrl);
          utils_audio.audioManager.playBackgroundMusic("main", musicUrl);
        }
      } catch (error) {
        console.error("初始化音频设置失败:", error);
      }
    };
    const checkDevelopmentEnvironment = () => {
      try {
        isDevelopment.value = utils_debugControl.shouldShowDebugButtons();
        console.log("开发环境检测结果:", isDevelopment.value);
      } catch (error) {
        console.error("检测开发环境失败:", error);
        isDevelopment.value = false;
      }
    };
    const showSettings = () => {
      utils_audio.audioManager.playSoundEffect("click");
      showSettingsModal.value = true;
      console.log("显示设置弹窗");
    };
    const closeSettings = () => {
      showSettingsModal.value = false;
      console.log("关闭设置弹窗");
    };
    const handleSettingsChange = (newSettings) => {
      gameSettings.value = { ...newSettings };
      console.log("设置已更新:", newSettings);
      if (newSettings.backgroundMusic !== gameSettings.value.backgroundMusic) {
        if (newSettings.backgroundMusic) {
          utils_audio.audioManager.playBackgroundMusic("main");
        } else {
          utils_audio.audioManager.stopBackgroundMusic();
        }
      }
    };
    const showHelp = () => {
      utils_audio.audioManager.playSoundEffect("click");
      showHelpModal();
    };
    const startGame = async () => {
      utils_audio.audioManager.playSoundEffect("click");
      const isLoggedIn = await utils_auth.checkLoginAndRedirect({
        toastMessage: "请先登录以开始游戏",
        redirectUrl: "/pages/login/index?redirect=" + encodeURIComponent("/pages/level-selection/index")
      });
      if (isLoggedIn) {
        goToLevelSelectionWithScroll();
      }
    };
    const goToLevelSelectionWithScroll = () => {
      const lastLevelId = getLastLevelId();
      common_vendor.index.navigateTo({
        url: `/pages/level-selection/index${lastLevelId ? "?scrollToLevel=" + lastLevelId : ""}`
      });
    };
    const getLastLevelId = () => {
      try {
        const lastLevel = common_vendor.index.getStorageSync("selectedLevel");
        if (lastLevel) {
          const levelData = JSON.parse(lastLevel);
          return levelData.id;
        }
      } catch (error) {
        console.error("获取上次关卡ID失败:", error);
      }
      return null;
    };
    const loadLastLevelInfo = () => {
      try {
        const lastLevel = common_vendor.index.getStorageSync("selectedLevel");
        if (lastLevel) {
          const levelData = JSON.parse(lastLevel);
          lastLevelInfo.value = levelData;
          console.log("加载上次关卡信息:", levelData);
        } else {
          lastLevelInfo.value = null;
        }
      } catch (error) {
        console.error("加载上次关卡信息失败:", error);
        lastLevelInfo.value = null;
      }
    };
    const goToFavorites = async () => {
      var _a;
      utils_audio.audioManager.playSoundEffect("click");
      const isLoggedIn = await utils_auth.checkLoginAndRedirect({
        toastMessage: "请先登录以访问收藏夹",
        redirectUrl: "/pages/login/index?redirect=" + encodeURIComponent("/pages/member-center/index")
      });
      if (!isLoggedIn) {
        return;
      }
      try {
        if (!dailyStatus.value) {
          console.log("🔍 获取每日状态以检查VIP权限...");
          await loadDailyStatus();
        }
        if ((_a = dailyStatus.value) == null ? void 0 : _a.isVip) {
          console.log("✅ VIP用户，打开收藏夹");
          common_vendor.index.navigateTo({
            url: "/pages/favorites/index"
          });
        } else {
          console.log("❌ 普通用户，显示VIP提示");
          showVipRequiredModal();
        }
      } catch (error) {
        console.error("检查VIP状态失败:", error);
        showVipRequiredModal();
      }
    };
    const showVipRequiredModal = () => {
      common_vendor.index.showModal({
        title: "收藏功能",
        content: "收藏为会员专享，解锁所有主题可享受更全面的功能！立即开通会员？",
        confirmText: "去开通",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            utils_audio.audioManager.playSoundEffect("click");
            common_vendor.index.navigateTo({
              url: "/pages/member-center/index"
            });
          } else {
            utils_audio.audioManager.playSoundEffect("click");
          }
        }
      });
    };
    const goToTagChallenge = async () => {
      var _a;
      utils_audio.audioManager.playSoundEffect("click");
      const isLoggedIn = await utils_auth.checkLoginAndRedirect({
        toastMessage: "请先登录以进行标签闯关",
        redirectUrl: "/pages/login/index?redirect=" + encodeURIComponent("/pages/tag-challenge/index")
      });
      if (!isLoggedIn) {
        return;
      }
      try {
        if (!dailyStatus.value) {
          console.log("🔍 获取每日状态以检查VIP权限...");
          await loadDailyStatus();
        }
        if ((_a = dailyStatus.value) == null ? void 0 : _a.isVip) {
          console.log("✅ VIP用户，允许访问标签闯关");
          common_vendor.index.navigateTo({
            url: "/pages/tag-challenge/index"
          });
        } else {
          console.log("❌ 非VIP用户，显示权限提示");
          showTagChallengeVipModal();
        }
      } catch (error) {
        console.error("❌ 检查VIP状态失败:", error);
        common_vendor.index.showToast({
          title: "检查权限失败，请重试",
          icon: "none",
          duration: 1500
        });
      }
    };
    const showTagChallengeVipModal = () => {
      common_vendor.index.showModal({
        title: "VIP专享功能",
        content: "标签闯关为VIP专享功能，开通VIP会员即可畅玩所有标签关卡！",
        confirmText: "开通VIP",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.navigateTo({
              url: "/pages/member-center/index"
            });
          }
        }
      });
    };
    const goToMemberCenter = async () => {
      utils_audio.audioManager.playSoundEffect("click");
      const isLoggedIn = await utils_auth.checkLoginAndRedirect({
        toastMessage: "请先登录以访问会员中心",
        redirectUrl: "/pages/login/index"
      });
      if (isLoggedIn) {
        common_vendor.index.navigateTo({
          url: "/pages/member-center/index"
        });
      }
    };
    const showVipPackages = async () => {
      try {
        utils_audio.audioManager.playSoundEffect("click");
        isLoadingVipPackages.value = true;
        const packages = await api_weixin.weixinApi.getVipPackages();
        vipPackages.value = packages;
        console.log("VIP套餐列表:", packages);
        const packageOptions = packages.map(
          (pkg) => `${pkg.name} - ¥${(pkg.price / 100).toFixed(2)}`
        );
        common_vendor.index.showActionSheet({
          itemList: packageOptions,
          success: (res) => {
            const selectedPackage = packages[res.tapIndex];
            if (selectedPackage) {
              showPaymentConfirm(selectedPackage);
            }
          },
          fail: (err) => {
            console.log("用户取消选择套餐:", err);
          }
        });
      } catch (error) {
        console.error("加载VIP套餐失败:", error);
        common_vendor.index.showToast({
          title: "加载套餐失败",
          icon: "none",
          duration: 2e3
        });
      } finally {
        isLoadingVipPackages.value = false;
      }
    };
    const showPaymentConfirm = (vipPackage) => {
      const price = (vipPackage.price / 100).toFixed(2);
      common_vendor.index.showModal({
        title: "确认购买",
        content: `${vipPackage.name}
价格：¥${price}
时长：${vipPackage.duration}天

${vipPackage.description}`,
        showCancel: true,
        cancelText: "取消",
        confirmText: "立即支付",
        success: (res) => {
          if (res.confirm) {
            handleVipPayment(vipPackage);
          }
        }
      });
    };
    const handleVipPayment = async (vipPackage) => {
      try {
        common_vendor.index.showLoading({
          title: "正在创建订单..."
        });
        const paymentSuccess = await api_weixin.weixinApi.requestPayment(vipPackage.id);
        if (paymentSuccess) {
          await loadUserInfo();
          await loadDailyStatus();
          common_vendor.index.showModal({
            title: "支付成功",
            content: `恭喜您成为VIP会员！
已获得${vipPackage.duration}天VIP特权
现在可以无限制解锁所有关卡！`,
            showCancel: false,
            confirmText: "开始游戏"
          });
        }
      } catch (error) {
        console.error("VIP支付失败:", error);
        if (error && typeof error === "object" && "errMsg" in error) {
          const errMsg = error.errMsg;
          if (errMsg.includes("cancel")) {
            return;
          }
        }
        common_vendor.index.showModal({
          title: "支付失败",
          content: "支付过程中出现问题，请稍后重试。如有疑问请联系客服。",
          showCancel: true,
          cancelText: "稍后重试",
          confirmText: "联系客服"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    };
    const loadlevel = async () => {
      try {
        const apiLevels = await api_weixin.weixinApi.getLevels();
        levelState.level = apiLevels;
        console.log("关卡列表加载成功:", apiLevels);
      } catch (error) {
        console.error("加载关卡列表失败:", error);
        console.log("使用本地备用关卡数据");
        levelState.level = createFallbacklevel();
      }
    };
    const createFallbacklevel = () => {
      return [
        {
          id: "fallback-level-1",
          name: "第1关 - 基础词汇",
          difficulty: 1,
          description: "小学基础词汇练习",
          isUnlocked: true,
          isCompleted: false,
          createdAt: (/* @__PURE__ */ new Date()).toISOString()
        },
        {
          id: "fallback-level-2",
          name: "第2关 - 动物世界",
          difficulty: 2,
          description: "认识可爱的动物",
          isUnlocked: true,
          isCompleted: false,
          createdAt: (/* @__PURE__ */ new Date()).toISOString()
        },
        {
          id: "fallback-level-3",
          name: "第3关 - 颜色彩虹",
          difficulty: 3,
          description: "学习各种颜色",
          isUnlocked: false,
          isCompleted: false,
          createdAt: (/* @__PURE__ */ new Date()).toISOString()
        },
        {
          id: "fallback-level-4",
          name: "第4关 - 数字王国",
          difficulty: 4,
          description: "掌握数字表达",
          isUnlocked: false,
          isCompleted: false,
          createdAt: (/* @__PURE__ */ new Date()).toISOString()
        }
      ];
    };
    const refreshlevel = async () => {
      await loadlevel();
    };
    const goToDebug = () => {
      common_vendor.index.navigateTo({
        url: "/pages/debug/index"
      });
    };
    const checkDailyShareReward = (userId) => {
      try {
        const today = (/* @__PURE__ */ new Date()).toDateString();
        const storageKey = `daily_share_reward_${userId}_${today}`;
        const hasSharedToday = common_vendor.index.getStorageSync(storageKey);
        console.log(`检查每日分享奖励状态 - 用户: ${userId}, 日期: ${today}, 已分享: ${!!hasSharedToday}`);
        return !!hasSharedToday;
      } catch (error) {
        console.error("检查每日分享奖励状态失败:", error);
        return false;
      }
    };
    const markDailyShareReward = (userId) => {
      try {
        const today = (/* @__PURE__ */ new Date()).toDateString();
        const storageKey = `daily_share_reward_${userId}_${today}`;
        common_vendor.index.setStorageSync(storageKey, true);
        console.log(`标记每日分享奖励完成 - 用户: ${userId}, 日期: ${today}`);
      } catch (error) {
        console.error("标记每日分享奖励失败:", error);
      }
    };
    const handleShareReward = async (options) => {
      var _a;
      try {
        if (!((_a = userInfo.value) == null ? void 0 : _a.id)) {
          console.warn("用户信息不存在，无法获取分享奖励");
          return;
        }
        if (isHandlingShareReward) {
          console.log("分享奖励正在处理中，跳过重复请求");
          return;
        }
        const hasSharedToday = checkDailyShareReward(userInfo.value.id);
        if (hasSharedToday) {
          console.log("今日已获取过分享奖励，跳过本次请求");
          common_vendor.index.showToast({
            title: "今日已获得分享奖励",
            icon: "none",
            duration: 2e3
          });
          return;
        }
        isHandlingShareReward = true;
        console.log("开始处理分享奖励:", options);
        setTimeout(async () => {
          try {
            const shareResponse = await api_weixin.weixinApi.shareForReward();
            if (shareResponse.status === "success") {
              markDailyShareReward(userInfo.value.id);
              await loadDailyStatus();
              common_vendor.index.showModal({
                title: "分享奖励",
                content: `${shareResponse.message}今日分享奖励已领取完毕。`,
                showCancel: false,
                confirmText: "太棒了",
                success: () => {
                  console.log("分享奖励提示已显示");
                }
              });
              console.log("分享奖励获取成功:", shareResponse);
            } else if (shareResponse.status === "already_shared") {
              console.log("今日已分享过:", shareResponse.message);
              markDailyShareReward(userInfo.value.id);
              common_vendor.index.showToast({
                title: shareResponse.message,
                icon: "none",
                duration: 2e3
              });
            } else {
              console.log("分享奖励获取失败:", shareResponse.message);
              common_vendor.index.showToast({
                title: shareResponse.message || "分享失败，请重试",
                icon: "none",
                duration: 2e3
              });
            }
          } catch (error) {
            console.error("获取分享奖励失败:", error);
          } finally {
            isHandlingShareReward = false;
            console.log("分享奖励处理完成，重置状态");
          }
        }, 2e3);
      } catch (error) {
        console.error("处理分享奖励失败:", error);
        isHandlingShareReward = false;
      }
    };
    common_vendor.onShareAppMessage((options) => {
      var _a;
      const promise = utils_share.shareUtils.handleShareAppMessage(options, {
        page: "pages/index/index",
        userId: (_a = userInfo.value) == null ? void 0 : _a.id
      });
      if (options.from === "menu") {
        handleShareReward(options);
      }
      return {
        title: "趣护消消乐",
        path: "/pages/index/index",
        imageUrl: "",
        promise
      };
    });
    return (_ctx, _cache) => {
      var _a, _b, _c, _d, _e, _f;
      return common_vendor.e({
        a: userInfo.value
      }, userInfo.value ? common_vendor.e({
        b: common_vendor.t(userInfo.value.unlockedLevels || 0),
        c: common_vendor.t(((_a = userInfo.value.completedLevelIds) == null ? void 0 : _a.length) || 0),
        d: common_vendor.o(goToFavorites),
        e: userStarStats.value
      }, userStarStats.value ? {
        f: common_vendor.t(userStarStats.value.totalStars),
        g: common_vendor.t(userStarStats.value.threeStarLevels),
        h: common_vendor.t(userStarStats.value.completedLevels)
      } : {}, {
        i: dailyStatus.value && !dailyStatus.value.isVip
      }, dailyStatus.value && !dailyStatus.value.isVip ? common_vendor.e({
        j: common_vendor.t(dailyStatus.value.dailyUnlockCount),
        k: common_vendor.t(dailyStatus.value.dailyUnlockLimit),
        l: dailyStatus.value.remainingUnlocks > 0
      }, dailyStatus.value.remainingUnlocks > 0 ? {
        m: common_vendor.t(dailyStatus.value.remainingUnlocks)
      } : {}, {
        n: !dailyStatus.value.dailyShared && dailyStatus.value.remainingUnlocks <= 3
      }, !dailyStatus.value.dailyShared && dailyStatus.value.remainingUnlocks <= 3 ? {} : {}) : {}, {
        o: dailyStatus.value && dailyStatus.value.isVip
      }, dailyStatus.value && dailyStatus.value.isVip ? {} : {}, {
        p: !((_b = dailyStatus.value) == null ? void 0 : _b.isVip)
      }, !((_c = dailyStatus.value) == null ? void 0 : _c.isVip) ? {
        q: common_vendor.o(showVipPackages)
      } : {}) : {}, {
        r: common_vendor.t(lastLevelInfo.value ? "继续上次的游戏进度" : "选择关卡开始你的学习之旅"),
        s: common_vendor.t(((_d = userInfo.value) == null ? void 0 : _d.unlockedLevels) || 0),
        t: common_vendor.t(((_f = (_e = userInfo.value) == null ? void 0 : _e.completedLevelIds) == null ? void 0 : _f.length) || 0),
        v: lastLevelInfo.value
      }, lastLevelInfo.value ? {
        w: common_vendor.t(lastLevelInfo.value.name || "第" + lastLevelInfo.value.id + "关")
      } : {}, {
        x: common_vendor.t(lastLevelInfo.value ? "继续游戏" : "选择关卡"),
        y: common_vendor.o(startGame),
        z: common_vendor.p({
          type: "help",
          color: "#666",
          size: "20"
        }),
        A: common_vendor.o(showHelp),
        B: common_vendor.o(goToTagChallenge),
        C: common_vendor.o(goToMemberCenter),
        D: common_vendor.o(showSettings),
        E: common_vendor.o(closeSettings),
        F: common_vendor.o(handleSettingsChange),
        G: common_vendor.p({
          visible: showSettingsModal.value
        }),
        H: isDevelopment.value
      }, isDevelopment.value ? {
        I: common_vendor.o(goToDebug)
      } : {});
    };
  }
});
_sfc_defineComponent.__runtimeHooks = 2;
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_defineComponent, [["__scopeId", "data-v-83a5a03c"]]);
wx.createPage(MiniProgramPage);
