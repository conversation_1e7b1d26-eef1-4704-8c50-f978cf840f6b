{
  "easycom": {
    "autoscan": true,
    "custom": {
      // uni-ui 规则如下配置
      "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"
    }
  },
  "pages": [
    //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "趣护消消乐"
      }
    },
    {
      "path": "pages/game/index",
      "style": {
        "navigationBarTitleText": "趣护消消乐"
      }
    },
    {
      "path": "pages/debug/index",
      "style": {
        "navigationBarTitleText": "API调试"
      }
    },
    {
      "path": "pages/webview/index",
      "style": {
        "navigationBarTitleText": "加载中...",
        "navigationBarBackgroundColor": "#667eea",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/test-audio/index",
      "style": {
        "navigationBarTitleText": "音频测试",
        "navigationBarBackgroundColor": "#667eea",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/level-selection/index",
      "style": {
        "navigationBarTitleText": "选择关卡",
        "navigationBarBackgroundColor": "#667eea",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/member-center/index",
      "style": {
        "navigationBarTitleText": "会员中心",
        "navigationBarBackgroundColor": "#6c65c8",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/help/index",
      "style": {
        "navigationBarTitleText": "帮助"
      }
    },
    {
      "path": "pages/login/index",
      "style": {
        "navigationBarTitleText": "登录",
        "navigationBarBackgroundColor": "#667eea",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/tag-challenge/index",
      "style": {
        "navigationBarTitleText": "标签闯关",
        "navigationBarBackgroundColor": "#667eea",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/tag-levels/index",
      "style": {
        "navigationBarTitleText": "标签关卡",
        "navigationBarBackgroundColor": "#667eea",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/activation-code/index",
      "style": {
        "navigationBarTitleText": "激活码兑换",
        "navigationBarBackgroundColor": "#667eea",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/favorites/index",
      "style": {
        "navigationBarTitleText": "我的收藏",
        "navigationBarBackgroundColor": "#667eea",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/tag-selection/index",
      "style": {
        "navigationBarTitleText": "标签挑战",
        "navigationBarBackgroundColor": "#667eea",
        "navigationBarTextStyle": "white"
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "趣护消消乐",
    "navigationBarBackgroundColor": "#fdf9f9",
    "backgroundColor": "#fdf9f9"
  }
}
