
.tag-levels-container.data-v-2ed8a332 {
  min-height: 100vh;
  background: #fdf9f9;
  padding: 32rpx 24rpx;
}

/* 标签信息头部 */
.tag-header.data-v-2ed8a332 {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(102, 126, 234, 0.15);
}
.tag-info.data-v-2ed8a332 {
  margin-bottom: 20rpx;
}
.tag-name.data-v-2ed8a332 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #1a202c;
  margin-bottom: 8rpx;
  letter-spacing: 0.5rpx;
}
.tag-desc.data-v-2ed8a332 {
  display: block;
  font-size: 26rpx;
  color: #4a5568;
  line-height: 1.4;
  font-weight: 400;
}
.tag-stats.data-v-2ed8a332 {
  display: flex;
  gap: 24rpx;
}
.stats-text.data-v-2ed8a332 {
  font-size: 24rpx;
  color: #667eea;
  font-weight: 500;
}

/* 加载和错误状态 */
.loading-container.data-v-2ed8a332, .error-container.data-v-2ed8a332 {
  text-align: center;
  padding: 80rpx 0;
}
.loading-spinner.data-v-2ed8a332 {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(17, 17, 17, 0.3);
  border-top: 4rpx solid #111;
  border-radius: 50%;
  animation: spin-2ed8a332 1s linear infinite;
  margin: 0 auto 24rpx;
}
@keyframes spin-2ed8a332 {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.loading-text.data-v-2ed8a332, .error-title.data-v-2ed8a332, .error-text.data-v-2ed8a332 {
  color: #111;
  font-size: 28rpx;
  margin-bottom: 16rpx;
}
.error-icon.data-v-2ed8a332 {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}
.retry-btn.data-v-2ed8a332 {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: #ffffff;
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  border: none;
  font-size: 26rpx;
  box-shadow: 0 8rpx 20rpx rgba(116, 185, 255, 0.3);
}
.retry-text.data-v-2ed8a332 {
  color: inherit;
}

/* 关卡列表样式 */
.levels-container.data-v-2ed8a332 {
  flex: 1;
}
.levels-grid.data-v-2ed8a332 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24rpx;
}
.level-card.data-v-2ed8a332 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}
.level-card.data-v-2ed8a332:active {
  transform: scale(0.98);
  opacity: 0.9;
}
.level-locked.data-v-2ed8a332 {
  opacity: 0.6;
}
.level-number.data-v-2ed8a332 {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 28rpx;
  font-weight: bold;
  flex-shrink: 0;
}
.level-info.data-v-2ed8a332 {
  flex: 1;
}
.level-name.data-v-2ed8a332 {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 8rpx;
}
.level-desc.data-v-2ed8a332 {
  display: block;
  font-size: 24rpx;
  color: #718096;
  margin-bottom: 12rpx;
}

/* 关卡标签样式 */
.level-tags.data-v-2ed8a332 {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}
.tag-item.data-v-2ed8a332 {
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  background: #f0f0f0;
}
.tag-vip.data-v-2ed8a332 {
  background: linear-gradient(135deg, #ffd700, #ffb347);
}
.tag-text.data-v-2ed8a332 {
  font-size: 20rpx;
  color: #666;
}
.tag-vip .tag-text.data-v-2ed8a332 {
  color: #8b4513;
  font-weight: 500;
}
.tag-vip-icon.data-v-2ed8a332 {
  font-size: 16rpx;
}

/* 关卡状态样式 */
.level-status.data-v-2ed8a332 {
  flex-shrink: 0;
}
.status-badge.data-v-2ed8a332 {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  text-align: center;
}
.status-badge.locked.data-v-2ed8a332 {
  background: #f0f0f0;
}
.status-badge.completed.data-v-2ed8a332 {
  background: #d1f2eb;
}
.status-badge.available.data-v-2ed8a332 {
  background: #e3f2fd;
}
.status-text.data-v-2ed8a332 {
  font-size: 22rpx;
  font-weight: 500;
}

/* 星级显示样式 */
.level-stars.data-v-2ed8a332 {
  display: flex;
  align-items: center;
  gap: 4rpx;
}
.star.data-v-2ed8a332 {
  font-size: 20rpx;
  color: #ddd;
  transition: color 0.2s;
}
.star-filled.data-v-2ed8a332 {
  color: #ffd700;
}

/* 底部按钮 */
.bottom-actions.data-v-2ed8a332 {
  display: flex;
  justify-content: center;
  padding-top: 32rpx;
}
.back-btn.data-v-2ed8a332 {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: #ffffff;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
  box-shadow: 0 8rpx 20rpx rgba(116, 185, 255, 0.3);
}
.back-btn.data-v-2ed8a332:active {
  transform: scale(0.98);
  opacity: 0.9;
}
.back-text.data-v-2ed8a332 {
  color: inherit;
}

/* 收藏按钮样式 */
.favorite-btn.data-v-2ed8a332 {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transition: all 0.2s;
}
.favorite-btn.data-v-2ed8a332:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 1);
}
.favorite-icon.data-v-2ed8a332 {
  font-size: 24rpx;
  transition: all 0.2s;
}
.favorite-icon.favorited.data-v-2ed8a332 {
  animation: heartbeat-2ed8a332 0.6s ease-in-out;
}
@keyframes heartbeat-2ed8a332 {
0% { transform: scale(1);
}
50% { transform: scale(1.2);
}
100% { transform: scale(1);
}
}
