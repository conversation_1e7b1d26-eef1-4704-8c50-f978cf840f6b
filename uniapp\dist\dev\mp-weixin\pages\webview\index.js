"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const url = common_vendor.ref("");
    const title = common_vendor.ref("");
    const isLoading = common_vendor.ref(true);
    const error = common_vendor.ref("");
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      console.log("WebView页面参数:", options);
      if (options.url) {
        url.value = decodeURIComponent(options.url);
      }
      if (options.title) {
        title.value = decodeURIComponent(options.title);
        common_vendor.index.setNavigationBarTitle({
          title: title.value
        });
      }
      if (!url.value) {
        error.value = "缺少URL参数";
        isLoading.value = false;
        return;
      }
      if (!isValidUrl(url.value)) {
        error.value = "无效的URL格式";
        isLoading.value = false;
        return;
      }
      console.log("准备加载URL:", url.value);
    });
    const isValidUrl = (urlString) => {
      try {
        const urlObj = new URL(urlString);
        return urlObj.protocol === "http:" || urlObj.protocol === "https:";
      } catch {
        return false;
      }
    };
    const handleLoad = (event) => {
      console.log("WebView加载完成:", event);
      isLoading.value = false;
      error.value = "";
    };
    const handleError = (event) => {
      console.error("WebView加载错误:", event);
      isLoading.value = false;
      error.value = "页面加载失败，请检查网络连接";
    };
    const handleMessage = (event) => {
      console.log("收到WebView消息:", event);
    };
    const retryLoad = () => {
      if (!url.value) {
        error.value = "缺少URL参数";
        return;
      }
      isLoading.value = true;
      error.value = "";
      setTimeout(() => {
        if (isLoading.value) {
          isLoading.value = false;
          error.value = "加载超时，请重试";
        }
      }, 1e4);
    };
    const goBack = () => {
      common_vendor.index.navigateBack({
        delta: 1
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: isLoading.value
      }, isLoading.value ? {} : {}, {
        b: error.value
      }, error.value ? {
        c: common_vendor.t(error.value),
        d: common_vendor.o(retryLoad),
        e: common_vendor.o(goBack)
      } : {}, {
        f: !isLoading.value && !error.value && url.value
      }, !isLoading.value && !error.value && url.value ? {
        g: url.value,
        h: common_vendor.o(handleMessage),
        i: common_vendor.o(handleLoad),
        j: common_vendor.o(handleError)
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f397c225"]]);
wx.createPage(MiniProgramPage);
