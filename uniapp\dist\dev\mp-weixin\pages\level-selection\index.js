"use strict";
const common_vendor = require("../../common/vendor.js");
const api_weixin = require("../../api/weixin.js");
const utils_audio = require("../../utils/audio.js");
const api_utils = require("../../api/utils.js");
const utils_auth = require("../../utils/auth.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const levelState = common_vendor.reactive({
      level: [],
      currentLevel: null,
      ...api_utils.createLoadingState()
    });
    const userInfo = common_vendor.ref(null);
    const dailyStatus = common_vendor.ref(null);
    common_vendor.ref(null);
    const scrollToLevelId = common_vendor.ref(null);
    const levels = common_vendor.computed(() => {
      return levelState.level.map((level, index) => {
        const extendedLevel = level;
        return {
          id: level.id,
          levelNumber: String(index + 1).padStart(2, "0"),
          name: level.name,
          description: level.description,
          library: getLibraryForLevel(level),
          locked: !level.isUnlocked,
          completed: level.isCompleted,
          difficulty: level.difficulty,
          maxStars: extendedLevel.userStars || 0,
          tags: extendedLevel.tags || [],
          // 使用 tagIds 字段
          isFavorited: extendedLevel.isFavorited || false
          // 添加收藏状态
        };
      });
    });
    common_vendor.computed(() => {
      return levels.value.some((level) => level.locked);
    });
    const libraries = [
      {
        name: "基础词汇",
        words: [
          { english: "apple", chinese: "苹果" },
          { english: "book", chinese: "书" },
          { english: "cat", chinese: "猫" },
          { english: "dog", chinese: "狗" },
          { english: "egg", chinese: "鸡蛋" },
          { english: "fish", chinese: "鱼" },
          { english: "girl", chinese: "女孩" },
          { english: "hat", chinese: "帽子" }
        ]
      },
      {
        name: "动物世界",
        words: [
          { english: "elephant", chinese: "大象" },
          { english: "tiger", chinese: "老虎" },
          { english: "lion", chinese: "狮子" },
          { english: "monkey", chinese: "猴子" },
          { english: "rabbit", chinese: "兔子" },
          { english: "bird", chinese: "鸟" },
          { english: "horse", chinese: "马" },
          { english: "sheep", chinese: "羊" }
        ]
      },
      {
        name: "颜色彩虹",
        words: [
          { english: "red", chinese: "红色" },
          { english: "blue", chinese: "蓝色" },
          { english: "green", chinese: "绿色" },
          { english: "yellow", chinese: "黄色" },
          { english: "purple", chinese: "紫色" },
          { english: "orange", chinese: "橙色" },
          { english: "black", chinese: "黑色" },
          { english: "white", chinese: "白色" }
        ]
      },
      {
        name: "数字王国",
        words: [
          { english: "one", chinese: "一" },
          { english: "two", chinese: "二" },
          { english: "three", chinese: "三" },
          { english: "four", chinese: "四" },
          { english: "five", chinese: "五" },
          { english: "six", chinese: "六" },
          { english: "seven", chinese: "七" },
          { english: "eight", chinese: "八" }
        ]
      }
    ];
    common_vendor.onLoad(async (options) => {
      const isLoggedIn = await utils_auth.checkLoginAndRedirect({
        toastMessage: "请先登录以选择关卡",
        redirectUrl: "/pages/login/index?redirect=" + encodeURIComponent("/pages/level-selection/index")
      });
      if (!isLoggedIn) {
        return;
      }
      if (options == null ? void 0 : options.scrollToLevel) {
        scrollToLevelId.value = options.scrollToLevel;
        console.log("需要滚动到关卡:", scrollToLevelId.value);
      }
    });
    common_vendor.onMounted(async () => {
      await initializePage();
      if (scrollToLevelId.value) {
        await common_vendor.nextTick$1();
        scrollToLevel(scrollToLevelId.value);
      }
    });
    const initializePage = async () => {
      await api_utils.withLoading(levelState, async () => {
        await loadUserInfo();
        await loadLevels();
        await loadDailyStatus();
      }, {
        errorMessage: "页面加载失败，请重试"
      });
    };
    const loadUserInfo = async () => {
      try {
        const info = await api_weixin.weixinApi.getUserInfo();
        userInfo.value = info;
        console.log("用户信息加载成功:", info);
      } catch (error) {
        console.error("加载用户信息失败:", error);
      }
    };
    const loadLevels = async () => {
      try {
        try {
          const extendedLevels = await api_weixin.weixinApi.getExtendedLevels();
          levelState.level = extendedLevels;
          console.log("扩展关卡列表加载成功:", extendedLevels);
          return;
        } catch (extendedError) {
          console.warn("扩展关卡API不可用，使用基础API:", extendedError);
        }
        const apiLevels = await api_weixin.weixinApi.getLevels();
        levelState.level = apiLevels;
        console.log("基础关卡列表加载成功:", apiLevels);
      } catch (error) {
        console.error("加载关卡列表失败:", error);
        console.log("使用本地备用关卡数据");
        levelState.level = createFallbackLevels();
      }
    };
    const loadDailyStatus = async () => {
      try {
        const status = await api_weixin.weixinApi.getDailyStatus();
        dailyStatus.value = status;
        console.log("每日状态加载成功:", status);
      } catch (error) {
        console.error("加载每日状态失败:", error);
      }
    };
    const createFallbackLevels = () => {
      return [
        {
          id: "fallback-level-1",
          name: "第1关 - 基础词汇",
          difficulty: 1,
          description: "小学基础词汇练习",
          isUnlocked: true,
          isCompleted: false,
          createdAt: (/* @__PURE__ */ new Date()).toISOString()
        },
        {
          id: "fallback-level-2",
          name: "第2关 - 动物世界",
          difficulty: 2,
          description: "认识可爱的动物",
          isUnlocked: true,
          isCompleted: false,
          createdAt: (/* @__PURE__ */ new Date()).toISOString()
        },
        {
          id: "fallback-level-3",
          name: "第3关 - 颜色彩虹",
          difficulty: 3,
          description: "学习各种颜色",
          isUnlocked: false,
          isCompleted: false,
          createdAt: (/* @__PURE__ */ new Date()).toISOString()
        },
        {
          id: "fallback-level-4",
          name: "第4关 - 数字王国",
          difficulty: 4,
          description: "掌握数字表达",
          isUnlocked: false,
          isCompleted: false,
          createdAt: (/* @__PURE__ */ new Date()).toISOString()
        }
      ];
    };
    const getLibraryForLevel = (level) => {
      const difficultyIndex = (level.difficulty - 1) % libraries.length;
      return libraries[difficultyIndex];
    };
    const selectLevel = async (level) => {
      if (level.locked) {
        utils_audio.audioManager.playSoundEffect("fail");
        await checkCanUnlock();
        return;
      }
      utils_audio.audioManager.playSoundEffect("click");
      console.log("Selected level:", level);
      common_vendor.index.setStorageSync("selectedLibrary", JSON.stringify(level.library));
      common_vendor.index.setStorageSync("selectedLevel", JSON.stringify(level));
      common_vendor.index.navigateTo({
        url: "/pages/game/index"
      });
    };
    const scrollToLevel = (levelId) => {
      try {
        console.log("开始滚动到关卡:", levelId);
        const targetIndex = levels.value.findIndex((level) => level.id.toString() === levelId.toString());
        if (targetIndex === -1) {
          console.warn("未找到目标关卡:", levelId);
          return;
        }
        console.log("找到目标关卡索引:", targetIndex);
        const scrollTop = targetIndex * 220;
        common_vendor.index.pageScrollTo({
          scrollTop,
          duration: 800,
          success: () => {
            console.log("滚动到关卡成功:", levelId);
            setTimeout(() => {
              common_vendor.index.showToast({
                title: `已定位到第${targetIndex + 1}关`,
                icon: "none",
                duration: 1500
              });
            }, 500);
          },
          fail: (error) => {
            console.error("滚动到关卡失败:", error);
          }
        });
      } catch (error) {
        console.error("滚动到关卡出错:", error);
      }
    };
    const checkCanUnlock = async (level) => {
      try {
        await loadDailyStatus();
        const status = dailyStatus.value;
        if (!status) {
          common_vendor.index.showToast({
            title: "该关卡尚未解锁",
            icon: "none",
            duration: 1500
          });
          return;
        }
        if (status.isVip) {
          common_vendor.index.showToast({
            title: "该关卡尚未解锁",
            icon: "none",
            duration: 1500
          });
          return;
        }
        if (status.remainingUnlocks > 0) {
          common_vendor.index.showModal({
            title: "解锁关卡",
            content: `是否使用1次解锁机会来解锁这个关卡？
剩余解锁次数：${status.remainingUnlocks}`,
            confirmText: "解锁",
            cancelText: "取消",
            success: async (res) => {
              if (res.confirm) {
                common_vendor.index.showToast({
                  title: "关卡解锁成功！",
                  icon: "success"
                });
                await loadLevels();
              }
            }
          });
        } else {
          common_vendor.index.showModal({
            title: "解锁次数不足",
            content: "今日解锁次数已用完\n• 升级VIP可无限解锁\n• 分享游戏可获得额外解锁机会",
            confirmText: "升级VIP",
            cancelText: "分享获取",
            success: (res) => {
              if (res.confirm) {
                showVipPackages();
              } else {
                common_vendor.index.showToast({
                  title: "请分享游戏给好友",
                  icon: "none"
                });
              }
            }
          });
        }
      } catch (error) {
        console.error("检查解锁状态失败:", error);
        common_vendor.index.showToast({
          title: "该关卡尚未解锁",
          icon: "none",
          duration: 1500
        });
      }
    };
    const showVipPackages = () => {
      common_vendor.index.showToast({
        title: "VIP功能开发中",
        icon: "none"
      });
    };
    return (_ctx, _cache) => {
      var _a;
      return common_vendor.e({
        a: userInfo.value
      }, userInfo.value ? {
        b: common_vendor.t(userInfo.value.unlockedLevels || 0),
        c: common_vendor.t(((_a = userInfo.value.completedLevelIds) == null ? void 0 : _a.length) || 0),
        d: common_vendor.t(levels.value.length)
      } : {}, {
        e: levelState.isLoading
      }, levelState.isLoading ? {} : levelState.error ? {
        g: common_vendor.t(levelState.error),
        h: common_vendor.o(loadLevels)
      } : {
        i: common_vendor.f(levels.value, (level, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(level.levelNumber),
            b: common_vendor.t(level.name),
            c: common_vendor.t(level.description),
            d: level.locked
          }, level.locked ? {} : level.completed ? {
            f: common_vendor.f(level.maxStars || 0, (star, k1, i1) => {
              return {
                a: star
              };
            })
          } : {}, {
            e: level.completed,
            g: level.tags && level.tags.length > 0
          }, level.tags && level.tags.length > 0 ? common_vendor.e({
            h: common_vendor.f(level.tags.slice(0, 3), (tag, k1, i1) => {
              return common_vendor.e({
                a: common_vendor.t(tag.name),
                b: tag.isVip
              }, tag.isVip ? {} : {}, {
                c: tag.id,
                d: tag.isVip ? 1 : "",
                e: tag.color || "#f0f0f0"
              });
            }),
            i: level.tags.length > 3
          }, level.tags.length > 3 ? {
            j: common_vendor.t(level.tags.length - 3)
          } : {}) : {}, {
            k: level.id,
            l: level.locked ? 1 : "",
            m: level.completed ? 1 : "",
            n: common_vendor.o(($event) => selectLevel(level), level.id)
          });
        })
      }, {
        f: levelState.error
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-5578dee7"]]);
wx.createPage(MiniProgramPage);
