<template>
  <view class="test-audio-container">
    <view class="header">
      <text class="title">音频功能测试</text>
      <text class="subtitle">测试新的 InnerAudioContext API</text>
    </view>

    <!-- 播放状态显示 -->
    <view class="status-card">
      <text class="status-title">播放状态</text>
      <view class="status-info">
        <text class="status-item">正在播放: {{ playStatus.isPlaying ? '是' : '否' }}</text>
        <text class="status-item">已暂停: {{ playStatus.isPaused ? '是' : '否' }}</text>
        <text class="status-item">音乐类型: {{ playStatus.musicType || '无' }}</text>
        <text class="status-item">音频源: {{ playStatus.currentSrc || '无' }}</text>
      </view>
    </view>

    <!-- 背景音乐控制 -->
    <view class="control-section">
      <text class="section-title">🎵 背景音乐控制</text>
      <view class="button-grid">
        <button class="control-btn primary" @click="playMainMusic">播放主页音乐</button>
        <button class="control-btn primary" @click="playGameMusic">播放游戏音乐</button>
        <button class="control-btn primary" @click="playMenuMusic">播放菜单音乐</button>
        <button class="control-btn secondary" @click="pauseMusic">暂停音乐</button>
        <button class="control-btn secondary" @click="resumeMusic">恢复音乐</button>
        <button class="control-btn danger" @click="stopMusic">停止音乐</button>
      </view>
    </view>

    <!-- 音效测试 -->
    <view class="control-section">
      <text class="section-title">🔊 音效测试</text>
      <view class="button-grid">
        <button class="control-btn effect" @click="playClickSound">点击音效</button>
        <button class="control-btn effect" @click="playSuccessSound">成功音效</button>
        <button class="control-btn effect" @click="playFailSound">失败音效</button>
        <button class="control-btn effect" @click="playUnlockSound">解锁音效</button>
        <button class="control-btn effect" @click="playCompleteSound">完成音效</button>
      </view>
    </view>

    <!-- 震动测试 -->
    <view class="control-section">
      <text class="section-title">📳 震动测试</text>
      <view class="button-grid">
        <button class="control-btn vibrate" @click="shortVibrate">短震动</button>
        <button class="control-btn vibrate" @click="longVibrate">长震动</button>
      </view>
    </view>

    <!-- 设置控制 -->
    <view class="control-section">
      <text class="section-title">⚙️ 设置控制</text>
      <view class="settings-list">
        <view class="setting-item">
          <text class="setting-label">背景音乐</text>
          <switch 
            :checked="settings.backgroundMusic" 
            @change="toggleBackgroundMusic"
            color="#667eea"
          />
        </view>
        <view class="setting-item">
          <text class="setting-label">音效</text>
          <switch 
            :checked="settings.soundEffects" 
            @change="toggleSoundEffects"
            color="#667eea"
          />
        </view>
        <view class="setting-item">
          <text class="setting-label">震动</text>
          <switch 
            :checked="settings.vibration" 
            @change="toggleVibration"
            color="#667eea"
          />
        </view>
      </view>
    </view>

    <!-- 全局配置测试 -->
    <view class="control-section">
      <text class="section-title">🌐 全局配置测试</text>
      <view class="config-info">
        <text class="config-item">主页音乐URL: {{ globalMusicUrls.main }}</text>
        <text class="config-item">游戏音乐URL: {{ globalMusicUrls.game }}</text>
        <text class="config-item">菜单音乐URL: {{ globalMusicUrls.menu }}</text>
      </view>
      <view class="button-grid">
        <button class="control-btn config" @click="testGlobalConfig">测试全局配置音乐</button>
        <button class="control-btn config" @click="refreshConfig">刷新配置</button>
      </view>
    </view>

    <!-- 返回按钮 -->
    <view class="back-btn-container">
      <button class="back-btn" @click="goBack">
        <text class="back-text">返回</text>
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { audioManager } from '../../utils/audio'
import { useGlobalConfig } from '../../composables/useGlobalConfig'
import type { GameSettings } from '../../api/types'

// 播放状态
const playStatus = ref({
  isPlaying: false,
  isPaused: false,
  currentSrc: '',
  musicType: ''
})

// 设置状态
const settings = ref<GameSettings>({
  backgroundMusic: true,
  soundEffects: true,
  vibration: true
})

// 全局配置
const { 
  getBackgroundMusicUrl,
  initializeGlobalConfig 
} = useGlobalConfig()

// 全局音乐URL
const globalMusicUrls = ref({
  main: '',
  game: '',
  menu: ''
})

// 状态更新定时器
let statusTimer: number | null = null

/**
 * 页面初始化
 */
onMounted(async () => {
  // 初始化全局配置
  await initializeGlobalConfig()
  
  // 加载设置
  loadSettings()
  
  // 更新全局音乐URL
  updateGlobalMusicUrls()
  
  // 启动状态更新定时器
  startStatusTimer()
})

/**
 * 页面销毁
 */
onUnmounted(() => {
  if (statusTimer) {
    clearInterval(statusTimer)
  }
})

/**
 * 加载设置
 */
const loadSettings = () => {
  settings.value = audioManager.getSettings()
}

/**
 * 更新全局音乐URL
 */
const updateGlobalMusicUrls = () => {
  globalMusicUrls.value = {
    main: getBackgroundMusicUrl('main'),
    game: getBackgroundMusicUrl('game'),
    menu: getBackgroundMusicUrl('menu')
  }
}

/**
 * 启动状态更新定时器
 */
const startStatusTimer = () => {
  statusTimer = setInterval(() => {
    playStatus.value = audioManager.getPlayStatus()
  }, 500) as any
}

/**
 * 播放主页音乐
 */
const playMainMusic = () => {
  audioManager.playBackgroundMusic('main')
}

/**
 * 播放游戏音乐
 */
const playGameMusic = () => {
  audioManager.playBackgroundMusic('game')
}

/**
 * 播放菜单音乐
 */
const playMenuMusic = () => {
  audioManager.playBackgroundMusic('menu')
}

/**
 * 暂停音乐
 */
const pauseMusic = () => {
  audioManager.pauseBackgroundMusic()
}

/**
 * 恢复音乐
 */
const resumeMusic = () => {
  audioManager.resumeBackgroundMusic()
}

/**
 * 停止音乐
 */
const stopMusic = () => {
  audioManager.stopBackgroundMusic()
}

/**
 * 播放点击音效
 */
const playClickSound = () => {
  audioManager.playSoundEffect('click')
}

/**
 * 播放成功音效
 */
const playSuccessSound = () => {
  audioManager.playSoundEffect('success')
}

/**
 * 播放失败音效
 */
const playFailSound = () => {
  audioManager.playSoundEffect('fail')
}

/**
 * 播放解锁音效
 */
const playUnlockSound = () => {
  audioManager.playSoundEffect('unlock')
}

/**
 * 播放完成音效
 */
const playCompleteSound = () => {
  audioManager.playSoundEffect('complete')
}

/**
 * 短震动
 */
const shortVibrate = () => {
  audioManager.vibrate('short')
}

/**
 * 长震动
 */
const longVibrate = () => {
  audioManager.vibrate('long')
}

/**
 * 切换背景音乐
 */
const toggleBackgroundMusic = (event: any) => {
  const value = event.detail.value
  audioManager.updateSettings({ backgroundMusic: value })
  loadSettings()
}

/**
 * 切换音效
 */
const toggleSoundEffects = (event: any) => {
  const value = event.detail.value
  audioManager.updateSettings({ soundEffects: value })
  loadSettings()
}

/**
 * 切换震动
 */
const toggleVibration = (event: any) => {
  const value = event.detail.value
  audioManager.updateSettings({ vibration: value })
  loadSettings()
}

/**
 * 测试全局配置音乐
 */
const testGlobalConfig = () => {
  const mainUrl = getBackgroundMusicUrl('main')
  audioManager.playBackgroundMusic('main', mainUrl)
}

/**
 * 刷新配置
 */
const refreshConfig = async () => {
  await initializeGlobalConfig()
  updateGlobalMusicUrls()
  
  uni.showToast({
    title: '配置已刷新',
    icon: 'success'
  })
}

/**
 * 返回上一页
 */
const goBack = () => {
  uni.navigateBack({
    delta: 1
  })
}
</script>

<style lang="scss" scoped>
.test-audio-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea, #764ba2);
  padding: 40rpx 20rpx;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.subtitle {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.status-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.status-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 20rpx;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.status-item {
  font-size: 24rpx;
  color: #4a5568;
  line-height: 1.4;
}

.control-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 20rpx;
}

.button-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.control-btn {
  flex: 1;
  min-width: 200rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s;
}

.control-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #ffffff;
}

.control-btn.secondary {
  background: #f8f9fa;
  color: #495057;
  border: 2rpx solid #dee2e6;
}

.control-btn.danger {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: #ffffff;
}

.control-btn.effect {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  color: #ffffff;
}

.control-btn.vibrate {
  background: linear-gradient(135deg, #ffa726, #ff7043);
  color: #ffffff;
}

.control-btn.config {
  background: linear-gradient(135deg, #ab47bc, #8e24aa);
  color: #ffffff;
}

.control-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.settings-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #e2e8f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 26rpx;
  color: #2d3748;
  font-weight: 500;
}

.config-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.config-item {
  font-size: 22rpx;
  color: #4a5568;
  line-height: 1.4;
  word-break: break-all;
}

.back-btn-container {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
}

.back-btn {
  background: rgba(0, 0, 0, 0.3);
  color: #ffffff;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s;
}

.back-btn:active {
  background: rgba(0, 0, 0, 0.5);
  transform: scale(0.95);
}

.back-text {
  color: #ffffff;
}
</style>
