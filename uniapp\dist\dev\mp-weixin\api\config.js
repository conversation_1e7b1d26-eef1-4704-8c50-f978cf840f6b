"use strict";
const utils_env = require("../utils/env.js");
const API_CONFIG = {
  // 请求超时时间（毫秒）
  TIMEOUT: 1e4,
  // 重试次数
  RETRY_COUNT: 3,
  // 重试延迟（毫秒）
  RETRY_DELAY: 1e3
};
const getWeixinApiUrl = (path) => {
  return utils_env.getApiUrl(path);
};
const HTTP_STATUS = {
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500
};
const ERROR_MESSAGES = {
  NETWORK_ERROR: "网络连接失败，请检查网络设置",
  TIMEOUT_ERROR: "请求超时，请稍后重试",
  SERVER_ERROR: "服务器错误，请稍后重试",
  UNAUTHORIZED: "未授权访问，请重新登录",
  NOT_FOUND: "请求的资源不存在",
  BAD_REQUEST: "请求参数错误",
  UNKNOWN_ERROR: "未知错误，请稍后重试"
};
const STORAGE_KEYS = {
  USER_INFO: "userInfo",
  USER_OPENID: "userOpenid",
  SELECTED_LIBRARY: "selectedLibrary",
  SELECTED_LEVEL: "selectedLevel",
  LEVEL_PROGRESS: "levelProgress"
};
exports.API_CONFIG = API_CONFIG;
exports.ERROR_MESSAGES = ERROR_MESSAGES;
exports.HTTP_STATUS = HTTP_STATUS;
exports.STORAGE_KEYS = STORAGE_KEYS;
exports.getWeixinApiUrl = getWeixinApiUrl;
