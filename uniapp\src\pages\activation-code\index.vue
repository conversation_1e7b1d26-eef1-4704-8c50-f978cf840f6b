<template>
  <view class="activation-code-container">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">激活码兑换</text>
      <text class="page-subtitle">输入激活码获得VIP权限</text>
    </view>

    <!-- 激活码输入区域 -->
    <view class="input-section">
      <view class="input-container">
        <text class="input-label">激活码</text>
        <input 
          v-model="activationCode"
          class="code-input"
          placeholder="请输入激活码"
          maxlength="20"
          :disabled="isRedeeming"
        />
      </view>
      
      <!-- 兑换按钮 -->
      <button 
        class="redeem-btn"
        :class="{ 'redeem-btn-disabled': !canRedeem }"
        :disabled="!canRedeem"
        @click="redeemCode"
      >
        <text class="redeem-btn-text">
          {{ isRedeeming ? '兑换中...' : '立即兑换' }}
        </text>
      </button>
    </view>

    <!-- 兑换说明 -->
    <view class="info-section">
      <view class="info-card">
        <text class="info-title">💎 VIP特权</text>
        <view class="privilege-list">
          <text class="privilege-item">• 无限制解锁关卡</text>
          <text class="privilege-item">• 专享VIP标签关卡</text>
          <text class="privilege-item">• 优先体验新功能</text>
          <text class="privilege-item">• 专属客服支持</text>
        </view>
      </view>

      <view class="info-card">
        <text class="info-title">📝 使用说明</text>
        <view class="instruction-list">
          <text class="instruction-item">1. 输入有效的激活码</text>
          <text class="instruction-item">2. 点击"立即兑换"按钮</text>
          <text class="instruction-item">3. 兑换成功后即可享受VIP特权</text>
          <text class="instruction-item">4. 激活码仅可使用一次</text>
        </view>
      </view>
    </view>

    <!-- 兑换结果弹窗 -->
    <view v-if="showResultModal" class="modal-overlay">
      <view class="modal-content">
        <view class="result-icon">
          <text class="icon">{{ redeemResult.success ? '🎉' : '❌' }}</text>
        </view>
        <text class="result-title">
          {{ redeemResult.success ? '兑换成功！' : '兑换失败' }}
        </text>
        <text class="result-message">{{ redeemResult.message }}</text>
        
        <!-- 成功时显示套餐信息 -->
        <view v-if="redeemResult.success && redeemResult.package" class="package-info">
          <text class="package-name">{{ redeemResult.package.name }}</text>
          <text class="package-desc">{{ redeemResult.package.description }}</text>
        </view>
        
        <button class="modal-btn" @click="closeResultModal">
          <text class="modal-btn-text">确定</text>
        </button>
      </view>
    </view>

    <!-- 底部返回按钮 -->
    <view class="bottom-actions">
      <button class="back-btn" @click="goBack">
        <text class="back-text">返回</text>
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import weixinApi from '../../api/weixin'
import { audioManager } from '../../utils/audio'
import { checkLoginAndRedirect } from '../../utils/auth'
import type { RedeemActivationCodeResponse } from '../../api/types'

// 状态管理
const activationCode = ref('')
const isRedeeming = ref(false)
const showResultModal = ref(false)
const redeemResult = ref<RedeemActivationCodeResponse>({
  success: false,
  message: ''
})

// 计算属性
const canRedeem = computed(() => {
  return activationCode.value.trim().length > 0 && !isRedeeming.value
})

/**
 * 页面加载
 */
onLoad(async () => {
  // 检查登录状态
  const isLoggedIn = await checkLoginAndRedirect({
    toastMessage: '请先登录以兑换激活码',
    redirectUrl: '/pages/login/index'
  })

  if (!isLoggedIn) {
    return
  }
})

/**
 * 兑换激活码
 */
const redeemCode = async () => {
  if (!canRedeem.value) {
    return
  }

  const code = activationCode.value.trim()
  if (!code) {
    uni.showToast({
      title: '请输入激活码',
      icon: 'none'
    })
    return
  }

  try {
    isRedeeming.value = true
    
    // 播放点击音效
    audioManager.playSoundEffect('click')
    
    const result = await weixinApi.redeemActivationCode(code)
    redeemResult.value = result
    showResultModal.value = true
    
    if (result.success) {
      // 播放成功音效
      audioManager.playSoundEffect('complete')
      audioManager.vibrate('short')
      
      // 清空输入框
      activationCode.value = ''
    } else {
      // 播放失败音效
      audioManager.playSoundEffect('fail')
    }
    
  } catch (error) {
    console.error('兑换激活码失败:', error)
    
    // 播放失败音效
    audioManager.playSoundEffect('fail')
    
    redeemResult.value = {
      success: false,
      message: '兑换失败，请检查网络连接或稍后重试'
    }
    showResultModal.value = true
  } finally {
    isRedeeming.value = false
  }
}

/**
 * 关闭结果弹窗
 */
const closeResultModal = () => {
  showResultModal.value = false
  
  // 如果兑换成功，可以选择跳转到会员中心或首页
  if (redeemResult.value.success) {
    // 可以在这里添加跳转逻辑
    // uni.navigateTo({ url: '/pages/member/index' })
  }
}

/**
 * 返回上一页
 */
const goBack = () => {
  // 播放点击音效
  audioManager.playSoundEffect('click')
  
  uni.navigateBack()
}
</script>

<style scoped>
.activation-code-container {
  min-height: 100vh;
  background: #fdf9f9;
  padding: 32rpx 24rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 48rpx;
  padding: 32rpx 24rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: #4a5568;
  font-weight: 500;
}

/* 输入区域 */
.input-section {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(102, 126, 234, 0.15);
}

.input-container {
  margin-bottom: 32rpx;
}

.input-label {
  display: block;
  font-size: 28rpx;
  color: #1a202c;
  font-weight: 600;
  margin-bottom: 16rpx;
  letter-spacing: 0.5rpx;
}

.code-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  font-size: 32rpx;
  color: #1a202c;
  background: #ffffff;
  box-sizing: border-box;
  font-weight: 500;
  letter-spacing: 1rpx;
}

.code-input:focus {
  border-color: #667eea;
  outline: none;
}

.redeem-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.redeem-btn:active {
  transform: scale(0.98);
}

.redeem-btn-disabled {
  opacity: 0.6;
  transform: none !important;
}

.redeem-btn-text {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 500;
}

/* 信息区域 */
.info-section {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.info-card {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(102, 126, 234, 0.1);
}

.info-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #1a202c;
  margin-bottom: 20rpx;
  letter-spacing: 0.5rpx;
}

.privilege-list, .instruction-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.privilege-item, .instruction-item {
  font-size: 26rpx;
  color: #2d3748;
  line-height: 1.5;
  font-weight: 400;
}

/* 结果弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 48rpx;
  margin: 0 32rpx;
  max-width: 600rpx;
  text-align: center;
}

.result-icon {
  margin-bottom: 24rpx;
}

.icon {
  font-size: 80rpx;
}

.result-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 16rpx;
}

.result-message {
  display: block;
  font-size: 28rpx;
  color: #4a5568;
  margin-bottom: 32rpx;
  line-height: 1.5;
}

.package-info {
  background: #f7fafc;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
}

.package-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 8rpx;
}

.package-desc {
  display: block;
  font-size: 26rpx;
  color: #4a5568;
  margin-bottom: 8rpx;
}

.package-duration {
  display: block;
  font-size: 24rpx;
  color: #718096;
}

.modal-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-btn-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 500;
}

/* 底部按钮 */
.bottom-actions {
  display: flex;
  justify-content: center;
  padding-top: 32rpx;
}

.back-btn {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: #ffffff;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
  box-shadow: 0 8rpx 20rpx rgba(116, 185, 255, 0.3);
}

.back-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.back-text {
  color: inherit;
}
</style>
