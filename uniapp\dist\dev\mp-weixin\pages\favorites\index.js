"use strict";
const common_vendor = require("../../common/vendor.js");
const api_weixin = require("../../api/weixin.js");
const utils_audio = require("../../utils/audio.js");
const utils_auth = require("../../utils/auth.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const isLoading = common_vendor.ref(false);
    const error = common_vendor.ref(null);
    const favorites = common_vendor.ref([]);
    const dailyStatus = common_vendor.ref(null);
    common_vendor.computed(() => {
      return favorites.value.map((level, index) => ({
        ...level,
        levelNumber: String(index + 1).padStart(2, "0"),
        locked: !level.isUnlocked,
        completed: level.isCompleted
      }));
    });
    common_vendor.onLoad(async () => {
      var _a;
      const isLoggedIn = await utils_auth.checkLoginAndRedirect({
        toastMessage: "请先登录以查看收藏",
        redirectUrl: "/pages/login/index"
      });
      if (!isLoggedIn) {
        return;
      }
      try {
        console.log("🔍 检查VIP状态...");
        dailyStatus.value = await api_weixin.weixinApi.getDailyStatus();
        if (!((_a = dailyStatus.value) == null ? void 0 : _a.isVip)) {
          console.log("❌ 非VIP用户，无法访问收藏功能");
          common_vendor.index.showModal({
            title: "收藏功能",
            content: "收藏为会员专享功能，请先开通会员",
            showCancel: false,
            confirmText: "返回",
            success: () => {
              common_vendor.index.navigateBack();
            }
          });
          return;
        }
        console.log("✅ VIP用户，可以访问收藏功能");
      } catch (error2) {
        console.error("获取每日状态失败:", error2);
        common_vendor.index.showModal({
          title: "加载失败",
          content: "无法验证会员状态，请稍后重试",
          showCancel: false,
          confirmText: "返回",
          success: () => {
            common_vendor.index.navigateBack();
          }
        });
        return;
      }
      await loadFavorites();
    });
    const loadFavorites = async () => {
      try {
        isLoading.value = true;
        error.value = null;
        console.log("🔄 开始加载收藏列表...");
        const response = await api_weixin.weixinApi.getUserFavorites();
        console.log("📦 收藏列表API响应:", response);
        if (response && response.favorites) {
          favorites.value = response.favorites;
          console.log("✅ 收藏列表加载成功，数量:", favorites.value.length);
        } else {
          favorites.value = [];
          console.log("⚠️ 收藏列表响应格式异常，设置为空数组");
        }
        if (favorites.value.length === 0) {
          console.log("📭 收藏列表为空，显示空状态页面");
        }
      } catch (err) {
        console.error("❌ 加载收藏列表失败:", err);
        if (err && typeof err === "object" && "message" in err) {
          console.log("错误详情:", err.message);
        }
        error.value = "加载失败，请重试";
        favorites.value = [];
      } finally {
        isLoading.value = false;
        console.log("🏁 收藏列表加载完成，最终状态:", {
          loading: isLoading.value,
          error: error.value,
          favoritesCount: favorites.value.length
        });
      }
    };
    const selectLevel = async (level) => {
      if (level.locked) {
        utils_audio.audioManager.playSoundEffect("fail");
        common_vendor.index.showToast({
          title: "该关卡尚未解锁",
          icon: "none",
          duration: 1500
        });
        return;
      }
      utils_audio.audioManager.playSoundEffect("click");
      console.log("Selected level:", level);
      common_vendor.index.setStorageSync("selectedLevel", JSON.stringify(level));
      common_vendor.index.navigateTo({
        url: "/pages/game/index"
      });
    };
    const unfavoriteLevel = async (level) => {
      try {
        utils_audio.audioManager.playSoundEffect("click");
        const result = await api_weixin.weixinApi.removeFavorite(level.id);
        if (result.success) {
          favorites.value = favorites.value.filter((fav) => fav.id !== level.id);
          utils_audio.audioManager.playSoundEffect("complete");
          common_vendor.index.showToast({
            title: "已取消收藏",
            icon: "success",
            duration: 1500
          });
        } else {
          throw new Error(result.message || "取消收藏失败");
        }
      } catch (error2) {
        console.error("取消收藏失败:", error2);
        utils_audio.audioManager.playSoundEffect("fail");
        common_vendor.index.showToast({
          title: "取消收藏失败",
          icon: "none",
          duration: 1500
        });
      }
    };
    const goToLevelSelection = () => {
      utils_audio.audioManager.playSoundEffect("click");
      common_vendor.index.navigateTo({
        url: "/pages/level-selection/index"
      });
    };
    const goToTagSelection = () => {
      utils_audio.audioManager.playSoundEffect("click");
      common_vendor.index.navigateTo({
        url: "/pages/tag-selection/index"
      });
    };
    const goBack = () => {
      utils_audio.audioManager.playSoundEffect("click");
      common_vendor.index.navigateBack();
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: isLoading.value
      }, isLoading.value ? {} : error.value ? {
        c: common_vendor.t(error.value),
        d: common_vendor.o(loadFavorites)
      } : favorites.value.length === 0 ? {
        f: common_vendor.o(goToLevelSelection),
        g: common_vendor.o(goToTagSelection)
      } : {
        h: common_vendor.t(favorites.value.length),
        i: common_vendor.f(favorites.value, (level, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(index + 1),
            b: common_vendor.t(level.name),
            c: common_vendor.t(level.description),
            d: level.tagIds && level.tagIds.length > 0
          }, level.tagIds && level.tagIds.length > 0 ? {
            e: common_vendor.f(level.tagIds.slice(0, 2), (tag, k1, i1) => {
              return common_vendor.e({
                a: common_vendor.t(tag.name),
                b: tag.isVip
              }, tag.isVip ? {} : {}, {
                c: tag.id,
                d: tag.isVip ? 1 : ""
              });
            })
          } : {}, {
            f: level.isUnlocked
          }, level.isUnlocked ? {} : level.isCompleted ? {
            h: common_vendor.f(level.userStars || 0, (star, k1, i1) => {
              return {
                a: star
              };
            })
          } : {}, {
            g: level.isCompleted,
            i: common_vendor.o(($event) => unfavoriteLevel(level), level.id),
            j: level.id,
            k: level.isUnlocked ? 1 : "",
            l: level.isCompleted ? 1 : "",
            m: common_vendor.o(($event) => selectLevel(level), level.id)
          });
        })
      }, {
        b: error.value,
        e: favorites.value.length === 0,
        j: common_vendor.o(goBack)
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-b2f01737"]]);
wx.createPage(MiniProgramPage);
