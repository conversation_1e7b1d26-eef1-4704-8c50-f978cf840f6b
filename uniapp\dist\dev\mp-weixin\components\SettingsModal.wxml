<view wx:if="{{a}}" class="settings-modal-overlay data-v-67bc804a" bindtap="{{l}}"><view class="settings-modal data-v-67bc804a" catchtap="{{k}}"><view class="modal-header data-v-67bc804a"><text class="modal-title data-v-67bc804a">⚙️ 游戏设置</text><view class="close-btn data-v-67bc804a" bindtap="{{b}}"><text class="close-icon data-v-67bc804a">✕</text></view></view><view class="modal-content data-v-67bc804a"><view class="setting-item data-v-67bc804a"><view class="setting-info data-v-67bc804a"><text class="setting-label data-v-67bc804a">🎵 背景音乐</text><text class="setting-desc data-v-67bc804a">开启后在游戏中播放背景音乐</text></view><switch class="data-v-67bc804a" checked="{{c}}" bindchange="{{d}}" color="#667eea"/></view><view class="setting-item data-v-67bc804a"><view class="setting-info data-v-67bc804a"><text class="setting-label data-v-67bc804a">🔊 游戏音效</text><text class="setting-desc data-v-67bc804a">开启后播放点击、成功等音效</text></view><switch class="data-v-67bc804a" checked="{{e}}" bindchange="{{f}}" color="#667eea"/></view><view class="setting-item data-v-67bc804a"><view class="setting-info data-v-67bc804a"><text class="setting-label data-v-67bc804a">📳 触觉反馈</text><text class="setting-desc data-v-67bc804a">开启后在特定操作时震动</text></view><switch class="data-v-67bc804a" checked="{{g}}" bindchange="{{h}}" color="#667eea"/></view></view><view class="modal-footer data-v-67bc804a"><button class="test-btn data-v-67bc804a" bindtap="{{i}}"><text class="test-btn-text data-v-67bc804a">🎧 测试音效</text></button><button class="confirm-btn data-v-67bc804a" bindtap="{{j}}"><text class="confirm-btn-text data-v-67bc804a">确定</text></button></view></view></view>