"use strict";
const common_vendor = require("../../common/vendor.js");
const api_weixin = require("../../api/weixin.js");
const api_utils = require("../../api/utils.js");
const utils_share = require("../../utils/share.js");
const utils_audio = require("../../utils/audio.js");
const utils_debugControl = require("../../utils/debug-control.js");
const composables_useGlobalConfig = require("../../composables/useGlobalConfig.js");
const utils_mockData = require("../../utils/mockData.js");
if (!Math) {
  SettingsModal();
}
const SettingsModal = () => "../../components/SettingsModal.js";
const _sfc_defineComponent = common_vendor.defineComponent({
  __name: "index",
  setup(__props, { expose: __expose }) {
    const selectedLibraryInfo = common_vendor.ref(null);
    const currentLevel = common_vendor.ref(null);
    const currentLevelId = common_vendor.ref(1);
    const currentLevelDetail = common_vendor.ref(null);
    const userInfo = common_vendor.ref(null);
    const isProgressSyncing = common_vendor.ref(false);
    const selectedLevelData = common_vendor.ref(null);
    let isHandlingGameShareReward = false;
    const gameBoard = common_vendor.ref([]);
    const selectedTiles = common_vendor.ref([]);
    const matchedPairs = common_vendor.ref(0);
    const totalPairs = common_vendor.ref(0);
    const isChecking = common_vendor.ref(false);
    const debugMode = common_vendor.ref(false);
    const gameStartTime = common_vendor.ref(null);
    const gameEndTime = common_vendor.ref(null);
    const gameTimer = common_vendor.ref(null);
    const currentGameTime = common_vendor.ref(60);
    const finalGameTime = common_vendor.ref(0);
    const gameStars = common_vendor.ref(0);
    const isTimeUp = common_vendor.ref(false);
    const isCurrentLevelFavorited = common_vendor.ref(false);
    let tileIdCounter = 0;
    const generateRandomLightColor = () => {
      const colors = [
        "#FFE1E6",
        // 淡粉色
        "#E1F0FF",
        // 淡蓝色
        "#E1FFE1"
        // 淡绿色
      ];
      return colors[Math.floor(Math.random() * colors.length)];
    };
    const isGameEndModalVisible = common_vendor.ref(false);
    const gameResultText = common_vendor.ref("");
    const gameWon = common_vendor.ref(false);
    const isGameInitializing = common_vendor.ref(true);
    const isCalculatingPositions = common_vendor.ref(false);
    const isPreparingRender = common_vendor.ref(false);
    const isLoadingGame = common_vendor.ref(false);
    const isGameReady = common_vendor.ref(false);
    const loadingProgress = common_vendor.ref(0);
    const loadingMessage = common_vendor.ref("");
    const isReplaying = common_vendor.ref(false);
    const showGridLines = common_vendor.ref(false);
    const currentGridSystem = common_vendor.ref(null);
    const isDevelopment = common_vendor.ref(false);
    const isH5 = common_vendor.ref(utils_mockData.isH5Environment());
    const useMockData = common_vendor.ref(isH5.value);
    const testMockDataImport = () => {
      console.log("🧪 测试Mock数据导入:");
      console.log("  - mockLibraries:", utils_mockData.mockLibraries);
      console.log("  - mockLevels:", utils_mockData.mockLevels);
      console.log("  - mockUser:", utils_mockData.mockUser);
      console.log("  - getMockWordsForLevel(1, 8):", utils_mockData.getMockWordsForLevel(1, 8));
      console.log("  - isH5Environment():", utils_mockData.isH5Environment());
    };
    const testGridSystem = () => {
      console.log("🧪 测试网格系统:");
      try {
        let testCards = [
          { id: 1, type: "english", word: { english: "test1", chinese: "测试1" } },
          { id: 2, type: "chinese", word: { english: "test1", chinese: "测试1" } },
          { id: 3, type: "english", word: { english: "test2", chinese: "测试2" } },
          { id: 4, type: "chinese", word: { english: "test2", chinese: "测试2" } }
        ];
        const testWidth = 750;
        const testHeight = 800;
        console.log("  - 输入测试数据:", testCards);
        console.log("  - 容器尺寸:", { width: testWidth, height: testHeight });
        const result = generateGridBasedPositions(testCards, testWidth, testHeight);
        console.log("  - 输出结果:", result);
        console.log("  - 测试完成");
        console.log("🧪 测试常量赋值修复:");
        testCards = [...result];
        console.log("  - 常量赋值测试通过");
        return result;
      } catch (error) {
        console.error("❌ 网格系统测试失败:", error);
        return null;
      }
    };
    const debugGameState = () => {
      console.log("🔍 游戏状态调试信息:");
      console.log("  - isH5:", isH5.value);
      console.log("  - useMockData:", useMockData.value);
      console.log("  - isGameInitializing:", isGameInitializing.value);
      console.log("  - isCalculatingPositions:", isCalculatingPositions.value);
      console.log("  - isPreparingRender:", isPreparingRender.value);
      console.log("  - isLoadingGame:", isLoadingGame.value);
      console.log("  - isGameReady:", isGameReady.value);
      console.log("  - currentLevel:", currentLevel.value);
      console.log("  - currentLevelDetail:", currentLevelDetail.value);
      console.log("  - wordsForCurrentLevel.length:", wordsForCurrentLevel.value.length);
      console.log("  - wordsForCurrentLevel:", wordsForCurrentLevel.value);
      console.log("  - gameBoard.length:", gameBoard.value.length);
      console.log("  - selectedLevelData:", selectedLevelData.value);
      console.log("  - selectedLibraryInfo:", selectedLibraryInfo.value);
      console.log("  - userInfo:", userInfo.value);
    };
    const showSettingsModal = common_vendor.ref(false);
    const gameSettings = common_vendor.ref({
      backgroundMusic: true,
      soundEffects: true,
      vibration: true
    });
    const {
      getBackgroundMusicUrl,
      initializeGlobalConfig
    } = composables_useGlobalConfig.useGlobalConfig();
    const canGoToNextLevel = common_vendor.computed(() => {
      if (currentLevelDetail.value) {
        return true;
      }
      if (!selectedLibraryInfo.value || !selectedLibraryInfo.value.words)
        return false;
      const maxlevel = Math.min(
        Math.floor(selectedLibraryInfo.value.words.length / 8),
        1e3
      );
      return currentLevelId.value < maxlevel;
    });
    const wordsForCurrentLevel = common_vendor.computed(() => {
      console.log("🔍 计算当前关卡词汇...");
      console.log("  - currentLevelDetail.value:", currentLevelDetail.value);
      console.log("  - selectedLibraryInfo.value:", selectedLibraryInfo.value);
      console.log("  - currentLevelId.value:", currentLevelId.value);
      console.log("  - useMockData.value:", useMockData.value);
      if (currentLevelDetail.value && currentLevelDetail.value.phrases) {
        console.log("📚 使用关卡详情中的词组数据:", currentLevelDetail.value.phrases.length, "个词汇");
        const words = currentLevelDetail.value.phrases.map((phrase) => ({
          english: phrase.english || phrase.text,
          chinese: phrase.chinese || phrase.meaning,
          id: phrase.id,
          pronunciation: phrase.pronunciation,
          category: phrase.category
        }));
        console.log("✅ 关卡词汇处理完成:", words.length, "个词汇");
        return words;
      }
      if (selectedLibraryInfo.value && selectedLibraryInfo.value.words) {
        console.log("📖 使用词库中的词汇数据:", selectedLibraryInfo.value.words.length, "个词汇");
        const words = selectedLibraryInfo.value.words;
        const startIndex = (currentLevelId.value - 1) * 8 % words.length;
        const selectedWords = [];
        for (let i = 0; i < 8; i++) {
          const wordIndex = (startIndex + i) % words.length;
          selectedWords.push(words[wordIndex]);
        }
        console.log("✅ 词库词汇处理完成:", selectedWords.length, "个词汇");
        return selectedWords;
      }
      console.warn("⚠️ 没有可用的词汇数据");
      console.log("📊 当前状态:");
      console.log("  - currentLevelDetail.value:", currentLevelDetail.value);
      console.log("  - selectedLibraryInfo.value:", selectedLibraryInfo.value);
      return [];
    });
    const formatGameTime = (seconds) => {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
    };
    const startGameTimer = () => {
      gameStartTime.value = Date.now();
      currentGameTime.value = 60;
      isTimeUp.value = false;
      if (gameTimer.value) {
        clearInterval(gameTimer.value);
      }
      gameTimer.value = setInterval(() => {
        if (gameStartTime.value && currentGameTime.value > 0) {
          const elapsed = Math.floor((Date.now() - gameStartTime.value) / 1e3);
          currentGameTime.value = Math.max(0, 60 - elapsed);
          if (currentGameTime.value <= 0) {
            handleTimeUp();
          }
        }
      }, 1e3);
    };
    const stopGameTimer = () => {
      if (gameTimer.value) {
        clearInterval(gameTimer.value);
        gameTimer.value = null;
      }
      if (gameStartTime.value) {
        gameEndTime.value = Date.now();
        finalGameTime.value = Math.floor((gameEndTime.value - gameStartTime.value) / 1e3);
      }
    };
    const handleTimeUp = () => {
      console.log("⏰ 时间到期！游戏结束");
      isTimeUp.value = true;
      stopGameTimer();
      finalGameTime.value = 60;
      gameStars.value = 0;
      utils_audio.audioManager.playSoundEffect("fail");
      utils_audio.audioManager.vibrate("short");
      setTimeout(() => {
        endGame(false);
      }, 1e3);
    };
    const calculateStars = (completionTime, levelDetail) => {
      if (levelDetail == null ? void 0 : levelDetail.timeLimit) {
        const timeLimit = levelDetail.timeLimit;
        if (completionTime <= timeLimit * 0.5) {
          return 3;
        } else if (completionTime <= timeLimit * 0.75) {
          return 2;
        } else if (completionTime <= timeLimit) {
          return 1;
        } else {
          return 1;
        }
      }
      if (completionTime <= 30) {
        return 3;
      } else if (completionTime <= 60) {
        return 2;
      } else {
        return 1;
      }
    };
    common_vendor.onLoad(async () => {
      try {
        testMockDataImport();
        testGridSystem();
        if (isH5.value) {
          console.log("🌐 检测到H5环境，启用Mock数据模式");
          const mockLibraryData = {
            id: utils_mockData.mockLibraries[0].id,
            name: utils_mockData.mockLibraries[0].name,
            description: utils_mockData.mockLibraries[0].description,
            words: utils_mockData.getMockWordsForLevel(1, 16)
          };
          const mockLevelData = {
            id: utils_mockData.mockLevels[0].id,
            name: utils_mockData.mockLevels[0].name,
            libraryId: utils_mockData.mockLibraries[0].id
          };
          console.log("📦 设置Mock存储数据:");
          console.log("  - mockLibraryData:", mockLibraryData);
          console.log("  - mockLevelData:", mockLevelData);
          common_vendor.index.setStorageSync("selectedLibrary", JSON.stringify(mockLibraryData));
          common_vendor.index.setStorageSync("selectedLevel", JSON.stringify(mockLevelData));
          console.log("✅ H5环境Mock数据初始化完成");
          const storedLibrary = common_vendor.index.getStorageSync("selectedLibrary");
          const storedLevel = common_vendor.index.getStorageSync("selectedLevel");
          console.log("🔍 验证存储数据:");
          console.log("  - storedLibrary:", storedLibrary);
          console.log("  - storedLevel:", storedLevel);
        }
        await initializeGlobalConfig();
        await initializeGame();
        checkDevelopmentEnvironment();
        initAudioSettings();
      } catch (error) {
        console.error("❌ onLoad初始化失败:", error);
        debugGameState();
      }
    });
    const initAudioSettings = () => {
      try {
        const settings = utils_audio.audioManager.getSettings();
        gameSettings.value = { ...settings };
        console.log("音频设置初始化完成:", settings);
        if (settings.backgroundMusic) {
          const musicUrl = getBackgroundMusicUrl("game");
          console.log("播放游戏页面背景音乐:", musicUrl);
          utils_audio.audioManager.playBackgroundMusic("game", musicUrl);
        }
      } catch (error) {
        console.error("初始化音频设置失败:", error);
      }
    };
    common_vendor.onShow(() => {
      console.log("游戏页面显示");
      utils_audio.audioManager.onPageShow();
      const settings = utils_audio.audioManager.getSettings();
      if (settings.backgroundMusic) {
        const musicUrl = getBackgroundMusicUrl("game");
        utils_audio.audioManager.playBackgroundMusic("game", musicUrl);
      }
    });
    common_vendor.onHide(() => {
      console.log("游戏页面隐藏");
      utils_audio.audioManager.onPageHide();
    });
    const initializeGame = async () => {
      try {
        console.log("🎮 开始游戏初始化...");
        isGameInitializing.value = true;
        isCalculatingPositions.value = false;
        isPreparingRender.value = false;
        isLoadingGame.value = false;
        isGameReady.value = false;
        console.log("📱 加载用户信息...");
        await loadUserInfo();
        console.log("🎯 加载关卡信息...");
        const levelData = common_vendor.index.getStorageSync("selectedLevel");
        let gameDataLoaded = false;
        if (levelData) {
          try {
            selectedLevelData.value = JSON.parse(levelData);
            console.log("获取到选择的关卡数据:", selectedLevelData.value);
            await loadLevelDetail(selectedLevelData.value.id);
            if (userInfo.value) {
              await recordGameStart(selectedLevelData.value.id);
            }
            gameDataLoaded = true;
          } catch (e) {
            console.error("Failed to parse selected level data:", e);
            console.log("🔄 尝试使用词库数据作为备用...");
            await loadLibraryDataAsFallback();
            gameDataLoaded = true;
          }
        } else {
          console.log("🔄 没有关卡数据，使用词库数据作为备用...");
          await loadLibraryDataAsFallback();
          gameDataLoaded = true;
        }
        console.log("🔍 检查游戏数据完整性...");
        console.log("  - gameDataLoaded:", gameDataLoaded);
        console.log("  - wordsForCurrentLevel.length:", wordsForCurrentLevel.value.length);
        console.log("  - currentLevel:", currentLevel.value);
        console.log("  - currentLevelDetail:", currentLevelDetail.value);
        if (!gameDataLoaded || wordsForCurrentLevel.value.length < 8) {
          console.error("❌ 游戏数据不足:");
          console.error("  - gameDataLoaded:", gameDataLoaded);
          console.error("  - wordsForCurrentLevel.length:", wordsForCurrentLevel.value.length);
          console.error("  - 需要至少8个单词");
          debugGameState();
          throw new Error(`无法获取足够的游戏数据: 需要8个单词，当前只有${wordsForCurrentLevel.value.length}个`);
        }
        console.log("🧮 开始计算卡片位置...");
        isGameInitializing.value = false;
        isCalculatingPositions.value = true;
        await new Promise((resolve) => setTimeout(resolve, 100));
        console.log("🎲 初始化游戏棋盘...");
        await initializeGameBoardAsync();
        console.log("✅ 位置计算完成，准备渲染...");
        isCalculatingPositions.value = false;
        isPreparingRender.value = true;
        await new Promise((resolve) => setTimeout(resolve, 100));
        console.log("🎨 开始最终渲染准备...");
        if (!checkRenderReadiness()) {
          throw new Error("游戏数据未正确生成或不完整");
        }
        console.log("🎮 开始加载游戏...");
        isPreparingRender.value = false;
        isLoadingGame.value = true;
        loadingProgress.value = 0;
        loadingMessage.value = "正在启动游戏...";
        await simulateGameLoading(2e3);
        console.log("🎉 游戏加载完成，开始渲染");
        isLoadingGame.value = false;
        isGameReady.value = true;
        console.log("⏱️ 开始游戏计时...");
        startGameTimer();
        console.log("💖 检查关卡收藏状态...");
        await checkCurrentLevelFavoriteStatus();
        console.log("✨ 游戏初始化完成，欢迎开始游戏！");
        debugGameState();
      } catch (error) {
        console.error("❌ 游戏初始化失败:", error);
        isGameInitializing.value = false;
        isCalculatingPositions.value = false;
        isPreparingRender.value = false;
        isLoadingGame.value = false;
        isGameReady.value = false;
        debugGameState();
        api_utils.showError(`游戏初始化失败: ${error.message || "未知错误"}`);
      }
    };
    const loadLevelDetail = async (levelId) => {
      var _a, _b;
      try {
        console.log("正在加载关卡详情:", levelId);
        if (useMockData.value) {
          console.log("🔧 H5环境：使用mock关卡数据");
          const mockLevel = utils_mockData.mockLevels.find((level) => level.id.toString() === levelId) || utils_mockData.mockLevels[0];
          const mockWords = utils_mockData.getMockWordsForLevel(parseInt(levelId), 8);
          const mockLevelDetail = {
            id: mockLevel.id,
            name: mockLevel.name,
            description: mockLevel.description,
            difficulty: mockLevel.difficulty,
            wordsCount: mockLevel.wordsCount,
            phrases: mockWords.map((word) => ({
              id: word.id,
              english: word.english,
              chinese: word.chinese,
              pronunciation: word.pronunciation,
              category: word.category
            }))
          };
          const response = await utils_mockData.mockApiResponse(mockLevelDetail, 400);
          currentLevelDetail.value = response.data;
          currentLevel.value = {
            id: response.data.id,
            name: response.data.name,
            wordsCount: ((_a = response.data.phrases) == null ? void 0 : _a.length) || 8
          };
          console.log("Mock关卡详情加载成功:", response.data);
        } else {
          const levelDetail = await api_weixin.weixinApi.getLevelDetail(levelId);
          currentLevelDetail.value = levelDetail;
          currentLevel.value = {
            id: levelDetail.id,
            name: levelDetail.name,
            wordsCount: ((_b = levelDetail.phrases) == null ? void 0 : _b.length) || 8
          };
          console.log("关卡详情加载成功:", levelDetail);
        }
      } catch (error) {
        console.error("加载关卡详情失败:", error);
        throw error;
      }
    };
    const recordGameStart = async (levelId) => {
      try {
        if (userInfo.value && userInfo.value.id) {
          if (useMockData.value) {
            console.log("🔧 H5环境：模拟记录游戏开始");
            const mockResponse = await utils_mockData.mockApiResponse({
              success: true,
              gameId: Date.now(),
              startTime: (/* @__PURE__ */ new Date()).toISOString()
            }, 200);
            console.log("Mock游戏开始记录成功:", mockResponse.data);
          } else {
            await api_weixin.weixinApi.startGame(userInfo.value.id, levelId);
            console.log("游戏开始记录成功");
          }
        }
      } catch (error) {
        console.error("记录游戏开始失败:", error);
      }
    };
    const loadLibraryDataAsFallback = async () => {
      console.log("🔄 开始加载词库数据作为备用...");
      if (useMockData.value) {
        console.log("🔧 H5环境：使用mock词库数据");
        const mockLibrary = utils_mockData.mockLibraries[0];
        const mockLevel = utils_mockData.mockLevels[0];
        selectedLibraryInfo.value = {
          id: mockLibrary.id,
          name: mockLibrary.name,
          description: mockLibrary.description,
          words: utils_mockData.getMockWordsForLevel(1, 16)
          // 获取足够的单词
        };
        currentLevelId.value = mockLevel.id;
        currentLevel.value = {
          id: mockLevel.id,
          name: mockLevel.name,
          wordsCount: 8
        };
        console.log("✅ 成功使用mock词库数据:", selectedLibraryInfo.value);
        console.log("📚 当前关卡词汇数量:", wordsForCurrentLevel.value.length);
      } else {
        const libraryData = common_vendor.index.getStorageSync("selectedLibrary");
        if (libraryData) {
          try {
            selectedLibraryInfo.value = JSON.parse(libraryData);
            const savedLevelId = getCurrentLevelId(selectedLibraryInfo.value.id);
            currentLevelId.value = savedLevelId;
            currentLevel.value = {
              id: currentLevelId.value,
              name: `第${currentLevelId.value}关`,
              wordsCount: 8
            };
            console.log("✅ 成功使用词库数据作为备用:", selectedLibraryInfo.value);
            console.log("📚 当前关卡词汇数量:", wordsForCurrentLevel.value.length);
            if (wordsForCurrentLevel.value.length < 8) {
              throw new Error(`词库数据不足: 需要8个词汇，当前只有${wordsForCurrentLevel.value.length}个`);
            }
          } catch (e) {
            console.error("❌ 加载词库数据失败:", e);
            throw new Error(`加载词库数据失败: ${e.message || "未知错误"}`);
          }
        } else {
          console.error("❌ 没有找到词库数据");
          throw new Error("未选择关卡或词库");
        }
      }
    };
    const loadUserInfo = async () => {
      try {
        if (useMockData.value) {
          console.log("🔧 H5环境：使用mock用户数据");
          const response = await utils_mockData.mockApiResponse(utils_mockData.mockUser, 300);
          userInfo.value = response.data;
          console.log("Mock用户信息加载成功:", userInfo.value);
        } else {
          const localUserInfo = api_weixin.weixinApi.getLocalUserInfo();
          if (localUserInfo) {
            userInfo.value = localUserInfo;
            console.log("游戏页面获取到用户信息:", localUserInfo);
          }
        }
      } catch (error) {
        console.error("加载用户信息失败:", error);
        if (!useMockData.value) {
          console.log("🔄 用户信息获取失败，降级使用mock数据");
          const response = await utils_mockData.mockApiResponse(utils_mockData.mockUser, 100);
          userInfo.value = response.data;
        }
      }
    };
    const getCurrentLevelId = (libraryId) => {
      const key = `currentLevel_${libraryId}`;
      const saved = common_vendor.index.getStorageSync(key);
      return saved ? parseInt(saved) : 1;
    };
    const saveCurrentLevelId = (libraryId, levelId) => {
      const key = `currentLevel_${libraryId}`;
      common_vendor.index.setStorageSync(key, levelId.toString());
    };
    const calculateCardSpacing = (card1Size, card2Size) => {
      const cardWidth = 160;
      const cardHeight = 35;
      const baseSpacing = Math.floor((cardWidth + cardHeight) / 2);
      const minSpacing = 80;
      const extraPadding = 15;
      const spacing = Math.max(baseSpacing, minSpacing) + extraPadding;
      console.log(`固定间距计算: 卡片尺寸(${cardWidth}×${cardHeight}) = ${spacing}rpx`);
      return spacing;
    };
    const getStandardCardSpacing = () => {
      const standardSize = { width: 160, height: 35 };
      const baseSpacing = Math.floor((standardSize.width + standardSize.height) / 2);
      const minSpacing = 80;
      const extraPadding = 15;
      const spacing = Math.max(baseSpacing, minSpacing) + extraPadding;
      console.log(`标准间距: ${spacing}rpx`);
      return spacing;
    };
    const getCompactSpacing = () => {
      const standardSpacing = getStandardCardSpacing();
      const compactSpacing = Math.floor(standardSpacing * 0.6);
      const minCompactSpacing = 50;
      const spacing = Math.max(compactSpacing, minCompactSpacing);
      console.log(`紧凑间距: ${spacing}rpx`);
      return spacing;
    };
    const isRectangleColliding = (rect1, rect2, padding = 0) => {
      const extraSafety = 10;
      const totalPadding = padding + extraSafety;
      const expandedRect1 = {
        left: rect1.x - totalPadding,
        right: rect1.x + rect1.width + totalPadding,
        top: rect1.y - totalPadding,
        bottom: rect1.y + rect1.height + totalPadding
      };
      const expandedRect2 = {
        left: rect2.x - totalPadding,
        right: rect2.x + rect2.width + totalPadding,
        top: rect2.y - totalPadding,
        bottom: rect2.y + rect2.height + totalPadding
      };
      const isColliding = !(expandedRect1.right <= expandedRect2.left || // rect1在rect2左侧
      expandedRect1.left >= expandedRect2.right || // rect1在rect2右侧
      expandedRect1.bottom <= expandedRect2.top || // rect1在rect2上方
      expandedRect1.top >= expandedRect2.bottom);
      if (isColliding) {
        const centerX1 = rect1.x + rect1.width / 2;
        const centerY1 = rect1.y + rect1.height / 2;
        const centerX2 = rect2.x + rect2.width / 2;
        const centerY2 = rect2.y + rect2.height / 2;
        const distance = Math.sqrt(
          Math.pow(centerX1 - centerX2, 2) + Math.pow(centerY1 - centerY2, 2)
        );
        const cardWidth = 160;
        const cardHeight = 35;
        const minCenterDistance = cardWidth + cardHeight + totalPadding;
        if (distance < minCenterDistance) {
          console.warn(`碰撞检测: 中心距离${Math.round(distance)}rpx < 最小距离${Math.round(minCenterDistance)}rpx`);
          return true;
        }
      }
      return isColliding;
    };
    const isOverlapping = (rect1, rect2, safeDistance = null) => {
      if (safeDistance === null) {
        safeDistance = calculateCardSpacing();
      }
      return isRectangleColliding(rect1, rect2, safeDistance);
    };
    const getGameBoardDimensions = () => {
      try {
        const systemInfo = common_vendor.index.getSystemInfoSync();
        const screenWidth = systemInfo.screenWidth || 375;
        const screenHeight = systemInfo.screenHeight || 667;
        const cardWidth = 160;
        const cardHeight = 35;
        const spacing = getStandardCardSpacing();
        const margin = 30;
        const cols = 4;
        const rows = 4;
        const minRequiredWidth = cols * cardWidth + (cols - 1) * spacing + margin * 2;
        const minRequiredHeight = rows * cardHeight + (rows - 1) * spacing + margin * 2;
        const cssMaxWidth = 750;
        const cssPadding = 25 * 4;
        const actualGameAreaWidth = cssMaxWidth - cssPadding;
        let containerWidth = Math.min(actualGameAreaWidth, minRequiredWidth);
        if (containerWidth < minRequiredWidth) {
          console.warn("CSS宽度限制过小，使用紧凑布局");
          const compactSpacing = getCompactSpacing();
          containerWidth = Math.min(
            actualGameAreaWidth,
            cols * cardWidth + (cols - 1) * compactSpacing + margin * 2
          );
        }
        const maxHeight = Math.floor(screenHeight * 0.7 * (750 / screenWidth));
        let containerHeight = Math.max(minRequiredHeight, Math.floor(containerWidth * 1.2));
        containerHeight = Math.min(containerHeight, maxHeight);
        console.log(`� 随机定位参数:`);
        console.log(`   - 卡片尺寸: ${cardWidth}×${cardHeight}rpx`);
        console.log(`   - 总卡片数: 16张`);
        console.log(`   - 标准间距: ${spacing}rpx, 边距: ${margin}rpx`);
        console.log(`   - 最小要求: ${minRequiredWidth}×${minRequiredHeight}rpx`);
        console.log(`   - CSS限制: ${actualGameAreaWidth}rpx`);
        console.log(`   - 实际尺寸: ${containerWidth}×${containerHeight}rpx`);
        return {
          containerWidth,
          containerHeight
        };
      } catch (error) {
        console.warn("获取屏幕信息失败，使用默认尺寸:", error);
        return { containerWidth: 700, containerHeight: 900 };
      }
    };
    const batchProcessGameData = async (cards, containerWidth, containerHeight) => {
      console.log("🔄 开始批量处理游戏数据...");
      console.log("📊 验证卡片布局质量...");
      validateCardLayout(cards, containerWidth, containerHeight);
      console.log("🔀 打乱卡片内容分配...");
      const englishCards = cards.filter((card) => card.type === "english");
      const chineseCards = cards.filter((card) => card.type === "chinese");
      for (let i = englishCards.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        const tempWord = englishCards[i].word;
        const tempColor = englishCards[i].color;
        const tempPairId = englishCards[i].pairId;
        englishCards[i].word = englishCards[j].word;
        englishCards[i].color = englishCards[j].color;
        englishCards[i].pairId = englishCards[j].pairId;
        englishCards[j].word = tempWord;
        englishCards[j].color = tempColor;
        englishCards[j].pairId = tempPairId;
      }
      for (let i = chineseCards.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        const tempWord = chineseCards[i].word;
        const tempColor = chineseCards[i].color;
        const tempPairId = chineseCards[i].pairId;
        chineseCards[i].word = chineseCards[j].word;
        chineseCards[i].color = chineseCards[j].color;
        chineseCards[i].pairId = chineseCards[j].pairId;
        chineseCards[j].word = tempWord;
        chineseCards[j].color = tempColor;
        chineseCards[j].pairId = tempPairId;
      }
      await new Promise((resolve) => setTimeout(resolve, 10));
      console.log("✅ 游戏数据批量处理完成");
      console.log(`📊 英文卡片 (${englishCards.length}张), 中文卡片 (${chineseCards.length}张)`);
      return cards;
    };
    class GridSystem {
      constructor(containerWidth, containerHeight, cardCount) {
        this.containerWidth = containerWidth;
        this.containerHeight = containerHeight;
        this.cardCount = cardCount;
        this.cardWidth = 160;
        this.cardHeight = 35;
        this.padding = 20;
        this.calculateGrid();
        this.occupiedGrids = /* @__PURE__ */ new Set();
      }
      /**
       * 计算最优网格布局
       */
      calculateGrid() {
        const availableWidth = this.containerWidth - this.padding * 2;
        const availableHeight = this.containerHeight - this.padding * 2;
        this.gridWidth = this.cardWidth + 40;
        this.gridHeight = (this.cardHeight + 40) * 1.5;
        this.cols = Math.floor(availableWidth / this.gridWidth);
        this.rows = Math.floor(availableHeight / this.gridHeight);
        const totalGrids = this.cols * this.rows;
        if (totalGrids < this.cardCount) {
          this.adjustGridSize();
        }
        console.log(`📐 网格系统: ${this.cols}列 × ${this.rows}行 = ${totalGrids}个网格，需要${this.cardCount}个`);
        console.log(`📏 网格尺寸: ${this.gridWidth} × ${this.gridHeight} (高度已调整为1.5倍)`);
      }
      /**
       * 调整网格尺寸以容纳所有卡片
       */
      adjustGridSize() {
        const availableWidth = this.containerWidth - this.padding * 2;
        const availableHeight = this.containerHeight - this.padding * 2;
        const idealGridCount = Math.ceil(this.cardCount * 1.5);
        let bestCols = 4;
        let bestRows = 4;
        let bestGridWidth = availableWidth / bestCols;
        let bestGridHeight = availableHeight / bestRows;
        for (let cols = 3; cols <= 8; cols++) {
          for (let rows = 3; rows <= 8; rows++) {
            if (cols * rows >= idealGridCount) {
              const gridWidth = availableWidth / cols;
              const gridHeight = availableHeight / rows;
              if (gridWidth >= this.cardWidth + 20 && gridHeight >= (this.cardHeight + 20) * 1.5) {
                bestCols = cols;
                bestRows = rows;
                bestGridWidth = gridWidth;
                bestGridHeight = gridHeight;
                break;
              }
            }
          }
        }
        this.cols = bestCols;
        this.rows = bestRows;
        this.gridWidth = bestGridWidth;
        this.gridHeight = bestGridHeight;
        console.log(`🔧 调整后网格: ${this.cols}列 × ${this.rows}行，网格尺寸: ${Math.round(this.gridWidth)} × ${Math.round(this.gridHeight)}`);
      }
      /**
       * 获取随机可用网格位置
       * @returns {Object|null} 网格位置信息或null
       */
      getRandomAvailableGrid() {
        const totalGrids = this.cols * this.rows;
        const availableGrids = [];
        for (let i = 0; i < totalGrids; i++) {
          if (!this.occupiedGrids.has(i)) {
            availableGrids.push(i);
          }
        }
        if (availableGrids.length === 0) {
          console.warn("⚠️ 没有可用的网格位置");
          return null;
        }
        const randomIndex = Math.floor(Math.random() * availableGrids.length);
        const gridIndex = availableGrids[randomIndex];
        this.occupiedGrids.add(gridIndex);
        const row = Math.floor(gridIndex / this.cols);
        const col = gridIndex % this.cols;
        const gridX = this.padding + col * this.gridWidth;
        const gridY = this.padding + row * this.gridHeight;
        return {
          gridIndex,
          row,
          col,
          gridX,
          gridY,
          gridWidth: this.gridWidth,
          gridHeight: this.gridHeight
        };
      }
      /**
       * 在网格内随机定位卡片
       * @param {Object} grid - 网格信息
       * @returns {Object} 卡片位置
       */
      getRandomPositionInGrid(grid) {
        const maxOffsetX = grid.gridWidth - this.cardWidth;
        const maxOffsetY = grid.gridHeight - this.cardHeight;
        const safeOffsetX = Math.max(0, maxOffsetX);
        const safeOffsetY = Math.max(0, maxOffsetY);
        const offsetX = Math.random() * safeOffsetX;
        const offsetY = Math.random() * safeOffsetY;
        const x = grid.gridX + offsetX;
        const y = grid.gridY + offsetY;
        console.log(`📍 网格(${grid.row},${grid.col}) -> 卡片位置(${Math.round(x)}, ${Math.round(y)})`);
        return {
          x: Math.round(x),
          y: Math.round(y)
        };
      }
      /**
       * 重置网格占用状态
       */
      reset() {
        this.occupiedGrids.clear();
        console.log("🔄 网格系统已重置");
      }
    }
    const checkRenderReadiness = () => {
      console.log("🔍 检查渲染准备状态...");
      console.log("📊 基础数据检查:");
      console.log("  - wordsForCurrentLevel.length:", wordsForCurrentLevel.value.length);
      console.log("  - currentLevel:", currentLevel.value);
      console.log("  - gameBoard.length:", gameBoard.value.length);
      if (wordsForCurrentLevel.value.length < 8) {
        console.error("❌ 词汇数据不足，需要8个词汇，当前:", wordsForCurrentLevel.value.length);
        debugGameState();
        return false;
      }
      if (gameBoard.value.length === 0) {
        console.error("❌ gameBoard数据为空");
        debugGameState();
        return false;
      }
      if (gameBoard.value.length !== 16) {
        console.error("❌ gameBoard数据不完整，期望16张卡片，实际:", gameBoard.value.length);
        debugGameState();
        return false;
      }
      for (let i = 0; i < gameBoard.value.length; i++) {
        const card = gameBoard.value[i];
        if (!card.position || !card.cardSize || !card.word) {
          console.error(`❌ 卡片${i + 1}数据不完整:`, card);
          return false;
        }
        if (typeof card.position.x !== "number" || typeof card.position.y !== "number") {
          console.error(`❌ 卡片${i + 1}位置数据无效:`, card.position);
          return false;
        }
      }
      if (!currentLevel.value) {
        console.error("❌ 当前关卡数据为空");
        debugGameState();
        return false;
      }
      console.log("✅ 渲染准备状态检查通过");
      return true;
    };
    const simulateGameLoading = async (duration = 1500) => {
      console.log("🎮 开始模拟游戏加载进度...");
      const steps = [
        { progress: 0, message: "初始化游戏引擎..." },
        { progress: 20, message: "加载游戏资源..." },
        { progress: 40, message: "准备卡片数据..." },
        { progress: 60, message: "生成游戏布局..." },
        { progress: 80, message: "优化游戏性能..." },
        { progress: 95, message: "最终检查..." },
        { progress: 100, message: "加载完成！" }
      ];
      const stepDuration = duration / steps.length;
      for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        loadingProgress.value = step.progress;
        loadingMessage.value = step.message;
        console.log(`📊 加载进度: ${step.progress}% - ${step.message}`);
        await new Promise((resolve) => setTimeout(resolve, stepDuration));
      }
      console.log("✅ 游戏加载进度模拟完成");
    };
    const simulateReplayLoading = async (duration = 1200) => {
      console.log("🔄 开始模拟重玩加载进度...");
      const steps = [
        { progress: 0, message: "重置游戏状态..." },
        { progress: 25, message: "清理旧数据..." },
        { progress: 50, message: "重新生成布局..." },
        { progress: 75, message: "优化卡片位置..." },
        { progress: 100, message: "重玩准备完成！" }
      ];
      const stepDuration = duration / steps.length;
      for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        loadingProgress.value = step.progress;
        loadingMessage.value = step.message;
        console.log(`🔄 重玩进度: ${step.progress}% - ${step.message}`);
        await new Promise((resolve) => setTimeout(resolve, stepDuration));
      }
      console.log("✅ 重玩加载进度模拟完成");
    };
    const generateGridBasedPositions = (cards, containerWidth, containerHeight) => {
      console.log("🎯 开始基于网格系统生成卡片位置...");
      console.log("📊 输入参数检查:");
      console.log("  - cards.length:", cards.length);
      console.log("  - containerWidth:", containerWidth);
      console.log("  - containerHeight:", containerHeight);
      console.log("  - cards:", cards);
      if (!Array.isArray(cards) || cards.length === 0) {
        console.error("❌ 无效的卡片数组:", cards);
        return [];
      }
      if (!containerWidth || !containerHeight) {
        console.error("❌ 无效的容器尺寸:", { containerWidth, containerHeight });
        return cards;
      }
      const gridSystem = new GridSystem(containerWidth, containerHeight, cards.length);
      currentGridSystem.value = gridSystem;
      const updatedCards = cards.map((card, index) => {
        console.log(`🔄 为第${index + 1}张卡片分配网格位置: ${card.type} - ${card.word ? card.type === "english" ? card.word.english : card.word.chinese : "unknown"}`);
        const grid = gridSystem.getRandomAvailableGrid();
        if (!grid) {
          console.error(`❌ 无法为卡片${index + 1}分配网格位置`);
          return {
            ...card,
            position: {
              x: Math.round(containerWidth / 2 - 80),
              y: Math.round(containerHeight / 2 - 17.5)
            },
            cardSize: { width: 160, height: 35 }
          };
        }
        const position = gridSystem.getRandomPositionInGrid(grid);
        return {
          ...card,
          position,
          cardSize: { width: 160, height: 35 },
          gridInfo: {
            gridIndex: grid.gridIndex,
            row: grid.row,
            col: grid.col
          }
        };
      });
      console.log("✅ 网格位置分配完成");
      console.log(`📊 使用了${gridSystem.occupiedGrids.size}个网格，总共${gridSystem.cols * gridSystem.rows}个可用网格`);
      console.log("🔍 验证返回数据:");
      console.log("  - updatedCards.length:", updatedCards.length);
      console.log("  - 前3张卡片:", updatedCards.slice(0, 3));
      const invalidCards = updatedCards.filter(
        (card) => !card.position || typeof card.position.x !== "number" || typeof card.position.y !== "number" || !card.cardSize || !card.word || !card.type
      );
      if (invalidCards.length > 0) {
        console.error("❌ 发现无效卡片:", invalidCards);
      } else {
        console.log("✅ 所有卡片数据验证通过");
      }
      return updatedCards;
    };
    const getGridLines = () => {
      if (!currentGridSystem.value)
        return [];
      const gridSystem = currentGridSystem.value;
      const gridLines = [];
      for (let row = 0; row < gridSystem.rows; row++) {
        for (let col = 0; col < gridSystem.cols; col++) {
          row * gridSystem.cols + col;
          const x = gridSystem.padding + col * gridSystem.gridWidth;
          const y = gridSystem.padding + row * gridSystem.gridHeight;
          gridLines.push({
            x: Math.round(x),
            y: Math.round(y),
            width: Math.round(gridSystem.gridWidth),
            height: Math.round(gridSystem.gridHeight),
            label: `${row},${col}`
          });
        }
      }
      return gridLines;
    };
    const validateCardLayout = (cards, containerWidth, containerHeight) => {
      let overlappingCount = 0;
      let outOfBoundsCount = 0;
      const margin = 30;
      console.log(`开始验证 ${cards.length} 张卡片的布局质量...`);
      for (let i = 0; i < cards.length; i++) {
        const card1 = cards[i];
        const cardRight = card1.position.x + card1.cardSize.width;
        const cardBottom = card1.position.y + card1.cardSize.height;
        if (card1.position.x < margin || card1.position.y < margin || cardRight > containerWidth - margin || cardBottom > containerHeight - margin) {
          outOfBoundsCount++;
          console.error(`❌ 卡片${i + 1}(${card1.type}:${card1.word[card1.type]})超出安全边界:`, {
            position: card1.position,
            size: card1.cardSize,
            cardRight,
            cardBottom,
            containerWidth,
            containerHeight,
            margin,
            超出左边界: card1.position.x < margin,
            超出上边界: card1.position.y < margin,
            超出右边界: cardRight > containerWidth - margin,
            超出下边界: cardBottom > containerHeight - margin
          });
        }
        for (let j = i + 1; j < cards.length; j++) {
          const card2 = cards[j];
          const rect1 = {
            x: card1.position.x,
            y: card1.position.y,
            width: card1.cardSize.width,
            height: card1.cardSize.height
          };
          const rect2 = {
            x: card2.position.x,
            y: card2.position.y,
            width: card2.cardSize.width,
            height: card2.cardSize.height
          };
          const requiredSpacing = calculateCardSpacing(card1.cardSize, card2.cardSize);
          if (isOverlapping(rect1, rect2, requiredSpacing)) {
            overlappingCount++;
            const distance = Math.sqrt(
              Math.pow(card1.position.x - card2.position.x, 2) + Math.pow(card1.position.y - card2.position.y, 2)
            );
            console.error(
              `❌ 卡片${i + 1}(${card1.type})和卡片${j + 1}(${card2.type})间距不足:`,
              `实际距离: ${Math.round(distance)}rpx < 要求间距: ${requiredSpacing}rpx`,
              `卡片1: (${card1.position.x}, ${card1.position.y}) 尺寸(${card1.cardSize.width}×${card1.cardSize.height})`,
              `卡片2: (${card2.position.x}, ${card2.position.y}) 尺寸(${card2.cardSize.width}×${card2.cardSize.height})`
            );
          }
        }
      }
      console.log(`📊 布局质量检查结果:`);
      console.log(`   - 总卡片数: ${cards.length}`);
      console.log(`   - 超出边界: ${outOfBoundsCount}张`);
      console.log(`   - 距离过近: ${overlappingCount}对`);
      console.log(`   - 容器尺寸: ${containerWidth}×${containerHeight}rpx`);
      if (overlappingCount === 0 && outOfBoundsCount === 0) {
        console.log("✅ 卡片布局质量完美，无重叠和越界问题");
        return true;
      } else {
        console.warn("⚠️ 卡片布局存在问题，需要优化算法");
        return false;
      }
    };
    const initializeGameBoard = () => {
      const words = wordsForCurrentLevel.value;
      if (words.length < 8) {
        common_vendor.index.showToast({ title: "词汇数量不足", icon: "none" });
        return;
      }
      const { containerWidth, containerHeight } = getGameBoardDimensions();
      console.log(`游戏区域尺寸: ${containerWidth}rpx × ${containerHeight}rpx`);
      let cards = [];
      const cardWidth = 160;
      const cardHeight = 35;
      const cardSize = { width: cardWidth, height: cardHeight };
      const cardData = words.slice(0, 8).map((word) => ({
        word,
        cardSize,
        // 所有卡片使用相同尺寸
        color: generateRandomLightColor()
        // 每对单词使用相同颜色
      }));
      console.log("所有卡片使用固定尺寸:", cardSize);
      for (let i = 0; i < 8; i++) {
        const { word, cardSize: cardSize2, color } = cardData[i];
        cards.push({
          id: tileIdCounter++,
          word,
          color,
          selected: false,
          matched: false,
          pairId: i,
          // 用于标识配对
          type: "english",
          // 标识为英文卡片
          cardSize: cardSize2
        });
        cards.push({
          id: tileIdCounter++,
          word,
          color,
          selected: false,
          matched: false,
          pairId: i,
          // 相同的pairId表示是一对
          type: "chinese",
          // 标识为中文卡片
          cardSize: cardSize2
        });
      }
      console.log("卡片数据创建完成，开始分配网格位置...");
      const _cards = generateGridBasedPositions(cards, containerWidth, containerHeight);
      console.log(_cards, "_cards");
      const englishCards = _cards.filter((card) => card.type === "english");
      const chineseCards = _cards.filter((card) => card.type === "chinese");
      for (let i = englishCards.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        const tempWord = englishCards[i].word;
        const tempColor = englishCards[i].color;
        const tempPairId = englishCards[i].pairId;
        englishCards[i].word = englishCards[j].word;
        englishCards[i].color = englishCards[j].color;
        englishCards[i].pairId = englishCards[j].pairId;
        englishCards[j].word = tempWord;
        englishCards[j].color = tempColor;
        englishCards[j].pairId = tempPairId;
      }
      for (let i = chineseCards.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        const tempWord = chineseCards[i].word;
        const tempColor = chineseCards[i].color;
        const tempPairId = chineseCards[i].pairId;
        chineseCards[i].word = chineseCards[j].word;
        chineseCards[i].color = chineseCards[j].color;
        chineseCards[i].pairId = chineseCards[j].pairId;
        chineseCards[j].word = tempWord;
        chineseCards[j].color = tempColor;
        chineseCards[j].pairId = tempPairId;
      }
      cards = [...englishCards, ...chineseCards];
      validateCardLayout(cards, containerWidth, containerHeight);
      console.log("所有计算完成，设置游戏状态...");
      gameBoard.value = cards;
      totalPairs.value = 8;
      matchedPairs.value = 0;
      console.log("游戏状态设置完成");
      console.log(`成功生成 ${cards.length} 张卡片，位置分布:`);
      console.log(`英文卡片 (${englishCards.length}张):`);
      englishCards.forEach((card, index) => {
        console.log(`  英文${index + 1}: ${card.word.english} (pairId:${card.pairId}) 位置(${card.position.x}, ${card.position.y})`);
      });
      console.log(`中文卡片 (${chineseCards.length}张):`);
      chineseCards.forEach((card, index) => {
        console.log(`  中文${index + 1}: ${card.word.chinese} (pairId:${card.pairId}) 位置(${card.position.x}, ${card.position.y})`);
      });
    };
    const initializeGameBoardAsync = async () => {
      console.log("🎲 开始异步初始化游戏棋盘...");
      console.log("📊 棋盘初始化数据检查:");
      console.log("  - currentLevel:", currentLevel.value);
      console.log("  - currentLevelDetail:", currentLevelDetail.value);
      console.log("  - selectedLibraryInfo:", selectedLibraryInfo.value);
      console.log("  - useMockData:", useMockData.value);
      const words = wordsForCurrentLevel.value;
      console.log("  - wordsForCurrentLevel:", words);
      console.log("  - words.length:", words.length);
      if (words.length < 8) {
        const errorMsg = `词汇数量不足: 需要8个词汇，当前只有${words.length}个`;
        console.error("❌", errorMsg);
        console.error("📊 详细状态信息:");
        debugGameState();
        throw new Error(errorMsg);
      }
      const { containerWidth, containerHeight } = getGameBoardDimensions();
      console.log(`📐 游戏区域尺寸: ${containerWidth}rpx × ${containerHeight}rpx`);
      let cards = [];
      console.log("🎨 预计算卡片尺寸和颜色...");
      const cardWidth = 160;
      const cardHeight = 35;
      const cardSize = { width: cardWidth, height: cardHeight };
      const cardData = words.slice(0, 8).map((word) => ({
        word,
        cardSize,
        // 所有卡片使用相同尺寸
        color: generateRandomLightColor()
      }));
      console.log("📏 所有卡片使用固定尺寸:", cardSize);
      console.log("📍 开始创建卡片数据...");
      for (let i = 0; i < 8; i++) {
        const { word, cardSize: cardSize2, color } = cardData[i];
        console.log(`🔄 创建第${i + 1}对卡片数据: ${word.english} / ${word.chinese}`);
        cards.push({
          id: tileIdCounter++,
          word,
          color,
          selected: false,
          matched: false,
          pairId: i,
          type: "english",
          cardSize: cardSize2
        });
        cards.push({
          id: tileIdCounter++,
          word,
          color,
          selected: false,
          matched: false,
          pairId: i,
          type: "chinese",
          cardSize: cardSize2
        });
        if (i % 2 === 1) {
          await new Promise((resolve) => setTimeout(resolve, 10));
        }
      }
      console.log("✅ 卡片数据创建完成，开始分配网格位置...");
      cards = generateGridBasedPositions(cards, containerWidth, containerHeight);
      console.log("🔄 开始批量处理游戏数据...");
      await batchProcessGameData(cards, containerWidth, containerHeight);
      console.log(`🎉 成功生成 ${cards.length} 张卡片，位置分布完成`);
      console.log("🎯 所有计算完成，设置游戏状态...");
      gameBoard.value = cards;
      totalPairs.value = 8;
      matchedPairs.value = 0;
      console.log("✅ 游戏状态设置完成，准备渲染");
    };
    const handleTileClick = (index) => {
      if (isChecking.value || isGameEndModalVisible.value)
        return;
      utils_audio.audioManager.playSoundEffect("click");
      const tile = gameBoard.value[index];
      if (tile.matched)
        return;
      if (selectedTiles.value.length >= 2)
        return;
      if (tile.selected) {
        tile.selected = false;
        selectedTiles.value = selectedTiles.value.filter(
          (item) => item.index !== index
        );
        return;
      }
      tile.selected = true;
      selectedTiles.value.push({ index, tile });
      if (selectedTiles.value.length === 2) {
        isChecking.value = true;
        setTimeout(() => {
          checkMatch();
        }, 500);
      }
    };
    const checkMatch = () => {
      const [tile1, tile2] = selectedTiles.value;
      const isValidPair = tile1.tile.type !== tile2.tile.type && tile1.tile.pairId === tile2.tile.pairId;
      if (isValidPair) {
        tile1.tile.matched = true;
        tile2.tile.matched = true;
        tile1.tile.selected = false;
        tile2.tile.selected = false;
        matchedPairs.value++;
        tile1.tile.type === "english" ? tile1.tile.word.english : tile2.tile.word.english;
        tile1.tile.type === "chinese" ? tile1.tile.word.chinese : tile2.tile.word.chinese;
        utils_audio.audioManager.playSoundEffect("success");
        utils_audio.audioManager.vibrate("short");
        console.log(`配对成功！已完成 ${matchedPairs.value}/${totalPairs.value} 对`);
        if (matchedPairs.value === totalPairs.value) {
          console.log("🎉 恭喜！所有配对完成！");
          stopGameTimer();
          gameStars.value = calculateStars(finalGameTime.value, currentLevelDetail.value);
          console.log(`⭐ 游戏完成时间: ${finalGameTime.value}秒, 获得星级: ${gameStars.value}`);
          setTimeout(() => {
            utils_audio.audioManager.playSoundEffect("complete");
            utils_audio.audioManager.vibrate("long");
          }, 500);
          setTimeout(() => {
            endGame(true);
          }, 1e3);
        }
      } else {
        utils_audio.audioManager.playSoundEffect("fail");
        api_utils.showError("匹配错误，正在重置本关...", 1500);
        setTimeout(async () => {
          await replayGame();
        }, 1500);
      }
      selectedTiles.value = [];
      isChecking.value = false;
    };
    const toggleCurrentLevelFavorite = async () => {
      var _a, _b;
      try {
        utils_audio.audioManager.playSoundEffect("click");
        const levelId = ((_a = currentLevelDetail.value) == null ? void 0 : _a.id) || ((_b = selectedLevelData.value) == null ? void 0 : _b.id) || currentLevelId.value;
        if (!levelId) {
          console.warn("无法获取关卡ID，跳过收藏操作");
          return;
        }
        if (isCurrentLevelFavorited.value) {
          await api_weixin.weixinApi.removeFavorite(levelId);
          isCurrentLevelFavorited.value = false;
          common_vendor.index.showToast({
            title: "已取消收藏",
            icon: "success",
            duration: 1500
          });
        } else {
          await api_weixin.weixinApi.addFavorite(levelId);
          isCurrentLevelFavorited.value = true;
          common_vendor.index.showToast({
            title: "已添加收藏",
            icon: "success",
            duration: 1500
          });
        }
        utils_audio.audioManager.playSoundEffect("complete");
      } catch (error) {
        console.error("收藏操作失败:", error);
        utils_audio.audioManager.playSoundEffect("fail");
        common_vendor.index.showToast({
          title: "操作失败，请重试",
          icon: "none",
          duration: 1500
        });
      }
    };
    const checkCurrentLevelFavoriteStatus = async () => {
      var _a, _b;
      try {
        const levelId = ((_a = currentLevelDetail.value) == null ? void 0 : _a.id) || ((_b = selectedLevelData.value) == null ? void 0 : _b.id) || currentLevelId.value;
        if (!levelId) {
          return;
        }
        const favorites = await api_weixin.weixinApi.getUserFavorites();
        isCurrentLevelFavorited.value = favorites.favorites.some((level) => level.id === levelId);
      } catch (error) {
        console.warn("检查收藏状态失败:", error);
        isCurrentLevelFavorited.value = false;
      }
    };
    const goBackHome = () => {
      common_vendor.index.navigateBack({ delta: 1 });
    };
    const checkDevelopmentEnvironment = () => {
      try {
        isDevelopment.value = utils_debugControl.shouldShowDebugButtons();
        console.log("开发环境检测结果:", isDevelopment.value);
      } catch (error) {
        console.error("检测开发环境失败:", error);
        isDevelopment.value = false;
      }
    };
    const goToDebug = () => {
      utils_audio.audioManager.playSoundEffect("click");
      common_vendor.index.navigateTo({
        url: "/pages/debug/index"
      });
    };
    const closeSettings = () => {
      showSettingsModal.value = false;
      console.log("关闭设置弹窗");
    };
    const handleSettingsChange = (newSettings) => {
      gameSettings.value = { ...newSettings };
      console.log("设置已更新:", newSettings);
      if (newSettings.backgroundMusic !== gameSettings.value.backgroundMusic) {
        if (newSettings.backgroundMusic) {
          utils_audio.audioManager.playBackgroundMusic("game");
        } else {
          utils_audio.audioManager.stopBackgroundMusic();
        }
      }
    };
    const resetGame = () => {
      selectedTiles.value = [];
      matchedPairs.value = 0;
      isChecking.value = false;
      isGameEndModalVisible.value = false;
      initializeGameBoard();
    };
    const replayGame = async () => {
      try {
        console.log("🎮 开始重玩游戏...");
        isReplaying.value = true;
        isGameReady.value = false;
        isGameInitializing.value = false;
        isCalculatingPositions.value = true;
        isPreparingRender.value = false;
        isLoadingGame.value = false;
        selectedTiles.value = [];
        matchedPairs.value = 0;
        isChecking.value = false;
        isGameEndModalVisible.value = false;
        console.log("🧮 重新计算卡片位置...");
        await new Promise((resolve) => setTimeout(resolve, 100));
        await initializeGameBoardAsync();
        console.log("🎨 准备重新渲染...");
        isCalculatingPositions.value = false;
        isPreparingRender.value = true;
        await new Promise((resolve) => setTimeout(resolve, 100));
        if (!checkRenderReadiness()) {
          throw new Error("重玩数据生成失败");
        }
        console.log("🎮 重新加载游戏...");
        isPreparingRender.value = false;
        isLoadingGame.value = true;
        loadingProgress.value = 0;
        loadingMessage.value = "正在重新启动游戏...";
        await simulateReplayLoading(1200);
        console.log("🎉 重玩完成，开始渲染");
        isLoadingGame.value = false;
        isReplaying.value = false;
        isGameReady.value = true;
        console.log("⏱️ 重新开始游戏计时...");
        startGameTimer();
        console.log("✨ 重玩成功，欢迎再次挑战！");
      } catch (error) {
        console.error("❌ 重玩失败:", error);
        isReplaying.value = false;
        isGameInitializing.value = false;
        isCalculatingPositions.value = false;
        isPreparingRender.value = false;
        isLoadingGame.value = false;
        isGameReady.value = false;
        api_utils.showError(`重玩失败: ${error.message || "未知错误"}`);
      }
    };
    const nextLevel = () => {
      if (currentLevelDetail.value) {
        console.log("🎯 从扩展关卡系统返回关卡选择页面");
        common_vendor.index.navigateBack({
          delta: 1,
          success: () => {
            api_utils.showSuccess("请选择下一关卡", 1e3);
          },
          fail: () => {
            common_vendor.index.redirectTo({
              url: "/pages/level-selection/index"
            });
          }
        });
        return;
      }
      if (canGoToNextLevel.value) {
        currentLevelId.value++;
        currentLevel.value = {
          id: currentLevelId.value,
          name: `第${currentLevelId.value}关`,
          wordsCount: 8
        };
        if (selectedLibraryInfo.value) {
          saveCurrentLevelId(selectedLibraryInfo.value.id, currentLevelId.value);
        }
        resetGame();
        api_utils.showSuccess(`进入第${currentLevelId.value}关`, 1e3);
      } else {
        api_utils.showError("已经是最后一关了", 1e3);
      }
    };
    const endGame = async (won) => {
      gameWon.value = won;
      if (won) {
        gameResultText.value = "恭喜过关！";
        await handleGameCompletion();
      } else {
        gameResultText.value = isTimeUp.value ? "时间到期！" : "挑战失败！";
        await handleGameFailure();
      }
      isGameEndModalVisible.value = true;
    };
    const handleGameCompletion = async () => {
      try {
        isProgressSyncing.value = true;
        const selectedLevel = common_vendor.index.getStorageSync("selectedLevel");
        if (selectedLevel) {
          try {
            const levelData = JSON.parse(selectedLevel);
            const completedKey = `level_${levelData.id}_completed`;
            common_vendor.index.setStorageSync(completedKey, "true");
            console.log(`Level ${levelData.id} marked as completed locally`);
          } catch (e) {
            console.error("Failed to save local level completion:", e);
          }
        }
        await callCompleteLevelAPI();
        await refreshUserInfo();
      } catch (error) {
        console.error("处理游戏完成失败:", error);
      } finally {
        isProgressSyncing.value = false;
      }
    };
    const handleGameFailure = async () => {
      try {
        isProgressSyncing.value = true;
        console.log("游戏失败，记录尝试:", {
          finalTime: finalGameTime.value,
          isTimeUp: isTimeUp.value,
          stars: gameStars.value
        });
      } catch (error) {
        console.error("处理游戏失败失败:", error);
      } finally {
        isProgressSyncing.value = false;
      }
    };
    const callCompleteLevelAPI = async () => {
      try {
        if (!userInfo.value || !userInfo.value.id) {
          console.warn("用户信息不存在，跳过通关接口调用");
          return;
        }
        let levelId = "";
        if (currentLevelDetail.value) {
          levelId = currentLevelDetail.value.id;
        } else if (selectedLevelData.value) {
          levelId = selectedLevelData.value.id;
        } else if (currentLevel.value) {
          levelId = currentLevel.value.id.toString();
        }
        if (!levelId) {
          console.warn("未找到关卡ID，跳过通关接口调用");
          return;
        }
        console.log("调用通关接口:", {
          userId: userInfo.value.id,
          levelId,
          completionTime: finalGameTime.value,
          stars: gameStars.value
        });
        const completeLevelResponse = await api_weixin.weixinApi.completeLevel(
          api_weixin.weixinApi.getOpenid(),
          levelId,
          finalGameTime.value
        );
        console.log("通关接口调用成功:", completeLevelResponse);
        if (completeLevelResponse.stars) {
          gameStars.value = completeLevelResponse.stars;
          console.log(`⭐ 服务端返回星级: ${completeLevelResponse.stars}`);
        }
        if (currentLevelDetail.value) {
          currentLevelDetail.value.isCompleted = true;
          const extendedLevel = currentLevelDetail.value;
          if ("userStars" in extendedLevel) {
            extendedLevel.userStars = Math.max(extendedLevel.userStars || 0, gameStars.value);
            extendedLevel.bestTime = Math.min(extendedLevel.bestTime || Infinity, finalGameTime.value);
          }
        }
        if (completeLevelResponse.hasUnlockedNewLevel) {
          utils_audio.audioManager.playSoundEffect("unlock");
          common_vendor.index.showModal({
            title: "恭喜通关！",
            content: `${completeLevelResponse.message}
已解锁 ${completeLevelResponse.unlockedLevels} 关！`,
            showCancel: false,
            confirmText: "太棒了"
          });
        } else {
          if (!completeLevelResponse.isVip && completeLevelResponse.remainingUnlocks <= 0) {
            common_vendor.index.showModal({
              title: "通关成功",
              content: `${completeLevelResponse.message}
今日解锁次数已用完，明天再来或分享获得额外机会！`,
              showCancel: true,
              cancelText: "明天再来",
              confirmText: "立即分享",
              success: (res) => {
                if (res.confirm) {
                  common_vendor.index.showShareMenu({
                    withShareTicket: true
                  });
                }
              }
            });
          } else {
            api_utils.showSuccess("恭喜通关！进度已同步", 2e3);
          }
        }
        await refreshUserInfo();
      } catch (error) {
        console.error("调用通关接口失败:", error);
        api_utils.showError("通关记录失败，但不影响游戏", 2e3);
      }
    };
    const refreshUserInfo = async () => {
      try {
        const freshUserInfo = await api_weixin.weixinApi.refreshUserInfo();
        if (freshUserInfo) {
          userInfo.value = freshUserInfo;
          common_vendor.index.setStorageSync("userInfo", JSON.stringify(freshUserInfo));
          console.log("用户信息已刷新并保存到本地:", freshUserInfo);
        }
      } catch (error) {
        console.error("刷新用户信息失败:", error);
      }
    };
    const onShareAppMessage = async (options) => {
      var _a, _b, _c, _d;
      console.log("游戏页面分享触发:", options);
      const shareParams = {
        page: "pages/game/index",
        levelId: ((_a = currentLevelDetail.value) == null ? void 0 : _a.id) || ((_b = selectedLevelData.value) == null ? void 0 : _b.id),
        userId: (_c = userInfo.value) == null ? void 0 : _c.id
      };
      if ((_d = userInfo.value) == null ? void 0 : _d.id) {
        handleGameShareReward(options);
      }
      return await utils_share.shareUtils.handleShareAppMessage(options, shareParams);
    };
    const checkDailyShareReward = (userId) => {
      try {
        const today = (/* @__PURE__ */ new Date()).toDateString();
        const storageKey = `daily_share_reward_${userId}_${today}`;
        const hasSharedToday = common_vendor.index.getStorageSync(storageKey);
        console.log(`检查每日分享奖励状态 - 用户: ${userId}, 日期: ${today}, 已分享: ${!!hasSharedToday}`);
        return !!hasSharedToday;
      } catch (error) {
        console.error("检查每日分享奖励状态失败:", error);
        return false;
      }
    };
    const markDailyShareReward = (userId) => {
      try {
        const today = (/* @__PURE__ */ new Date()).toDateString();
        const storageKey = `daily_share_reward_${userId}_${today}`;
        common_vendor.index.setStorageSync(storageKey, true);
        console.log(`标记每日分享奖励完成 - 用户: ${userId}, 日期: ${today}`);
      } catch (error) {
        console.error("标记每日分享奖励失败:", error);
      }
    };
    const handleGameShareReward = async (options, shareParams) => {
      var _a;
      try {
        if (!((_a = userInfo.value) == null ? void 0 : _a.id)) {
          console.warn("用户信息不存在，无法获取分享奖励");
          return;
        }
        if (isHandlingGameShareReward) {
          console.log("游戏分享奖励正在处理中，跳过重复请求");
          return;
        }
        const hasSharedToday = checkDailyShareReward(userInfo.value.id);
        if (hasSharedToday) {
          console.log("今日已获取过分享奖励，跳过本次请求");
          common_vendor.index.showToast({
            title: "今日已获得分享奖励",
            icon: "none",
            duration: 2e3
          });
          return;
        }
        isHandlingGameShareReward = true;
        console.log("开始处理游戏页面分享奖励:", options);
        setTimeout(async () => {
          try {
            const rewardResponse = await api_weixin.weixinApi.getShareReward();
            if (rewardResponse.success) {
              markDailyShareReward(userInfo.value.id);
              if (rewardResponse.userInfo) {
                userInfo.value = rewardResponse.userInfo;
                common_vendor.index.setStorageSync("userInfo", JSON.stringify(rewardResponse.userInfo));
              }
              common_vendor.index.showModal({
                title: "分享奖励",
                content: `恭喜获得${rewardResponse.reward.description}！可以继续挑战更多关卡了！今日分享奖励已领取完毕。`,
                showCancel: false,
                confirmText: "太棒了",
                success: () => {
                  console.log("游戏分享奖励提示已显示");
                }
              });
              console.log("游戏分享奖励获取成功:", rewardResponse.reward);
            } else {
              console.log("游戏分享奖励获取失败:", rewardResponse.message);
              if (rewardResponse.message.includes("今日") || rewardResponse.message.includes("已领取")) {
                markDailyShareReward(userInfo.value.id);
              }
            }
          } catch (error) {
            console.error("获取游戏分享奖励失败:", error);
          } finally {
            isHandlingGameShareReward = false;
            console.log("游戏分享奖励处理完成，重置状态");
          }
        }, 2e3);
      } catch (error) {
        console.error("处理游戏分享奖励失败:", error);
        isHandlingGameShareReward = false;
      }
    };
    __expose({
      onShareAppMessage
    });
    return (_ctx, _cache) => {
      var _a;
      return common_vendor.e({
        a: currentLevelDetail.value
      }, currentLevelDetail.value ? common_vendor.e({
        b: common_vendor.t(currentLevelDetail.value.name),
        c: common_vendor.t(((_a = currentLevelDetail.value.phrases) == null ? void 0 : _a.length) || 0),
        d: currentLevelDetail.value.isCompleted
      }, currentLevelDetail.value.isCompleted ? {} : {}) : selectedLibraryInfo.value ? {
        f: common_vendor.t(selectedLibraryInfo.value.name)
      } : {}, {
        e: selectedLibraryInfo.value,
        g: currentLevel.value
      }, currentLevel.value ? common_vendor.e({
        h: common_vendor.t(currentLevel.value.name),
        i: common_vendor.t(matchedPairs.value),
        j: common_vendor.t(totalPairs.value),
        k: common_vendor.t(formatGameTime(currentGameTime.value)),
        l: currentGameTime.value <= 10 ? 1 : "",
        m: isProgressSyncing.value
      }, isProgressSyncing.value ? {} : {}, {
        n: useMockData.value
      }, useMockData.value ? {} : {}, {
        o: isDevelopment.value
      }, isDevelopment.value ? {
        p: common_vendor.t(debugMode.value ? "关闭调试" : "调试模式"),
        q: common_vendor.o(($event) => debugMode.value = !debugMode.value)
      } : {}, {
        r: debugMode.value && isDevelopment.value
      }, debugMode.value && isDevelopment.value ? {
        s: common_vendor.t(showGridLines.value ? "隐藏网格" : "显示网格"),
        t: common_vendor.o(($event) => showGridLines.value = !showGridLines.value)
      } : {}, {
        v: common_vendor.t(isReplaying.value ? "重玩中..." : "重玩"),
        w: !isGameReady.value ? 1 : "",
        x: common_vendor.o(($event) => isGameReady.value ? replayGame() : null),
        y: isGameInitializing.value
      }, isGameInitializing.value ? {} : isCalculatingPositions.value ? {} : isPreparingRender.value ? {} : isLoadingGame.value ? {
        C: common_vendor.t(isReplaying.value ? "重新开始游戏..." : "加载游戏中..."),
        D: common_vendor.t(loadingMessage.value || (isReplaying.value ? "正在为您重新准备游戏" : "正在为您准备精彩的游戏体验")),
        E: loadingProgress.value + "%",
        F: common_vendor.t(loadingProgress.value)
      } : !isGameReady.value && !isGameInitializing.value && !isCalculatingPositions.value && !isPreparingRender.value && !isLoadingGame.value ? {
        H: common_vendor.o(initializeGame)
      } : isGameReady.value ? common_vendor.e({
        J: showGridLines.value && currentGridSystem.value
      }, showGridLines.value && currentGridSystem.value ? {
        K: common_vendor.f(getGridLines(), (grid, index, i0) => {
          return {
            a: common_vendor.t(grid.label),
            b: `grid-${index}`,
            c: grid.x + "rpx",
            d: grid.y + "rpx",
            e: grid.width + "rpx",
            f: grid.height + "rpx"
          };
        })
      } : {}, {
        L: common_vendor.f(gameBoard.value, (tile, index, i0) => {
          return common_vendor.e({
            a: tile.type === "english"
          }, tile.type === "english" ? {
            b: common_vendor.t(tile.word ? tile.word.english : "")
          } : {}, {
            c: tile.type === "chinese"
          }, tile.type === "chinese" ? {
            d: common_vendor.t(tile.word ? tile.word.chinese : "")
          } : {}, {
            e: tile.id,
            f: tile.selected ? 1 : "",
            g: tile.matched ? 1 : "",
            h: tile.type === "english" ? 1 : "",
            i: tile.type === "chinese" ? 1 : "",
            j: tile.type === "english" && tile.word && tile.word.english.length <= 4 ? 1 : "",
            k: tile.type === "english" && tile.word && tile.word.english.length > 4 && tile.word.english.length <= 7 ? 1 : "",
            l: tile.type === "english" && tile.word && tile.word.english.length > 7 ? 1 : "",
            m: tile.color,
            n: tile.position.x + "rpx",
            o: tile.position.y + "rpx",
            p: tile.cardSize.width + "rpx",
            q: tile.cardSize.height + "rpx",
            r: debugMode.value ? `(${Math.round(tile.position.x)}, ${Math.round(tile.position.y)})` : "",
            s: common_vendor.o(($event) => handleTileClick(index), tile.id)
          });
        }),
        M: debugMode.value ? 1 : "",
        N: isChecking.value ? 1 : ""
      }) : {}, {
        z: isCalculatingPositions.value,
        A: isPreparingRender.value,
        B: isLoadingGame.value,
        G: !isGameReady.value && !isGameInitializing.value && !isCalculatingPositions.value && !isPreparingRender.value && !isLoadingGame.value,
        I: isGameReady.value
      }) : {}, {
        O: isGameEndModalVisible.value
      }, isGameEndModalVisible.value ? common_vendor.e({
        P: common_vendor.t(gameResultText.value),
        Q: gameWon.value
      }, gameWon.value ? {
        R: common_vendor.f(3, (star, k0, i0) => {
          return {
            a: star,
            b: star <= gameStars.value ? 1 : ""
          };
        }),
        S: common_vendor.t(formatGameTime(finalGameTime.value)),
        T: common_vendor.t(isCurrentLevelFavorited.value ? "💖" : "🤍"),
        U: common_vendor.t(isCurrentLevelFavorited.value ? "已收藏" : "收藏关卡"),
        V: isCurrentLevelFavorited.value ? 1 : "",
        W: common_vendor.o(toggleCurrentLevelFavorite)
      } : {}, {
        X: gameWon.value && canGoToNextLevel.value
      }, gameWon.value && canGoToNextLevel.value ? {
        Y: common_vendor.t(currentLevelDetail.value ? "选择关卡" : "下一关"),
        Z: common_vendor.o(nextLevel)
      } : {}, {
        aa: common_vendor.o(replayGame),
        ab: common_vendor.o(goBackHome)
      }) : {}, {
        ac: common_vendor.o(closeSettings),
        ad: common_vendor.o(handleSettingsChange),
        ae: common_vendor.p({
          visible: showSettingsModal.value
        }),
        af: isDevelopment.value
      }, isDevelopment.value ? {
        ag: common_vendor.o(goToDebug)
      } : {});
    };
  }
});
_sfc_defineComponent.__runtimeHooks = 2;
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_defineComponent, [["__scopeId", "data-v-131fc1ab"]]);
wx.createPage(MiniProgramPage);
