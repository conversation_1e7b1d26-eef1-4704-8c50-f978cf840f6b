<view class="page-box data-v-131fc1ab"><view class="game-page-container data-v-131fc1ab"><view wx:if="{{a}}" class="level-detail-info card data-v-131fc1ab"><view class="level-header data-v-131fc1ab"><text class="level-name data-v-131fc1ab">{{b}}</text></view><view class="level-stats data-v-131fc1ab"><text class="stats-item data-v-131fc1ab">词组数量: {{c}}</text><text wx:if="{{d}}" class="stats-item data-v-131fc1ab">已完成</text><text wx:else class="stats-item data-v-131fc1ab">🎯 挑战中</text></view></view><view wx:elif="{{e}}" class="selected-library-info card data-v-131fc1ab"><text class="library-name data-v-131fc1ab">{{f}}</text></view><view wx:if="{{g}}" class="game-area card data-v-131fc1ab"><view class="game-info-bar data-v-131fc1ab"><text class="data-v-131fc1ab">关卡: {{h}}</text><text class="data-v-131fc1ab">已配对: {{i}}/{{j}}</text><text class="{{['game-timer', 'data-v-131fc1ab', l && 'time-warning']}}"> ⏱️ {{k}}</text><text wx:if="{{m}}" class="sync-status data-v-131fc1ab">同步中...</text><text wx:if="{{n}}" class="h5-mock-tip data-v-131fc1ab">H5演示</text><view wx:if="{{o}}" class="debug-btn data-v-131fc1ab" bindtap="{{q}}"><text class="debug-btn-text data-v-131fc1ab">{{p}}</text></view><view wx:if="{{r}}" class="grid-debug-btn data-v-131fc1ab" bindtap="{{t}}"><text class="grid-debug-btn-text data-v-131fc1ab">{{s}}</text></view><view class="{{['replay-btn', 'data-v-131fc1ab', w && 'replay-btn-disabled']}}" bindtap="{{x}}"><text class="replay-btn-text data-v-131fc1ab">{{v}}</text></view></view><view wx:if="{{y}}" class="loading-container data-v-131fc1ab"><view class="loading-spinner data-v-131fc1ab"></view><text class="loading-text data-v-131fc1ab">正在初始化游戏...</text></view><view wx:elif="{{z}}" class="loading-container data-v-131fc1ab"><view class="loading-spinner data-v-131fc1ab"></view><text class="loading-text data-v-131fc1ab">正在计算卡片位置...</text></view><view wx:elif="{{A}}" class="loading-container data-v-131fc1ab"><view class="loading-spinner data-v-131fc1ab"></view><text class="loading-text data-v-131fc1ab">准备渲染游戏...</text></view><view wx:elif="{{B}}" class="game-loading-container data-v-131fc1ab"><view class="game-loading-content data-v-131fc1ab"><view class="game-loading-spinner data-v-131fc1ab"><view class="spinner-ring data-v-131fc1ab"></view><view class="spinner-ring data-v-131fc1ab"></view><view class="spinner-ring data-v-131fc1ab"></view></view><text class="game-loading-title data-v-131fc1ab">{{C}}</text><text class="game-loading-subtitle data-v-131fc1ab">{{D}}</text><view class="loading-progress data-v-131fc1ab"><view class="progress-bar data-v-131fc1ab"><view class="progress-fill data-v-131fc1ab" style="{{'width:' + E}}"></view></view><text class="progress-text data-v-131fc1ab">{{F}}%</text></view></view></view><view wx:elif="{{G}}" class="error-container data-v-131fc1ab"><text class="error-text data-v-131fc1ab">游戏初始化失败</text><view class="retry-btn data-v-131fc1ab" bindtap="{{H}}"><text class="retry-btn-text data-v-131fc1ab">重试</text></view></view><view wx:elif="{{I}}" class="{{['game-board', 'data-v-131fc1ab', N && 'checking-match']}}"><view wx:if="{{J}}" class="grid-overlay data-v-131fc1ab"><view wx:for="{{K}}" wx:for-item="grid" wx:key="b" class="grid-line data-v-131fc1ab" style="{{'position:' + 'absolute' + ';' + ('left:' + grid.c) + ';' + ('top:' + grid.d) + ';' + ('width:' + grid.e) + ';' + ('height:' + grid.f) + ';' + ('border:' + '2rpx dashed rgba(255, 0, 0, 0.5)') + ';' + ('background-color:' + 'rgba(255, 0, 0, 0.1)') + ';' + ('pointer-events:' + 'none')}}"><text class="grid-label data-v-131fc1ab">{{grid.a}}</text></view></view><view wx:for="{{L}}" wx:for-item="tile" wx:key="e" class="{{['board-tile', 'data-v-131fc1ab', tile.f && 'selected', tile.g && 'matched', M && 'debug-mode', tile.h && 'tile-english', tile.i && 'tile-chinese', tile.j && 'tile-short', tile.k && 'tile-medium', tile.l && 'tile-long']}}" style="{{'background-color:' + tile.m + ';' + ('position:' + 'absolute') + ';' + ('left:' + tile.n) + ';' + ('top:' + tile.o) + ';' + ('width:' + tile.p) + ';' + ('height:' + tile.q)}}" data-position="{{tile.r}}" bindtap="{{tile.s}}"><text wx:if="{{tile.a}}" class="tile-word data-v-131fc1ab">{{tile.b}}</text><text wx:if="{{tile.c}}" class="tile-chinese-only data-v-131fc1ab">{{tile.d}}</text></view></view></view><view wx:if="{{O}}" class="modal-overlay data-v-131fc1ab"><view class="modal-content data-v-131fc1ab"><text class="modal-title data-v-131fc1ab">{{P}}</text><view wx:if="{{Q}}" class="game-completion-info data-v-131fc1ab"><view class="stars-display data-v-131fc1ab"><text class="stars-label data-v-131fc1ab">获得星级</text><view class="stars-container data-v-131fc1ab"><text wx:for="{{R}}" wx:for-item="star" wx:key="a" class="{{['star-icon', 'data-v-131fc1ab', star.b && 'star-filled']}}"> ⭐ </text></view></view><view class="completion-time data-v-131fc1ab"><text class="time-label data-v-131fc1ab">完成时间</text><text class="time-value data-v-131fc1ab">{{S}}</text></view><view class="favorite-section data-v-131fc1ab"><button class="{{['favorite-btn', 'data-v-131fc1ab', V && 'favorited']}}" bindtap="{{W}}"><text class="favorite-icon data-v-131fc1ab">{{T}}</text><text class="favorite-text data-v-131fc1ab">{{U}}</text></button></view></view><view class="modal-buttons data-v-131fc1ab"><button wx:if="{{X}}" bindtap="{{Z}}" class="modal-button primary data-v-131fc1ab">{{Y}}</button><button bindtap="{{aa}}" class="modal-button data-v-131fc1ab">再试一次</button><button bindtap="{{ab}}" class="modal-button data-v-131fc1ab">返回首页</button></view></view></view></view><settings-modal wx:if="{{ae}}" class="data-v-131fc1ab" bindclose="{{ac}}" bindsettingsChange="{{ad}}" u-i="131fc1ab-0" bind:__l="__l" u-p="{{ae}}"/><view wx:if="{{af}}" class="floating-debug-btn data-v-131fc1ab" bindtap="{{ag}}"><text class="debug-icon data-v-131fc1ab">🔧</text></view></view>