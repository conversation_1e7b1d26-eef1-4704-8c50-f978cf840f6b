/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.settings-modal-overlay.data-v-67bc804a {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  -webkit-backdrop-filter: blur(4rpx);
          backdrop-filter: blur(4rpx);
}
.settings-modal.data-v-67bc804a {
  width: 600rpx;
  max-height: 80vh;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  overflow: hidden;
  animation: modalSlideIn-67bc804a 0.3s ease-out;
}
@keyframes modalSlideIn-67bc804a {
from {
    opacity: 0;
    transform: translateY(-50rpx) scale(0.9);
}
to {
    opacity: 1;
    transform: translateY(0) scale(1);
}
}
.modal-header.data-v-67bc804a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #ffffff;
}
.modal-title.data-v-67bc804a {
  font-size: 32rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.close-btn.data-v-67bc804a {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}
.close-btn.data-v-67bc804a:active {
  background: rgba(255, 255, 255, 0.3);
}
.close-icon.data-v-67bc804a {
  font-size: 24rpx;
  font-weight: bold;
}
.modal-content.data-v-67bc804a {
  padding: 32rpx;
}
.setting-item.data-v-67bc804a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}
.setting-item.data-v-67bc804a:last-child {
  border-bottom: none;
}
.setting-info.data-v-67bc804a {
  flex: 1;
  margin-right: 32rpx;
}
.setting-label.data-v-67bc804a {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 8rpx;
}
.setting-desc.data-v-67bc804a {
  display: block;
  font-size: 24rpx;
  color: #718096;
  line-height: 1.4;
}
.modal-footer.data-v-67bc804a {
  padding: 24rpx 32rpx 32rpx;
  display: flex;
  gap: 16rpx;
}
.test-btn.data-v-67bc804a {
  flex: 1;
  padding: 20rpx;
  background: linear-gradient(135deg, #48bb78, #38a169);
  border-radius: 16rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(72, 187, 120, 0.3);
}
.test-btn-text.data-v-67bc804a {
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 500;
}
.confirm-btn.data-v-67bc804a {
  flex: 1;
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}
.confirm-btn-text.data-v-67bc804a {
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 500;
}
.test-btn.data-v-67bc804a:active,
.confirm-btn.data-v-67bc804a:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}