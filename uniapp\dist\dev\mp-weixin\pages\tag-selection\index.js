"use strict";
const common_vendor = require("../../common/vendor.js");
const api_weixin = require("../../api/weixin.js");
const utils_audio = require("../../utils/audio.js");
const utils_auth = require("../../utils/auth.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const isLoading = common_vendor.ref(false);
    const error = common_vendor.ref(null);
    const tags = common_vendor.ref([]);
    const userInfo = common_vendor.ref(null);
    const isVipUser = common_vendor.computed(() => {
      var _a;
      return ((_a = userInfo.value) == null ? void 0 : _a.isVip) || false;
    });
    common_vendor.onLoad(async () => {
      const isLoggedIn = await utils_auth.checkLoginAndRedirect({
        toastMessage: "请先登录以查看标签挑战",
        redirectUrl: "/pages/login/index"
      });
      if (!isLoggedIn) {
        return;
      }
      try {
        const storedUserInfo = common_vendor.index.getStorageSync("userInfo");
        if (storedUserInfo) {
          userInfo.value = JSON.parse(storedUserInfo);
        }
      } catch (error2) {
        console.warn("获取用户信息失败:", error2);
      }
      await loadTags();
    });
    const loadTags = async () => {
      try {
        isLoading.value = true;
        error.value = null;
        const response = await api_weixin.weixinApi.getActiveTags();
        tags.value = response;
        console.log("标签列表加载成功:", response);
      } catch (err) {
        console.error("加载标签列表失败:", err);
        error.value = "加载失败，请重试";
      } finally {
        isLoading.value = false;
      }
    };
    const selectTag = async (tag) => {
      if (!tag.isActive) {
        utils_audio.audioManager.playSoundEffect("fail");
        common_vendor.index.showToast({
          title: "该标签尚未激活",
          icon: "none",
          duration: 1500
        });
        return;
      }
      if (tag.isVip && !isVipUser.value) {
        utils_audio.audioManager.playSoundEffect("fail");
        common_vendor.index.showToast({
          title: "该标签需要VIP权限",
          icon: "none",
          duration: 1500
        });
        return;
      }
      utils_audio.audioManager.playSoundEffect("click");
      console.log("Selected tag:", tag);
      common_vendor.index.navigateTo({
        url: `/pages/tag-levels/index?tagId=${tag.id}`
      });
    };
    const getDifficultyText = (difficulty) => {
      if (!difficulty)
        return "普通";
      switch (difficulty) {
        case 1:
          return "简单";
        case 2:
          return "普通";
        case 3:
          return "困难";
        case 4:
          return "专家";
        case 5:
          return "大师";
        default:
          return "普通";
      }
    };
    const goBack = () => {
      utils_audio.audioManager.playSoundEffect("click");
      common_vendor.index.navigateBack();
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: isLoading.value
      }, isLoading.value ? {} : error.value ? {
        c: common_vendor.t(error.value),
        d: common_vendor.o(loadTags)
      } : tags.value.length === 0 ? {} : {
        f: common_vendor.t(tags.value.length),
        g: common_vendor.f(tags.value, (tag, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(tag.icon || "🏷️"),
            b: tag.isVip
          }, tag.isVip ? {} : {}, {
            c: common_vendor.t(tag.name),
            d: common_vendor.t(tag.description),
            e: common_vendor.t(tag.levelCount || 0),
            f: common_vendor.t(getDifficultyText(tag.difficulty)),
            g: !tag.isActive
          }, !tag.isActive ? {} : tag.isVip && !isVipUser.value ? {} : {}, {
            h: tag.isVip && !isVipUser.value,
            i: tag.id,
            j: tag.isVip ? 1 : "",
            k: !tag.isActive ? 1 : "",
            l: common_vendor.o(($event) => selectTag(tag), tag.id)
          });
        })
      }, {
        b: error.value,
        e: tags.value.length === 0,
        h: common_vendor.o(goBack)
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-774f4f28"]]);
wx.createPage(MiniProgramPage);
