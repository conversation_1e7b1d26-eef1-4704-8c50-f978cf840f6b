/**
 * API配置文件
 * 包含API基础配置、环境配置等
 */

import { getApiBaseUrl, getApiUrl } from '@/utils/env'

// API基础配置
export const API_CONFIG = {
  // 微信小程序API前缀
  WEIXIN_PREFIX: '/api/v1/weixin',

  // 请求超时时间（毫秒）
  TIMEOUT: 10000,

  // 重试次数
  RETRY_COUNT: 3,

  // 重试延迟（毫秒）
  RETRY_DELAY: 1000,
}

// 获取当前环境的API基础地址
export const getBaseUrl = (): string => {
  return getApiBaseUrl()
}

// 获取微信API地址
export const getWeixinApiUrl = (path: string): string => {
  return getApiUrl(path)
}

// HTTP状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const

// 错误消息映射
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  TIMEOUT_ERROR: '请求超时，请稍后重试',
  SERVER_ERROR: '服务器错误，请稍后重试',
  UNAUTHORIZED: '未授权访问，请重新登录',
  NOT_FOUND: '请求的资源不存在',
  BAD_REQUEST: '请求参数错误',
  UNKNOWN_ERROR: '未知错误，请稍后重试',
} as const

// 存储键名
export const STORAGE_KEYS = {
  USER_INFO: 'userInfo',
  USER_OPENID: 'userOpenid',
  SELECTED_LIBRARY: 'selectedLibrary',
  SELECTED_LEVEL: 'selectedLevel',
  LEVEL_PROGRESS: 'levelProgress',
} as const
