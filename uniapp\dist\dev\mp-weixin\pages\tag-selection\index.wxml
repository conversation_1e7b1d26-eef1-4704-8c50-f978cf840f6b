<view class="tag-selection-container data-v-774f4f28"><view class="page-header data-v-774f4f28"><text class="page-title data-v-774f4f28">选择需要挑战的考点</text><text class="page-subtitle data-v-774f4f28">选择你想挑战的数学考点类型</text></view><view wx:if="{{a}}" class="loading-container data-v-774f4f28"><view class="loading-spinner data-v-774f4f28"></view><text class="loading-text data-v-774f4f28">正在加载标签...</text></view><view wx:elif="{{b}}" class="error-container data-v-774f4f28"><view class="error-icon data-v-774f4f28">⚠️</view><text class="error-title data-v-774f4f28">加载失败</text><text class="error-text data-v-774f4f28">{{c}}</text><button class="retry-btn data-v-774f4f28" bindtap="{{d}}"><text class="retry-text data-v-774f4f28">重试</text></button></view><view wx:elif="{{e}}" class="empty-container data-v-774f4f28"><view class="empty-icon data-v-774f4f28">🏷️</view><text class="empty-title data-v-774f4f28">暂无标签</text><text class="empty-text data-v-774f4f28">当前没有可用的标签挑战</text></view><view wx:else class="tags-list data-v-774f4f28"><view class="list-header data-v-774f4f28"><text class="list-title data-v-774f4f28">共{{f}}个标签</text></view><view class="tags-grid data-v-774f4f28"><view wx:for="{{g}}" wx:for-item="tag" wx:key="i" class="{{['tag-card', 'data-v-774f4f28', tag.j && 'tag-vip', tag.k && 'tag-inactive']}}" bindtap="{{tag.l}}"><view class="tag-icon data-v-774f4f28"><text class="icon-text data-v-774f4f28">{{tag.a}}</text><text wx:if="{{tag.b}}" class="vip-crown data-v-774f4f28">👑</text></view><view class="tag-info data-v-774f4f28"><text class="tag-name data-v-774f4f28">{{tag.c}}</text><text class="tag-desc data-v-774f4f28">{{tag.d}}</text><view class="tag-stats data-v-774f4f28"><view class="stat-item data-v-774f4f28"><text class="stat-label data-v-774f4f28">关卡数</text><text class="stat-value data-v-774f4f28">{{tag.e}}</text></view><view class="stat-item data-v-774f4f28"><text class="stat-label data-v-774f4f28">难度</text><text class="stat-value data-v-774f4f28">{{tag.f}}</text></view></view></view><view class="tag-status data-v-774f4f28"><view wx:if="{{tag.g}}" class="status-badge inactive data-v-774f4f28"><text class="status-text data-v-774f4f28">未激活</text></view><view wx:elif="{{tag.h}}" class="status-badge vip-only data-v-774f4f28"><text class="status-text data-v-774f4f28">VIP专享</text></view><view wx:else class="status-badge available data-v-774f4f28"><text class="status-text data-v-774f4f28">开始挑战</text></view></view></view></view></view><view class="bottom-actions data-v-774f4f28"><button class="back-btn data-v-774f4f28" bindtap="{{h}}"><text class="back-text data-v-774f4f28">返回</text></button></view></view>