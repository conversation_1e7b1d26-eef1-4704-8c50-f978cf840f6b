<view class="level-selection-container data-v-5578dee7"><view class="header-section data-v-5578dee7"><view class="header-content data-v-5578dee7"><text class="page-title data-v-5578dee7">选择关卡</text><text class="page-subtitle data-v-5578dee7">选择你想要挑战的关卡开始游戏吧！</text></view><view wx:if="{{a}}" class="progress-card data-v-5578dee7"><view class="progress-item data-v-5578dee7"><text class="progress-label data-v-5578dee7">已解锁</text><text class="progress-value data-v-5578dee7">{{b}}</text></view><view class="progress-item data-v-5578dee7"><text class="progress-label data-v-5578dee7">已完成</text><text class="progress-value data-v-5578dee7">{{c}}</text></view><view class="progress-item data-v-5578dee7"><text class="progress-label data-v-5578dee7">总关卡</text><text class="progress-value data-v-5578dee7">{{d}}</text></view></view></view><view wx:if="{{e}}" class="loading-container data-v-5578dee7"><view class="loading-spinner data-v-5578dee7"></view><text class="loading-text data-v-5578dee7">正在加载关卡...</text></view><view wx:elif="{{f}}" class="error-container data-v-5578dee7"><view class="error-icon data-v-5578dee7">⚠️</view><text class="error-title data-v-5578dee7">加载失败</text><text class="error-text data-v-5578dee7">{{g}}</text><button class="retry-btn data-v-5578dee7" bindtap="{{h}}"><text class="retry-text data-v-5578dee7">重试</text></button></view><view wx:else class="levels-container data-v-5578dee7"><view class="levels-grid data-v-5578dee7"><view wx:for="{{i}}" wx:for-item="level" wx:key="k" class="{{['level-card', 'data-v-5578dee7', level.l && 'level-locked', level.m && 'level-completed']}}" bindtap="{{level.n}}"><view class="level-main-content data-v-5578dee7"><view class="level-number data-v-5578dee7">{{level.a}}</view><view class="level-info data-v-5578dee7"><text class="level-name data-v-5578dee7">{{level.b}}</text><text class="level-desc data-v-5578dee7">{{level.c}}</text></view><view class="level-status data-v-5578dee7"><view wx:if="{{level.d}}" class="status-badge locked data-v-5578dee7"><text class="status-text data-v-5578dee7">🔒 未解锁</text></view><view wx:elif="{{level.e}}" class="status-badge completed data-v-5578dee7"><view class="level-stars data-v-5578dee7"><text wx:for="{{level.f}}" wx:for-item="star" wx:key="a" class="star star-filled data-v-5578dee7"> ⭐ </text></view></view><view wx:else class="status-badge available data-v-5578dee7"><text class="status-text data-v-5578dee7">开始</text></view></view></view><view wx:if="{{level.g}}" class="level-tags data-v-5578dee7"><view wx:for="{{level.h}}" wx:for-item="tag" wx:key="c" class="{{['tag-item', 'data-v-5578dee7', tag.d && 'tag-vip']}}" style="{{'background-color:' + tag.e}}"><text class="tag-text data-v-5578dee7">{{tag.a}}</text><text wx:if="{{tag.b}}" class="tag-vip-icon data-v-5578dee7">👑</text></view><view wx:if="{{level.i}}" class="tag-more data-v-5578dee7"><text class="tag-more-text data-v-5578dee7">+{{level.j}}</text></view></view></view></view></view></view>