<template>
  <view class="tag-levels-container">
    <!-- 标签信息头部 -->
    <view class="tag-header">
      <view class="tag-info">
        <text class="tag-name">{{ tagInfo?.name || '标签关卡' }}</text>
        <text class="tag-desc">{{ tagInfo?.description || '挑战该标签下的所有关卡' }}</text>
      </view>
      <view class="tag-stats">
        <text class="stats-text">共{{ levels.length }}关</text>
        <text class="stats-text">已完成{{ completedCount }}关</text>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载关卡...</text>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="error" class="error-container">
      <view class="error-icon">⚠️</view>
      <text class="error-title">加载失败</text>
      <text class="error-text">{{ error }}</text>
      <button class="retry-btn" @click="loadTagLevels">
        <text class="retry-text">重试</text>
      </button>
    </view>

    <!-- 关卡列表 -->
    <view v-else class="levels-container">
      <view class="levels-grid">
        <view
          v-for="level in formattedLevels"
          :key="level.id"
          class="level-card"
          :class="{
            'level-locked': level.locked,
            'level-completed': level.completed
          }"
          @click="selectLevel(level)"
        >
          <!-- 关卡编号 -->
          <view class="level-number">{{ level.levelNumber }}</view>
          
          <!-- 关卡信息 -->
          <view class="level-info">
            <text class="level-name">{{ level.name }}</text>
            <text class="level-desc">{{ level.description }}</text>
          </view>

          <!-- 关卡状态和星级 -->
          <view class="level-status">
            <!-- 未解锁状态 -->
            <view v-if="level.locked" class="status-badge locked">
              <text class="status-text">🔒 未解锁</text>
            </view>
            <!-- 已完成状态 - 显示星级 -->
            <view v-else-if="level.completed" class="status-badge completed">
              <view class="level-stars">
                <text
                  v-for="star in (level.userStars || 0)"
                  :key="star"
                  class="star star-filled"
                >
                  ⭐
                </text>
              </view>
            </view>
            <!-- 可开始状态 -->
            <view v-else class="status-badge available">
              <text class="status-text">开始</text>
            </view>
          </view>

          <!-- 收藏按钮 -->
          <view class="favorite-btn" @click.stop="toggleFavorite(level)">
            <text class="favorite-icon" :class="{ 'favorited': level.isFavorited }">
              {{ level.isFavorited ? '💖' : '🤍' }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import weixinApi from '../../api/weixin'
import { audioManager } from '../../utils/audio'
import { checkLoginAndRedirect } from '../../utils/auth'
import type { ExtendedLevelInfo, LevelTag, TagLevelsResponse } from '../../api/types'

// 页面参数
const tagId = ref<string>('')

// 状态管理
const isLoading = ref(false)
const error = ref<string | null>(null)
const tagInfo = ref<LevelTag | null>(null)
const levels = ref<ExtendedLevelInfo[]>([])

// 计算属性
const completedCount = computed(() => {
  return levels.value.filter(level => level.isCompleted).length
})

// 格式化的关卡列表
const formattedLevels = computed(() => {
  return levels.value.map((level, index) => ({
    ...level,
    levelNumber: String(index + 1).padStart(2, '0'),
    locked: !level.isUnlocked,
    completed: level.isCompleted,
    isFavorited: level.isFavorited || false
  }))
})

/**
 * 页面加载
 */
onLoad(async (options) => {
  // 检查登录状态
  const isLoggedIn = await checkLoginAndRedirect({
    toastMessage: '请先登录以查看标签关卡',
    redirectUrl: '/pages/login/index'
  })

  if (!isLoggedIn) {
    return
  }

  // 获取标签ID
  if (options?.tagId) {
    tagId.value = options.tagId
    await loadTagLevels()
  } else {
    error.value = '缺少标签ID参数'
  }
})

/**
 * 加载标签关卡列表
 */
const loadTagLevels = async () => {
  if (!tagId.value) {
    error.value = '缺少标签ID参数'
    return
  }

  try {
    isLoading.value = true
    error.value = null

    // 先获取标签信息
    const tags = await weixinApi.getActiveTags()
    const currentTag = tags.find(tag => tag.id === tagId.value)

    if (currentTag) {
      tagInfo.value = currentTag
    }

    // 获取标签下的关卡列表
    const response = await weixinApi.getTagLevels(tagId.value)
    levels.value = response.levels

    console.log('标签关卡列表加载成功:', response)
  } catch (err) {
    console.error('加载标签关卡列表失败:', err)
    error.value = '加载失败，请重试'
  } finally {
    isLoading.value = false
  }
}

/**
 * 选择关卡
 */
const selectLevel = async (level: any) => {
  if (level.locked) {
    // 播放失败音效
    audioManager.playSoundEffect('fail')
    
    uni.showToast({
      title: '该关卡尚未解锁',
      icon: 'none',
      duration: 1500
    })
    return
  }

  // 播放点击音效
  audioManager.playSoundEffect('click')

  console.log('Selected level:', level)

  // 将选择的关卡信息存储到本地
  uni.setStorageSync('selectedLevel', JSON.stringify(level))

  // 跳转到游戏页面
  uni.navigateTo({
    url: '/pages/game/index'
  })
}

/**
 * 切换收藏状态
 */
const toggleFavorite = async (level: any) => {
  try {
    // 播放点击音效
    audioManager.playSoundEffect('click')

    if (level.isFavorited) {
      // 取消收藏
      await weixinApi.removeFavorite(level.id)
      level.isFavorited = false

      uni.showToast({
        title: '已取消收藏',
        icon: 'success',
        duration: 1500
      })
    } else {
      // 添加收藏
      await weixinApi.addFavorite(level.id)
      level.isFavorited = true

      uni.showToast({
        title: '已添加收藏',
        icon: 'success',
        duration: 1500
      })
    }

    // 播放成功音效
    audioManager.playSoundEffect('complete')
  } catch (error) {
    console.error('收藏操作失败:', error)

    // 播放失败音效
    audioManager.playSoundEffect('fail')

    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
      duration: 1500
    })
  }
}

/**
 * 返回上一页
 */
const goBack = () => {
  // 播放点击音效
  audioManager.playSoundEffect('click')

  uni.navigateBack()
}
</script>

<style scoped>
.tag-levels-container {
  min-height: 100vh;
  background: #fdf9f9;
  padding: 32rpx 24rpx;
}

/* 标签信息头部 */
.tag-header {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(102, 126, 234, 0.15);
}

.tag-info {
  margin-bottom: 20rpx;
}

.tag-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #1a202c;
  margin-bottom: 8rpx;
  letter-spacing: 0.5rpx;
}

.tag-desc {
  display: block;
  font-size: 26rpx;
  color: #4a5568;
  line-height: 1.4;
  font-weight: 400;
}

.tag-stats {
  display: flex;
  gap: 24rpx;
}

.stats-text {
  font-size: 24rpx;
  color: #667eea;
  font-weight: 500;
}

/* 加载和错误状态 */
.loading-container, .error-container {
  text-align: center;
  padding: 80rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(17, 17, 17, 0.3);
  border-top: 4rpx solid #111;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text, .error-title, .error-text {
  color: #111;
  font-size: 28rpx;
  margin-bottom: 16rpx;
}

.error-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.retry-btn {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: #ffffff;
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  border: none;
  font-size: 26rpx;
  box-shadow: 0 8rpx 20rpx rgba(116, 185, 255, 0.3);
}

.retry-text {
  color: inherit;
}

/* 关卡列表样式 */
.levels-container {
  flex: 1;
}

.levels-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24rpx;
}

.level-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}

.level-card:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.level-locked {
  opacity: 0.6;
}

.level-number {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 28rpx;
  font-weight: bold;
  flex-shrink: 0;
}

.level-info {
  flex: 1;
}

.level-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 8rpx;
}

.level-desc {
  display: block;
  font-size: 24rpx;
  color: #718096;
  margin-bottom: 12rpx;
}

/* 关卡标签样式 */
.level-tags {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  background: #f0f0f0;
}

.tag-vip {
  background: linear-gradient(135deg, #ffd700, #ffb347);
}

.tag-text {
  font-size: 20rpx;
  color: #666;
}

.tag-vip .tag-text {
  color: #8b4513;
  font-weight: 500;
}

.tag-vip-icon {
  font-size: 16rpx;
}

/* 关卡状态样式 */
.level-status {
  flex-shrink: 0;
}

.status-badge {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  text-align: center;
}

.status-badge.locked {
  background: #f0f0f0;
}

.status-badge.completed {
  background: #d1f2eb;
}

.status-badge.available {
  background: #e3f2fd;
}

.status-text {
  font-size: 22rpx;
  font-weight: 500;
}

/* 星级显示样式 */
.level-stars {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.star {
  font-size: 20rpx;
  color: #ddd;
  transition: color 0.2s;
}

.star-filled {
  color: #ffd700;
}

/* 底部按钮 */
.bottom-actions {
  display: flex;
  justify-content: center;
  padding-top: 32rpx;
}

.back-btn {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: #ffffff;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
  box-shadow: 0 8rpx 20rpx rgba(116, 185, 255, 0.3);
}

.back-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.back-text {
  color: inherit;
}

/* 收藏按钮样式 */
.favorite-btn {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transition: all 0.2s;
}

.favorite-btn:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 1);
}

.favorite-icon {
  font-size: 24rpx;
  transition: all 0.2s;
}

.favorite-icon.favorited {
  animation: heartbeat 0.6s ease-in-out;
}

@keyframes heartbeat {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}
</style>
