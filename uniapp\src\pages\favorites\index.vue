<template>
  <view class="favorites-container">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">我的收藏</text>
      <text class="page-subtitle">收藏的关卡列表</text>
    </view>

    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载收藏...</text>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="error" class="error-container">
      <view class="error-icon">⚠️</view>
      <text class="error-title">加载失败</text>
      <text class="error-text">{{ error }}</text>
      <button class="retry-btn" @click="loadFavorites">
        <text class="retry-text">重试</text>
      </button>
    </view>

    <!-- 空状态 -->
    <view v-else-if="favorites.length === 0" class="empty-container">
      <view class="empty-title">还没有收藏的关卡</view>
      <view class="empty-text">在游戏中完成关卡后，可以收藏喜欢的关卡</view>
      <view class="empty-hint">收藏的关卡会显示在这里，方便你随时回顾挑战</view>

      <view class="empty-actions">
        <button class="browse-btn primary" @click="goToLevelSelection">
          <text class="browse-text">去选择关卡</text>
        </button>
        <button class="browse-btn secondary" @click="goToTagSelection">
          <text class="browse-text">标签挑战</text>
        </button>
      </view>
    </view>

    <!-- 收藏列表 -->
    <view v-else class="favorites-list">
      <view class="list-header">
        <text class="list-title">共{{ favorites.length }}个收藏</text>
      </view>
      
      <view class="levels-grid">
        <view
          v-for="(level, index) in favorites"
          :key="level.id"
          class="level-card"
          :class="{
            'level-locked': level.isUnlocked,
            'level-completed': level.isCompleted
          }"
          @click="selectLevel(level)"
        >
          <!-- 关卡编号 -->
          <view class="level-number">{{ index + 1 }}</view>
          
          <!-- 关卡信息 -->
          <view class="level-info">
            <text class="level-name">{{ level.name }}</text>
            <text class="level-desc">{{ level.description }}</text>
            
            <!-- 关卡标签 -->
            <view v-if="level.tagIds && level.tagIds.length > 0" class="level-tags">
              <view 
                v-for="tag in level.tagIds.slice(0, 2)" 
                :key="tag.id"
                class="tag-item"
                :class="{ 'tag-vip': tag.isVip }"
              >
                <text class="tag-text">{{ tag.name }}</text>
                <text v-if="tag.isVip" class="tag-vip-icon">👑</text>
              </view>
            </view>
          </view>
          
          <!-- 关卡状态和星级 -->
          <view class="level-status">
            <!-- 未解锁状态 -->
            <view v-if="level.isUnlocked" class="status-badge locked">
              <text class="status-text">🔒 未解锁</text>
            </view>
            <!-- 已完成状态 - 显示星级 -->
            <view v-else-if="level.isCompleted" class="status-badge completed">
              <view class="level-stars">
                <text
                  v-for="star in (level.userStars || 0)"
                  :key="star"
                  class="star star-filled"
                >
                  ⭐
                </text>
              </view>
            </view>
            <!-- 可开始状态 -->
            <view v-else class="status-badge available">
              <text class="status-text">开始</text>
            </view>
          </view>

          <!-- 取消收藏按钮 -->
          <view class="unfavorite-btn" @click.stop="unfavoriteLevel(level)">
            <text class="unfavorite-icon">💔</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部返回按钮 -->
    <view class="bottom-actions">
      <button class="back-btn" @click="goBack">
        <text class="back-text">返回</text>
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import weixinApi from '../../api/weixin'
import { audioManager } from '../../utils/audio'
import { checkLoginAndRedirect } from '../../utils/auth'
import type { ExtendedLevelInfo, UserFavoritesResponse, DailyStatusResponse } from '../../api/types'

// 状态管理
const isLoading = ref(false)
const error = ref<string | null>(null)
const favorites = ref<ExtendedLevelInfo[]>([])
const dailyStatus = ref<DailyStatusResponse | null>(null)

// 格式化的收藏列表
const formattedFavorites = computed(() => {
  return favorites.value.map((level, index) => ({
    ...level,
    levelNumber: String(index + 1).padStart(2, '0'),
    locked: !level.isUnlocked,
    completed: level.isCompleted
  }))
})

/**
 * 页面加载
 */
onLoad(async () => {
  // 检查登录状态
  const isLoggedIn = await checkLoginAndRedirect({
    toastMessage: '请先登录以查看收藏',
    redirectUrl: '/pages/login/index'
  })

  if (!isLoggedIn) {
    return
  }

  // 通过每日状态接口检查VIP权限
  try {
    console.log('🔍 检查VIP状态...')
    dailyStatus.value = await weixinApi.getDailyStatus()

    if (!dailyStatus.value?.isVip) {
      // 非VIP用户，显示提示并返回
      console.log('❌ 非VIP用户，无法访问收藏功能')
      uni.showModal({
        title: '收藏功能',
        content: '收藏为会员专享功能，请先开通会员',
        showCancel: false,
        confirmText: '返回',
        success: () => {
          uni.navigateBack()
        }
      })
      return
    }

    console.log('✅ VIP用户，可以访问收藏功能')
  } catch (error) {
    console.error('获取每日状态失败:', error)
    // 如果接口失败，也阻止访问收藏功能
    uni.showModal({
      title: '加载失败',
      content: '无法验证会员状态，请稍后重试',
      showCancel: false,
      confirmText: '返回',
      success: () => {
        uni.navigateBack()
      }
    })
    return
  }

  await loadFavorites()
})

/**
 * 加载收藏列表
 */
const loadFavorites = async () => {
  try {
    isLoading.value = true
    error.value = null
    console.log('🔄 开始加载收藏列表...')

    const response = await weixinApi.getUserFavorites()
    console.log('📦 收藏列表API响应:', response)

    // 处理响应数据
    if (response && response.favorites) {
      favorites.value = response.favorites
      console.log('✅ 收藏列表加载成功，数量:', favorites.value.length)
    }  else {
      // 如果响应格式不符合预期，设置为空数组
      favorites.value = []
      console.log('⚠️ 收藏列表响应格式异常，设置为空数组')
    }

    // 如果收藏为空，显示空状态页面
    if (favorites.value.length === 0) {
      console.log('📭 收藏列表为空，显示空状态页面')
    }
  } catch (err) {
    console.error('❌ 加载收藏列表失败:', err)

    // 检查是否是网络错误或接口不存在
    if (err && typeof err === 'object' && 'message' in err) {
      console.log('错误详情:', err.message)
    }

    // 设置错误状态
    error.value = '加载失败，请重试'
    // 确保在错误情况下也设置空数组
    favorites.value = []
  } finally {
    isLoading.value = false
    console.log('🏁 收藏列表加载完成，最终状态:', {
      loading: isLoading.value,
      error: error.value,
      favoritesCount: favorites.value.length
    })
  }
}

/**
 * 选择关卡
 */
const selectLevel = async (level: any) => {
  if (level.locked) {
    // 播放失败音效
    audioManager.playSoundEffect('fail')
    
    uni.showToast({
      title: '该关卡尚未解锁',
      icon: 'none',
      duration: 1500
    })
    return
  }

  // 播放点击音效
  audioManager.playSoundEffect('click')

  console.log('Selected level:', level)

  // 将选择的关卡信息存储到本地
  uni.setStorageSync('selectedLevel', JSON.stringify(level))

  // 跳转到游戏页面
  uni.navigateTo({
    url: '/pages/game/index'
  })
}

/**
 * 取消收藏关卡
 */
const unfavoriteLevel = async (level: any) => {
  try {
    // 播放点击音效
    audioManager.playSoundEffect('click')

    const result = await weixinApi.removeFavorite(level.id)
    
    if (result.success) {
      // 从列表中移除
      favorites.value = favorites.value.filter(fav => fav.id !== level.id)
      
      // 播放成功音效
      audioManager.playSoundEffect('complete')
      
      uni.showToast({
        title: '已取消收藏',
        icon: 'success',
        duration: 1500
      })
    } else {
      throw new Error(result.message || '取消收藏失败')
    }
  } catch (error) {
    console.error('取消收藏失败:', error)
    
    // 播放失败音效
    audioManager.playSoundEffect('fail')
    
    uni.showToast({
      title: '取消收藏失败',
      icon: 'none',
      duration: 1500
    })
  }
}

/**
 * 跳转到关卡选择页面
 */
const goToLevelSelection = () => {
  // 播放点击音效
  audioManager.playSoundEffect('click')

  uni.navigateTo({
    url: '/pages/level-selection/index'
  })
}

/**
 * 跳转到标签选择页面
 */
const goToTagSelection = () => {
  // 播放点击音效
  audioManager.playSoundEffect('click')

  uni.navigateTo({
    url: '/pages/tag-selection/index'
  })
}

/**
 * 返回上一页
 */
const goBack = () => {
  // 播放点击音效
  audioManager.playSoundEffect('click')
  
  uni.navigateBack()
}
</script>

<style scoped>
.favorites-container {
  min-height: 100vh;
  background: #fdf9f9;
  padding: 32rpx 24rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 48rpx;
  padding: 32rpx 24rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: #4a5568;
  font-weight: 500;
}

/* 加载和错误状态 */
.loading-container, .error-container, .empty-container {
  text-align: center;
  padding: 80rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  margin: 24rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text, .error-title, .error-text, .empty-title, .empty-text {
  color: #2d3748;
  font-size: 28rpx;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.error-icon, .empty-icon {
  font-size: 64rpx;
  margin-bottom: 24rpx;
}

.empty-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.empty-text {
  font-size: 26rpx;
  color: #4a5568;
  margin-bottom: 12rpx;
  line-height: 1.4;
  font-weight: 400;
}

.empty-hint {
  font-size: 24rpx;
  color: #718096;
  margin-bottom: 40rpx;
  line-height: 1.4;
  font-weight: 400;
}

.empty-actions {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  align-items: center;
}

.retry-btn, .browse-btn {
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  font-size: 26rpx;
  min-width: 200rpx;
  transition: all 0.2s;
}

.browse-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #ffffff;
  border-color: transparent;
}

.browse-btn.secondary {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: #ffffff;
  border-color: transparent;
}

.browse-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.retry-text, .browse-text {
  color: inherit;
}

/* 收藏列表 */
.favorites-list {
  flex: 1;
}

.list-header {
  margin-bottom: 24rpx;
}

.list-title {
  color: #4a5568;
  font-size: 26rpx;
  font-weight: 600;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
}

.levels-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24rpx;
}

.level-card {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 20rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(102, 126, 234, 0.15);
  transition: all 0.2s;
  position: relative;
}

.level-card:active {
  transform: scale(0.98);
  opacity: 0.9;
  border-color: rgba(102, 126, 234, 0.3);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.12);
}

.level-locked {
  opacity: 0.6;
}

.level-number {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 28rpx;
  font-weight: bold;
  flex-shrink: 0;
}

.level-info {
  flex: 1;
}

.level-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #1a202c;
  margin-bottom: 8rpx;
  letter-spacing: 0.5rpx;
}

.level-desc {
  display: block;
  font-size: 24rpx;
  color: #4a5568;
  margin-bottom: 12rpx;
  font-weight: 400;
}

/* 关卡标签样式 */
.level-tags {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  background: #f0f0f0;
}

.tag-vip {
  background: linear-gradient(135deg, #ffd700, #ffb347);
}

.tag-text {
  font-size: 20rpx;
  color: #666;
}

.tag-vip .tag-text {
  color: #8b4513;
  font-weight: 500;
}

.tag-vip-icon {
  font-size: 16rpx;
}

/* 关卡状态样式 */
.level-status {
  flex-shrink: 0;
}

.status-badge {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  text-align: center;
}

.status-badge.locked {
  background: #f0f0f0;
}

.status-badge.completed {
  background: #d1f2eb;
}

.status-badge.available {
  background: #e3f2fd;
}

.status-text {
  font-size: 22rpx;
  font-weight: 500;
}

/* 星级显示样式 */
.level-stars {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.star {
  font-size: 20rpx;
  color: #ddd;
  transition: color 0.2s;
}

.star-filled {
  color: #ffd700;
}

/* 取消收藏按钮 */
.unfavorite-btn {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transition: all 0.2s;
}

.unfavorite-btn:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 1);
}

.unfavorite-icon {
  font-size: 24rpx;
}

/* 底部按钮 */
.bottom-actions {
  display: flex;
  justify-content: center;
  padding-top: 32rpx;
}

.back-btn {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: #ffffff;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
  box-shadow: 0 8rpx 20rpx rgba(116, 185, 255, 0.3);
}

.back-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.back-text {
  color: inherit;
}
</style>
