<template>
  <view class="page-box">
    <view class="game-page-container">
      <!-- 关卡详情信息 -->
      <view v-if="currentLevelDetail" class="level-detail-info card">
        <view class="level-header">
          <text class="level-name">{{ currentLevelDetail.name }}</text>
          <!-- <text class="level-difficulty">难度: {{ currentLevelDetail.difficulty }}星</text> -->
        </view>
        <!-- <text class="level-description">{{ currentLevelDetail.description }}</text> -->
        <view class="level-stats">
          <text class="stats-item">词组数量: {{ currentLevelDetail.phrases?.length || 0 }}</text>
          <text class="stats-item" v-if="currentLevelDetail.isCompleted">已完成</text>
          <text class="stats-item" v-else>🎯 挑战中</text>
        </view>
      </view>

      <!-- 备用：词库信息显示 -->
      <view v-else-if="selectedLibraryInfo" class="selected-library-info card">
        <text class="library-name">{{ selectedLibraryInfo.name }}</text>
      </view>

      <!-- 游戏区域 -->
      <view v-if="currentLevel" class="game-area card">
        <view class="game-info-bar">
          <text>关卡: {{ currentLevel.name }}</text>
          <text>已配对: {{ matchedPairs }}/{{ totalPairs }}</text>
          <text class="game-timer" :class="{ 'time-warning': currentGameTime <= 10 }">
            ⏱️ {{ formatGameTime(currentGameTime) }}
          </text>
          <text v-if="isProgressSyncing" class="sync-status">同步中...</text>
          <!-- H5环境提示 -->
          <text v-if="useMockData" class="h5-mock-tip">H5演示</text>
          <!-- 调试按钮（仅开发环境显示） -->
          <view class="debug-btn" @click="debugMode = !debugMode" v-if="isDevelopment">
            <text class="debug-btn-text">{{ debugMode ? '关闭调试' : '调试模式' }}</text>
          </view>
          <!-- 网格调试按钮（仅开发环境显示） -->
          <view class="grid-debug-btn" @click="showGridLines = !showGridLines" v-if="debugMode && isDevelopment">
            <text class="grid-debug-btn-text">{{ showGridLines ? '隐藏网格' : '显示网格' }}</text>
          </view>
          <!-- 重玩按钮 -->
          <view
            class="replay-btn"
            :class="{ 'replay-btn-disabled': !isGameReady }"
            @click="isGameReady ? replayGame() : null"
          >
            <text class="replay-btn-text">{{ isReplaying ? '重玩中...' : '重玩' }}</text>
          </view>
        </view>

        <!-- 游戏初始化加载状态 -->
        <view v-if="isGameInitializing" class="loading-container">
          <view class="loading-spinner"></view>
          <text class="loading-text">正在初始化游戏...</text>
        </view>

        <!-- 位置计算加载状态 -->
        <view v-else-if="isCalculatingPositions" class="loading-container">
          <view class="loading-spinner"></view>
          <text class="loading-text">正在计算卡片位置...</text>
        </view>

        <!-- 准备渲染状态 -->
        <view v-else-if="isPreparingRender" class="loading-container">
          <view class="loading-spinner"></view>
          <text class="loading-text">准备渲染游戏...</text>
        </view>

        <!-- 加载游戏状态 -->
        <view v-else-if="isLoadingGame" class="game-loading-container">
          <view class="game-loading-content">
            <view class="game-loading-spinner">
              <view class="spinner-ring"></view>
              <view class="spinner-ring"></view>
              <view class="spinner-ring"></view>
            </view>
            <text class="game-loading-title">{{ isReplaying ? '重新开始游戏...' : '加载游戏中...' }}</text>
            <text class="game-loading-subtitle">{{ loadingMessage || (isReplaying ? '正在为您重新准备游戏' : '正在为您准备精彩的游戏体验') }}</text>
            <view class="loading-progress">
              <view class="progress-bar">
                <view class="progress-fill" :style="{ width: loadingProgress + '%' }"></view>
              </view>
              <text class="progress-text">{{ loadingProgress }}%</text>
            </view>
          </view>
        </view>

        <!-- 初始化失败状态 -->
        <view v-else-if="!isGameReady && !isGameInitializing && !isCalculatingPositions && !isPreparingRender && !isLoadingGame" class="error-container">
          <text class="error-text">游戏初始化失败</text>
          <view class="retry-btn" @click="initializeGame">
            <text class="retry-btn-text">重试</text>
          </view>
        </view>

        <!-- 游戏棋盘 - 只有在游戏准备就绪时才显示 -->
        <view
          v-else-if="isGameReady"
          class="game-board"
          :class="{ 'checking-match': isChecking }"
        >
          <!-- 网格线显示 -->
          <view v-if="showGridLines && currentGridSystem" class="grid-overlay">
            <view
              v-for="(grid, index) in getGridLines()"
              :key="`grid-${index}`"
              class="grid-line"
              :style="{
                position: 'absolute',
                left: grid.x + 'rpx',
                top: grid.y + 'rpx',
                width: grid.width + 'rpx',
                height: grid.height + 'rpx',
                border: '2rpx dashed rgba(255, 0, 0, 0.5)',
                backgroundColor: 'rgba(255, 0, 0, 0.1)',
                pointerEvents: 'none'
              }"
            >
              <text class="grid-label">{{ grid.label }}</text>
            </view>
          </view>

          <!-- 卡片渲染 -->
          <view
            v-for="(tile, index) in gameBoard"
            :key="tile.id"
            class="board-tile"
            :class="{
              selected: tile.selected,
              matched: tile.matched,
              'debug-mode': debugMode,
              'tile-english': tile.type === 'english',
              'tile-chinese': tile.type === 'chinese',
              'tile-short': tile.type === 'english' && tile.word && tile.word.english.length <= 4,
              'tile-medium': tile.type === 'english' && tile.word &&
                tile.word.english.length > 4 &&
                tile.word.english.length <= 7,
              'tile-long': tile.type === 'english' && tile.word && tile.word.english.length > 7,
            }"
            :style="{
              backgroundColor: tile.color,
              position: 'absolute',
              left: tile.position.x + 'rpx',
              top: tile.position.y + 'rpx',
              width: tile.cardSize.width + 'rpx',
              height: tile.cardSize.height + 'rpx',
            }"
            :data-position="debugMode ? `(${Math.round(tile.position.x)}, ${Math.round(tile.position.y)})` : ''"
            @click="handleTileClick(index)"
          >
            <!-- 英文卡片只显示英文 -->
            <text v-if="tile.type === 'english'" class="tile-word">{{
              tile.word ? tile.word.english : ""
            }}</text>
            <!-- 中文卡片只显示中文 -->
            <text v-if="tile.type === 'chinese'" class="tile-chinese-only">{{
              tile.word ? tile.word.chinese : ""
            }}</text>
          </view>
        </view>
      </view>

      <!-- 关卡完成弹窗 -->
      <view v-if="isGameEndModalVisible" class="modal-overlay">
        <view class="modal-content">
          <text class="modal-title">{{ gameResultText }}</text>

          <!-- 游戏完成信息 -->
          <view v-if="gameWon" class="game-completion-info">
            <!-- 星级显示 -->
            <view class="stars-display">
              <text class="stars-label">获得星级</text>
              <view class="stars-container">
                <text
                  v-for="star in 3"
                  :key="star"
                  class="star-icon"
                  :class="{ 'star-filled': star <= gameStars }"
                >
                  ⭐
                </text>
              </view>
            </view>

            <!-- 完成时间 -->
            <view class="completion-time">
              <text class="time-label">完成时间</text>
              <text class="time-value">{{ formatGameTime(finalGameTime) }}</text>
            </view>

            <!-- 收藏按钮 -->
            <view class="favorite-section">
              <button
                class="favorite-btn"
                :class="{ 'favorited': isCurrentLevelFavorited }"
                @click="toggleCurrentLevelFavorite"
              >
                <text class="favorite-icon">
                  {{ isCurrentLevelFavorited ? '💖' : '🤍' }}
                </text>
                <text class="favorite-text">
                  {{ isCurrentLevelFavorited ? '已收藏' : '收藏关卡' }}
                </text>
              </button>
            </view>
          </view>

          <view class="modal-buttons">
            <button
              @click="nextLevel"
              class="modal-button primary"
              v-if="gameWon && canGoToNextLevel"
            >
              {{ currentLevelDetail ? '选择关卡' : '下一关' }}
            </button>
            <button @click="replayGame" class="modal-button">再试一次</button>
            <button @click="goBackHome" class="modal-button">返回首页</button>

          </view>
        </view>
      </view>
    </view>

    <!-- 右上角设置按钮 -->
    <!-- <view class="floating-settings-btn" @click="showSettings">
      <text class="settings-icon">⚙️</text>
    </view> -->

    <!-- 设置弹窗 -->
    <SettingsModal
      :visible="showSettingsModal"
      @close="closeSettings"
      @settingsChange="handleSettingsChange"
    />

    <!-- 悬浮调试按钮（仅开发环境显示） -->
    <!-- #ifdef MP-WEIXIN -->
    <view v-if="isDevelopment" class="floating-debug-btn" @click="goToDebug">
      <text class="debug-icon">🔧</text>
    </view>
    <!-- #endif -->

    <!-- #ifdef H5 -->
    <view v-if="isDevelopment" class="floating-debug-btn" @click="goToDebug">
      <text class="debug-icon">🔧</text>
    </view>
    <!-- #endif -->
  </view>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from "vue"
  import { onLoad, onShow, onHide } from "@dcloudio/uni-app"
  import weixinApi from '../../api/weixin'
  import { showSuccess, showError, withLoading, createLoadingState } from '../../api/utils'
  import { shareUtils } from '../../utils/share'
  import { audioManager } from '../../utils/audio'
  import { shouldShowDebugButtons } from '../../utils/debug-control'
  import SettingsModal from '../../components/SettingsModal.vue'
  import { useGlobalConfig } from '../../composables/useGlobalConfig'
  import type { UserInfo, LevelDetail, PhraseInfo, GameSettings } from '../../api/types'
  // Mock数据导入
  import {
    isH5Environment,
    mockUser,
    mockLibraries,
    mockLevels,
    getMockWordsForLevel,
    mockApiResponse
  } from '../../utils/mockData'

  const selectedLibraryInfo = ref(null)
  const currentLevel = ref(null) // 当前关卡
  const currentLevelId = ref(1) // 当前关卡ID
  const currentLevelDetail = ref<LevelDetail | null>(null) // 当前关卡详情
  const userInfo = ref<UserInfo | null>(null) // 用户信息
  const isProgressSyncing = ref(false) // 进度同步状态
  const selectedLevelData = ref(null) // 从首页传递的关卡数据

  // 分享奖励执行状态
  let isHandlingGameShareReward = false

  const gameBoard = ref([]);
  const selectedTiles = ref([]); // 存储用户选择的卡片
  const matchedPairs = ref(0); // 已配对数量
  const totalPairs = ref(0); // 总配对数量
  const isChecking = ref(false); // 是否正在检查匹配
  const debugMode = ref(false); // 调试模式，显示卡片边界

  // 游戏计时相关
  const gameStartTime = ref<number | null>(null); // 游戏开始时间戳
  const gameEndTime = ref<number | null>(null); // 游戏结束时间戳
  const gameTimer = ref<number | null>(null); // 计时器ID
  const currentGameTime = ref(60); // 当前剩余时间（秒，倒计时）
  const finalGameTime = ref(0); // 最终游戏时间（秒）
  const gameStars = ref(0); // 获得的星级（1-3）
  const isTimeUp = ref(false); // 时间是否到期

  // 收藏相关状态
  const isCurrentLevelFavorited = ref<boolean>(false)

  let tileIdCounter = 0;

  // 生成随机淡色（纯色：淡粉、淡蓝、淡绿）
  const generateRandomLightColor = () => {
    const colors = [
      "#FFE1E6", // 淡粉色
      "#E1F0FF", // 淡蓝色
      "#E1FFE1", // 淡绿色
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  const isGameEndModalVisible = ref(false);
  const gameResultText = ref("");
  const gameWon = ref(false);

  // 渲染控制状态
  const isGameInitializing = ref(true); // 游戏初始化状态
  const isCalculatingPositions = ref(false); // 位置计算状态
  const isPreparingRender = ref(false); // 准备渲染状态
  const isLoadingGame = ref(false); // 加载游戏状态
  const isGameReady = ref(false); // 游戏准备就绪状态

  // 加载进度状态
  const loadingProgress = ref(0); // 加载进度 0-100
  const loadingMessage = ref(''); // 加载消息
  const isReplaying = ref(false); // 是否正在重玩

  // 网格调试状态
  const showGridLines = ref(false); // 是否显示网格线
  const currentGridSystem = ref(null); // 当前网格系统实例

  // 开发环境检测
  const isDevelopment = ref(false)

  // H5环境检测
  const isH5 = ref(isH5Environment())
  const useMockData = ref(isH5.value) // H5环境下使用mock数据

  // 测试Mock数据导入
  const testMockDataImport = () => {
    console.log('🧪 测试Mock数据导入:')
    console.log('  - mockLibraries:', mockLibraries)
    console.log('  - mockLevels:', mockLevels)
    console.log('  - mockUser:', mockUser)
    console.log('  - getMockWordsForLevel(1, 8):', getMockWordsForLevel(1, 8))
    console.log('  - isH5Environment():', isH5Environment())
  }

  // 测试网格系统
  const testGridSystem = () => {
    console.log('🧪 测试网格系统:')
    try {
      // 创建测试卡片数据
      let testCards = [
        { id: 1, type: 'english', word: { english: 'test1', chinese: '测试1' } },
        { id: 2, type: 'chinese', word: { english: 'test1', chinese: '测试1' } },
        { id: 3, type: 'english', word: { english: 'test2', chinese: '测试2' } },
        { id: 4, type: 'chinese', word: { english: 'test2', chinese: '测试2' } }
      ]

      const testWidth = 750
      const testHeight = 800

      console.log('  - 输入测试数据:', testCards)
      console.log('  - 容器尺寸:', { width: testWidth, height: testHeight })

      const result = generateGridBasedPositions(testCards, testWidth, testHeight)

      console.log('  - 输出结果:', result)
      console.log('  - 测试完成')

      // 测试常量赋值问题
      console.log('🧪 测试常量赋值修复:')
      testCards = [...result] // 这应该不会报错，因为testCards现在是let声明
      console.log('  - 常量赋值测试通过')

      return result
    } catch (error) {
      console.error('❌ 网格系统测试失败:', error)
      return null
    }
  }

  // 调试函数：检查游戏状态
  const debugGameState = () => {
    console.log('🔍 游戏状态调试信息:')
    console.log('  - isH5:', isH5.value)
    console.log('  - useMockData:', useMockData.value)
    console.log('  - isGameInitializing:', isGameInitializing.value)
    console.log('  - isCalculatingPositions:', isCalculatingPositions.value)
    console.log('  - isPreparingRender:', isPreparingRender.value)
    console.log('  - isLoadingGame:', isLoadingGame.value)
    console.log('  - isGameReady:', isGameReady.value)
    console.log('  - currentLevel:', currentLevel.value)
    console.log('  - currentLevelDetail:', currentLevelDetail.value)
    console.log('  - wordsForCurrentLevel.length:', wordsForCurrentLevel.value.length)
    console.log('  - wordsForCurrentLevel:', wordsForCurrentLevel.value)
    console.log('  - gameBoard.length:', gameBoard.value.length)
    console.log('  - selectedLevelData:', selectedLevelData.value)
    console.log('  - selectedLibraryInfo:', selectedLibraryInfo.value)
    console.log('  - userInfo:', userInfo.value)
  }

  // 设置相关
  const showSettingsModal = ref(false)
  const gameSettings = ref<GameSettings>({
    backgroundMusic: true,
    soundEffects: true,
    vibration: true
  })

  // 全局配置
  const {
    globalConfig,
    getBackgroundMusicUrl,
    initializeGlobalConfig
  } = useGlobalConfig()

  // 计算属性：是否可以进入下一关
  const canGoToNextLevel = computed(() => {
    // 如果使用的是扩展关卡信息（从关卡选择页面进入）
    if (currentLevelDetail.value) {
      // 总是显示"下一关"按钮，点击时会返回关卡选择页面
      return true;
    }

    // 如果使用的是词库系统（旧版本）
    if (!selectedLibraryInfo.value || !selectedLibraryInfo.value.words)
      return false;
    const maxlevel = Math.min(
      Math.floor(selectedLibraryInfo.value.words.length / 8),
      1000
    );
    return currentLevelId.value < maxlevel;
  });

  // 获取当前关卡的单词
  const wordsForCurrentLevel = computed(() => {
    console.log('🔍 计算当前关卡词汇...')
    console.log('  - currentLevelDetail.value:', currentLevelDetail.value)
    console.log('  - selectedLibraryInfo.value:', selectedLibraryInfo.value)
    console.log('  - currentLevelId.value:', currentLevelId.value)
    console.log('  - useMockData.value:', useMockData.value)

    // 优先使用关卡详情中的词组
    if (currentLevelDetail.value && currentLevelDetail.value.phrases) {
      console.log('📚 使用关卡详情中的词组数据:', currentLevelDetail.value.phrases.length, '个词汇')
      const words = currentLevelDetail.value.phrases.map(phrase => ({
        english: phrase.english || phrase.text,
        chinese: phrase.chinese || phrase.meaning,
        id: phrase.id,
        pronunciation: phrase.pronunciation,
        category: phrase.category
      }))
      console.log('✅ 关卡词汇处理完成:', words.length, '个词汇')
      return words
    }

    // 备用：使用词库中的单词
    if (selectedLibraryInfo.value && selectedLibraryInfo.value.words) {
      console.log('📖 使用词库中的词汇数据:', selectedLibraryInfo.value.words.length, '个词汇')
      // 每个关卡使用8个不同的单词，循环使用词库中的单词
      const words = selectedLibraryInfo.value.words;
      const startIndex = ((currentLevelId.value - 1) * 8) % words.length;
      const selectedWords = [];

      for (let i = 0; i < 8; i++) {
        const wordIndex = (startIndex + i) % words.length;
        selectedWords.push(words[wordIndex]);
      }

      console.log('✅ 词库词汇处理完成:', selectedWords.length, '个词汇')
      return selectedWords;
    }

    console.warn('⚠️ 没有可用的词汇数据')
    console.log('📊 当前状态:')
    console.log('  - currentLevelDetail.value:', currentLevelDetail.value)
    console.log('  - selectedLibraryInfo.value:', selectedLibraryInfo.value)
    return [];
  });

  /**
   * 格式化游戏时间显示
   */
  const formatGameTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  /**
   * 开始游戏计时（60秒倒计时）
   */
  const startGameTimer = () => {
    gameStartTime.value = Date.now()
    currentGameTime.value = 60
    isTimeUp.value = false

    // 清除之前的计时器
    if (gameTimer.value) {
      clearInterval(gameTimer.value)
    }

    // 开始新的倒计时器
    gameTimer.value = setInterval(() => {
      if (gameStartTime.value && currentGameTime.value > 0) {
        const elapsed = Math.floor((Date.now() - gameStartTime.value) / 1000)
        currentGameTime.value = Math.max(0, 60 - elapsed)

        // 检查时间是否到期
        if (currentGameTime.value <= 0) {
          handleTimeUp()
        }
      }
    }, 1000)
  }

  /**
   * 停止游戏计时
   */
  const stopGameTimer = () => {
    if (gameTimer.value) {
      clearInterval(gameTimer.value)
      gameTimer.value = null
    }

    if (gameStartTime.value) {
      gameEndTime.value = Date.now()
      finalGameTime.value = Math.floor((gameEndTime.value - gameStartTime.value) / 1000)
    }
  }

  /**
   * 处理时间到期
   */
  const handleTimeUp = () => {
    console.log("⏰ 时间到期！游戏结束")
    isTimeUp.value = true
    stopGameTimer()

    // 设置最终时间为60秒（满时间）
    finalGameTime.value = 60
    gameStars.value = 0 // 时间到期获得0星

    // 播放失败音效
    audioManager.playSoundEffect('fail')
    audioManager.vibrate('short')

    // 结束游戏（失败）
    setTimeout(() => {
      endGame(false)
    }, 1000)
  }

  /**
   * 计算星级评定
   */
  const calculateStars = (completionTime: number, levelDetail: any): number => {
    // 如果关卡有时间限制，使用时间限制来计算星级
    if (levelDetail?.timeLimit) {
      const timeLimit = levelDetail.timeLimit
      if (completionTime <= timeLimit * 0.5) {
        return 3 // 在一半时间内完成 = 3星
      } else if (completionTime <= timeLimit * 0.75) {
        return 2 // 在3/4时间内完成 = 2星
      } else if (completionTime <= timeLimit) {
        return 1 // 在时间限制内完成 = 1星
      } else {
        return 1 // 超时但完成 = 1星
      }
    }

    // 默认星级评定规则（基于完成时间）
    if (completionTime <= 30) {
      return 3 // 30秒内完成 = 3星
    } else if (completionTime <= 60) {
      return 2 // 1分钟内完成 = 2星
    } else {
      return 1 // 超过1分钟 = 1星
    }
  }

  onLoad(async () => {
    try {
      // 测试Mock数据导入
      testMockDataImport()

      // 测试网格系统
      testGridSystem()

      // H5环境初始化
      if (isH5.value) {
        console.log('🌐 检测到H5环境，启用Mock数据模式')

        // 在H5环境下模拟存储一些基础数据
        const mockLibraryData = {
          id: mockLibraries[0].id,
          name: mockLibraries[0].name,
          description: mockLibraries[0].description,
          words: getMockWordsForLevel(1, 16)
        }

        const mockLevelData = {
          id: mockLevels[0].id,
          name: mockLevels[0].name,
          libraryId: mockLibraries[0].id
        }

        console.log('📦 设置Mock存储数据:')
        console.log('  - mockLibraryData:', mockLibraryData)
        console.log('  - mockLevelData:', mockLevelData)

        uni.setStorageSync("selectedLibrary", JSON.stringify(mockLibraryData))
        uni.setStorageSync("selectedLevel", JSON.stringify(mockLevelData))

        console.log('✅ H5环境Mock数据初始化完成')

        // 验证存储数据
        const storedLibrary = uni.getStorageSync("selectedLibrary")
        const storedLevel = uni.getStorageSync("selectedLevel")
        console.log('🔍 验证存储数据:')
        console.log('  - storedLibrary:', storedLibrary)
        console.log('  - storedLevel:', storedLevel)
      }

      // 初始化全局配置
      await initializeGlobalConfig()

      await initializeGame()

      // 检测开发环境
      checkDevelopmentEnvironment()

      // 初始化音频设置
      initAudioSettings()
    } catch (error) {
      console.error('❌ onLoad初始化失败:', error)
      debugGameState()
    }
  })

  /**
   * 初始化音频设置
   */
  const initAudioSettings = () => {
    try {
      const settings = audioManager.getSettings()
      gameSettings.value = { ...settings }
      console.log('音频设置初始化完成:', settings)

      // 如果背景音乐开启，播放游戏页面背景音乐
      if (settings.backgroundMusic) {
        const musicUrl = getBackgroundMusicUrl('game')
        console.log('播放游戏页面背景音乐:', musicUrl)
        audioManager.playBackgroundMusic('game', musicUrl)
      }
    } catch (error) {
      console.error('初始化音频设置失败:', error)
    }
  }

  // 页面显示时
  onShow(() => {
    console.log('游戏页面显示')
    audioManager.onPageShow()

    // 播放游戏背景音乐
    const settings = audioManager.getSettings()
    if (settings.backgroundMusic) {
      const musicUrl = getBackgroundMusicUrl('game')
      audioManager.playBackgroundMusic('game', musicUrl)
    }
  })

  // 页面隐藏时
  onHide(() => {
    console.log('游戏页面隐藏')
    audioManager.onPageHide()
  })

  /**
   * 游戏初始化 - 优化渲染逻辑
   */
  const initializeGame = async () => {
    try {
      console.log('🎮 开始游戏初始化...')
      isGameInitializing.value = true
      isCalculatingPositions.value = false
      isPreparingRender.value = false
      isLoadingGame.value = false
      isGameReady.value = false

      // 1. 加载用户信息
      console.log('📱 加载用户信息...')
      await loadUserInfo()

      // 2. 获取选择的关卡信息
      console.log('🎯 加载关卡信息...')
      const levelData = uni.getStorageSync("selectedLevel")

      let gameDataLoaded = false;

      if (levelData) {
        try {
          selectedLevelData.value = JSON.parse(levelData)
          console.log('获取到选择的关卡数据:', selectedLevelData.value)

          // 3. 尝试加载关卡详情
          await loadLevelDetail(selectedLevelData.value.id)

          // 4. 记录游戏开始
          if (userInfo.value) {
            await recordGameStart(selectedLevelData.value.id)
          }

          gameDataLoaded = true;

        } catch (e) {
          console.error("Failed to parse selected level data:", e)
          // 如果关卡详情加载失败，尝试使用词库数据作为备用
          console.log('🔄 尝试使用词库数据作为备用...')
          await loadLibraryDataAsFallback()
          gameDataLoaded = true;
        }
      } else {
        // 如果没有关卡数据，尝试使用词库数据作为备用
        console.log('🔄 没有关卡数据，使用词库数据作为备用...')
        await loadLibraryDataAsFallback()
        gameDataLoaded = true;
      }

      // 检查是否有可用的游戏数据
      console.log('🔍 检查游戏数据完整性...')
      console.log('  - gameDataLoaded:', gameDataLoaded)
      console.log('  - wordsForCurrentLevel.length:', wordsForCurrentLevel.value.length)
      console.log('  - currentLevel:', currentLevel.value)
      console.log('  - currentLevelDetail:', currentLevelDetail.value)

      if (!gameDataLoaded || wordsForCurrentLevel.value.length < 8) {
        console.error('❌ 游戏数据不足:')
        console.error('  - gameDataLoaded:', gameDataLoaded)
        console.error('  - wordsForCurrentLevel.length:', wordsForCurrentLevel.value.length)
        console.error('  - 需要至少8个单词')
        debugGameState()
        throw new Error(`无法获取足够的游戏数据: 需要8个单词，当前只有${wordsForCurrentLevel.value.length}个`)
      }

      // 5. 开始计算卡片位置
      console.log('🧮 开始计算卡片位置...')
      isGameInitializing.value = false
      isCalculatingPositions.value = true

      // 使用 setTimeout 确保 UI 更新
      await new Promise(resolve => setTimeout(resolve, 100))

      // 6. 初始化游戏棋盘（计算所有卡片位置）
      console.log('🎲 初始化游戏棋盘...')
      await initializeGameBoardAsync()

      // 7. 计算完成，准备渲染
      console.log('✅ 位置计算完成，准备渲染...')
      isCalculatingPositions.value = false
      isPreparingRender.value = true

      // 使用 setTimeout 确保UI更新，显示准备渲染状态
      await new Promise(resolve => setTimeout(resolve, 100))

      // 8. 最终渲染准备
      console.log('🎨 开始最终渲染准备...')

      // 检查渲染准备状态
      if (!checkRenderReadiness()) {
        throw new Error('游戏数据未正确生成或不完整')
      }

      // 9. 游戏加载阶段
      console.log('🎮 开始加载游戏...')
      isPreparingRender.value = false
      isLoadingGame.value = true

      // 重置加载进度
      loadingProgress.value = 0
      loadingMessage.value = '正在启动游戏...'

      // 显示游戏加载界面，模拟真实的加载过程
      await simulateGameLoading(2000) // 2秒的游戏加载体验

      // 10. 最终渲染
      console.log('🎉 游戏加载完成，开始渲染')
      isLoadingGame.value = false
      isGameReady.value = true

      // 11. 开始游戏计时
      console.log('⏱️ 开始游戏计时...')
      startGameTimer()

      // 12. 检查当前关卡的收藏状态
      console.log('💖 检查关卡收藏状态...')
      await checkCurrentLevelFavoriteStatus()

      console.log('✨ 游戏初始化完成，欢迎开始游戏！')

      // 输出最终状态调试信息
      debugGameState()

    } catch (error) {
      console.error('❌ 游戏初始化失败:', error)
      isGameInitializing.value = false
      isCalculatingPositions.value = false
      isPreparingRender.value = false
      isLoadingGame.value = false
      isGameReady.value = false

      // 输出错误状态调试信息
      debugGameState()

      showError(`游戏初始化失败: ${error.message || '未知错误'}`)
    }
  }

  /**
   * 加载关卡详情
   */
  const loadLevelDetail = async (levelId: string) => {
    try {
      console.log('正在加载关卡详情:', levelId)

      if (useMockData.value) {
        // H5环境使用mock数据
        console.log('🔧 H5环境：使用mock关卡数据')
        const mockLevel = mockLevels.find(level => level.id.toString() === levelId) || mockLevels[0]
        const mockWords = getMockWordsForLevel(parseInt(levelId), 8)

        const mockLevelDetail = {
          id: mockLevel.id,
          name: mockLevel.name,
          description: mockLevel.description,
          difficulty: mockLevel.difficulty,
          wordsCount: mockLevel.wordsCount,
          phrases: mockWords.map(word => ({
            id: word.id,
            english: word.english,
            chinese: word.chinese,
            pronunciation: word.pronunciation,
            category: word.category
          }))
        }

        const response = await mockApiResponse(mockLevelDetail, 400)
        currentLevelDetail.value = response.data

        // 设置当前关卡信息
        currentLevel.value = {
          id: response.data.id,
          name: response.data.name,
          wordsCount: response.data.phrases?.length || 8,
        }

        console.log('Mock关卡详情加载成功:', response.data)
      } else {
        // 真实环境调用API
        const levelDetail = await weixinApi.getLevelDetail(levelId)
        currentLevelDetail.value = levelDetail

        // 设置当前关卡信息
        currentLevel.value = {
          id: levelDetail.id,
          name: levelDetail.name,
          wordsCount: levelDetail.phrases?.length || 8,
        }

        console.log('关卡详情加载成功:', levelDetail)
      }
    } catch (error) {
      console.error('加载关卡详情失败:', error)
      throw error
    }
  }

  /**
   * 记录游戏开始
   */
  const recordGameStart = async (levelId: string) => {
    try {
      if (userInfo.value && userInfo.value.id) {
        if (useMockData.value) {
          // H5环境模拟记录游戏开始
          console.log('🔧 H5环境：模拟记录游戏开始')
          const mockResponse = await mockApiResponse({
            success: true,
            gameId: Date.now(),
            startTime: new Date().toISOString()
          }, 200)
          console.log('Mock游戏开始记录成功:', mockResponse.data)
        } else {
          // 真实环境调用API
          await weixinApi.startGame(userInfo.value.id, levelId)
          console.log('游戏开始记录成功')
        }
      }
    } catch (error) {
      console.error('记录游戏开始失败:', error)
      // 不阻断游戏流程
    }
  }

  /**
   * 加载词库数据作为备用
   */
  const loadLibraryDataAsFallback = async () => {
    console.log('🔄 开始加载词库数据作为备用...')

    if (useMockData.value) {
      // H5环境使用mock数据
      console.log('🔧 H5环境：使用mock词库数据')

      // 模拟从存储中获取选择的词库
      const mockLibrary = mockLibraries[0] // 使用第一个词库
      const mockLevel = mockLevels[0] // 使用第一个关卡

      selectedLibraryInfo.value = {
        id: mockLibrary.id,
        name: mockLibrary.name,
        description: mockLibrary.description,
        words: getMockWordsForLevel(1, 16) // 获取足够的单词
      }

      currentLevelId.value = mockLevel.id
      currentLevel.value = {
        id: mockLevel.id,
        name: mockLevel.name,
        wordsCount: 8,
      }

      console.log('✅ 成功使用mock词库数据:', selectedLibraryInfo.value)
      console.log('📚 当前关卡词汇数量:', wordsForCurrentLevel.value.length)

    } else {
      // 真实环境从存储获取数据
      const libraryData = uni.getStorageSync("selectedLibrary")
      if (libraryData) {
        try {
          selectedLibraryInfo.value = JSON.parse(libraryData)

          // 获取当前关卡进度
          const savedLevelId = getCurrentLevelId(selectedLibraryInfo.value.id)
          currentLevelId.value = savedLevelId

          // 设置当前关卡信息
          currentLevel.value = {
            id: currentLevelId.value,
            name: `第${currentLevelId.value}关`,
            wordsCount: 8,
          }

          console.log('✅ 成功使用词库数据作为备用:', selectedLibraryInfo.value)
          console.log('📚 当前关卡词汇数量:', wordsForCurrentLevel.value.length)

          // 检查词汇数量是否足够
          if (wordsForCurrentLevel.value.length < 8) {
            throw new Error(`词库数据不足: 需要8个词汇，当前只有${wordsForCurrentLevel.value.length}个`)
          }

        } catch (e) {
          console.error("❌ 加载词库数据失败:", e)
          throw new Error(`加载词库数据失败: ${e.message || '未知错误'}`)
        }
      } else {
        console.error("❌ 没有找到词库数据")
        throw new Error("未选择关卡或词库")
      }
    }
  }

  /**
   * 加载用户信息
   */
  const loadUserInfo = async () => {
    try {
      if (useMockData.value) {
        // H5环境使用mock数据
        console.log('🔧 H5环境：使用mock用户数据')
        const response = await mockApiResponse(mockUser, 300)
        userInfo.value = response.data
        console.log('Mock用户信息加载成功:', userInfo.value)
      } else {
        // 真实环境获取本地用户信息
        const localUserInfo = weixinApi.getLocalUserInfo()
        if (localUserInfo) {
          userInfo.value = localUserInfo
          console.log('游戏页面获取到用户信息:', localUserInfo)
        }
      }
    } catch (error) {
      console.error('加载用户信息失败:', error)

      // 如果真实环境失败，降级使用mock数据
      if (!useMockData.value) {
        console.log('🔄 用户信息获取失败，降级使用mock数据')
        const response = await mockApiResponse(mockUser, 100)
        userInfo.value = response.data
      }
    }
  }

  // 获取当前关卡进度
  const getCurrentLevelId = (libraryId: number): number => {
    const key = `currentLevel_${libraryId}`
    const saved = uni.getStorageSync(key)
    return saved ? parseInt(saved) : 1 // 默认第一关
  }

  // 保存当前关卡进度
  const saveCurrentLevelId = (libraryId: number, levelId: number): void => {
    const key = `currentLevel_${libraryId}`
    uni.setStorageSync(key, levelId.toString())
  }

  // 获取卡片尺寸的函数（固定尺寸）
  const getCardSize = (wordLength: number): { width: number; height: number } => {
    // 所有卡片使用固定尺寸：宽度160rpx，高度35rpx
    return { width: 160, height: 35 }
  }

  // 计算两张卡片之间的固定间距（所有卡片尺寸相同）
  const calculateCardSpacing = (card1Size, card2Size) => {
    // 固定卡片尺寸：160rpx × 35rpx
    const cardWidth = 160;
    const cardHeight = 35;

    // 根据固定尺寸计算间距
    const baseSpacing = Math.floor((cardWidth + cardHeight) / 2); // 97.5rpx
    const minSpacing = 80; // 最小间距80rpx
    const extraPadding = 15; // 额外安全间距
    const spacing = Math.max(baseSpacing, minSpacing) + extraPadding; // 112rpx

    console.log(`固定间距计算: 卡片尺寸(${cardWidth}×${cardHeight}) = ${spacing}rpx`);
    return spacing;
  }

  // 获取标准卡片间距（用于单个卡片的情况）
  const getStandardCardSpacing = () => {
    // 使用固定卡片尺寸计算间距
    const standardSize = { width: 160, height: 35 }; // 固定尺寸卡片
    const baseSpacing = Math.floor((standardSize.width + standardSize.height) / 2); // 97.5rpx
    const minSpacing = 80; // 最小间距80rpx
    const extraPadding = 15; // 额外安全间距
    const spacing = Math.max(baseSpacing, minSpacing) + extraPadding; // 112rpx

    console.log(`标准间距: ${spacing}rpx`);
    return spacing;
  }

  // 获取紧凑布局间距
  const getCompactSpacing = () => {
    const standardSpacing = getStandardCardSpacing();
    const compactSpacing = Math.floor(standardSpacing * 0.6); // 标准间距的60%
    const minCompactSpacing = 50; // 紧凑布局最小间距50rpx
    const spacing = Math.max(compactSpacing, minCompactSpacing); // 67rpx

    console.log(`紧凑间距: ${spacing}rpx`);
    return spacing;
  }

  // 增强的矩形碰撞检测算法 - 更严格的碰撞检测
  const isRectangleColliding = (rect1, rect2, padding = 0) => {
    // 增加额外的安全边距
    const extraSafety = 10; // 额外10rpx安全边距
    const totalPadding = padding + extraSafety;

    // 扩展矩形边界，加入安全间距
    const expandedRect1 = {
      left: rect1.x - totalPadding,
      right: rect1.x + rect1.width + totalPadding,
      top: rect1.y - totalPadding,
      bottom: rect1.y + rect1.height + totalPadding
    };

    const expandedRect2 = {
      left: rect2.x - totalPadding,
      right: rect2.x + rect2.width + totalPadding,
      top: rect2.y - totalPadding,
      bottom: rect2.y + rect2.height + totalPadding
    };

    // 严格的AABB碰撞检测：如果两个矩形在任一轴上分离，则不碰撞
    const isColliding = !(
      expandedRect1.right <= expandedRect2.left ||   // rect1在rect2左侧
      expandedRect1.left >= expandedRect2.right ||   // rect1在rect2右侧
      expandedRect1.bottom <= expandedRect2.top ||   // rect1在rect2上方
      expandedRect1.top >= expandedRect2.bottom      // rect1在rect2下方
    );

    // 额外的距离检测
    if (isColliding) {
      const centerX1 = rect1.x + rect1.width / 2;
      const centerY1 = rect1.y + rect1.height / 2;
      const centerX2 = rect2.x + rect2.width / 2;
      const centerY2 = rect2.y + rect2.height / 2;

      const distance = Math.sqrt(
        Math.pow(centerX1 - centerX2, 2) + Math.pow(centerY1 - centerY2, 2)
      );

      // 固定卡片尺寸的最小中心距离计算
      const cardWidth = 160;
      const cardHeight = 35;
      const minCenterDistance = (cardWidth + cardHeight) + totalPadding; // 195 + padding

      if (distance < minCenterDistance) {
        console.warn(`碰撞检测: 中心距离${Math.round(distance)}rpx < 最小距离${Math.round(minCenterDistance)}rpx`);
        return true;
      }
    }

    return isColliding;
  };

  // 检查两个矩形是否重叠（兼容原有接口）
  const isOverlapping = (rect1, rect2, safeDistance = null) => {
    // 如果没有提供间距，使用动态计算
    if (safeDistance === null) {
      safeDistance = calculateCardSpacing(
        { width: rect1.width, height: rect1.height },
        { width: rect2.width, height: rect2.height }
      );
    }

    return isRectangleColliding(rect1, rect2, safeDistance);
  };

  // 检查卡片是否超出边界
  const isOutOfBounds = (rect, containerWidth, containerHeight, margin = 25) => {
    return (
      rect.x < margin ||
      rect.y < margin ||
      rect.x + rect.width > containerWidth - margin ||
      rect.y + rect.height > containerHeight - margin
    );
  };

  // 获取游戏区域的实际尺寸
  const getGameBoardDimensions = () => {
    try {
      // 基于屏幕宽度动态计算游戏区域尺寸
      const systemInfo = uni.getSystemInfoSync();
      const screenWidth = systemInfo.screenWidth || 375; // 默认iPhone宽度
      const screenHeight = systemInfo.screenHeight || 667; // 默认iPhone高度

      // 基于固定卡片尺寸计算所需空间
      const cardWidth = 160; // 固定卡片宽度
      const cardHeight = 35; // 固定卡片高度
      const spacing = getStandardCardSpacing(); // 使用统一的标准间距
      const margin = 30; // 边距

      // 计算4列4行网格所需的最小尺寸
      const cols = 4;
      const rows = 4;
      const minRequiredWidth = cols * cardWidth + (cols - 1) * spacing + margin * 2;
      const minRequiredHeight = rows * cardHeight + (rows - 1) * spacing + margin * 2;

      // CSS限制
      const cssMaxWidth = 750; // 对应CSS中的max-width: 750rpx
      const cssPadding = 25 * 4; // 对应CSS中的padding: 25rpx
      const actualGameAreaWidth = cssMaxWidth - cssPadding; // 实际可用宽度 = 700rpx

      // 确保容器宽度不超过CSS限制，但满足最小要求
      let containerWidth = Math.min(actualGameAreaWidth, minRequiredWidth);

      // 如果CSS限制太小，调整为紧凑布局
      if (containerWidth < minRequiredWidth) {
        console.warn('CSS宽度限制过小，使用紧凑布局');
        const compactSpacing = getCompactSpacing(); // 使用统一的紧凑间距
        containerWidth = Math.min(actualGameAreaWidth,
          cols * cardWidth + (cols - 1) * compactSpacing + margin * 2);
      }

      // 游戏区域高度：确保能容纳4行卡片
      const maxHeight = Math.floor(screenHeight * 0.7 * (750 / screenWidth));
      let containerHeight = Math.max(minRequiredHeight, Math.floor(containerWidth * 1.2));
      containerHeight = Math.min(containerHeight, maxHeight);

      console.log(`� 随机定位参数:`);
      console.log(`   - 卡片尺寸: ${cardWidth}×${cardHeight}rpx`);
      console.log(`   - 总卡片数: 16张`);
      console.log(`   - 标准间距: ${spacing}rpx, 边距: ${margin}rpx`);
      console.log(`   - 最小要求: ${minRequiredWidth}×${minRequiredHeight}rpx`);
      console.log(`   - CSS限制: ${actualGameAreaWidth}rpx`);
      console.log(`   - 实际尺寸: ${containerWidth}×${containerHeight}rpx`);

      return {
        containerWidth: containerWidth,
        containerHeight: containerHeight
      };
    } catch (error) {
      console.warn('获取屏幕信息失败，使用默认尺寸:', error);
      return { containerWidth: 700, containerHeight: 900 }; // 默认尺寸不超过CSS限制(750-50=700)
    }
  };

  // 优化的随机位置生成策略 - 真正的随机分布且避免交叉
  const generateRandomPosition = (
    cardWidth,
    cardHeight,
    existingPositions,
    containerWidth,
    containerHeight
  ) => {
    const margin = 40; // 增加边距到40rpx
    const maxAttempts = 2000; // 大幅增加尝试次数

    // 计算可用区域
    const availableWidth = containerWidth - margin * 2 - cardWidth;
    const availableHeight = containerHeight - margin * 2 - cardHeight;

    // 确保有足够空间
    if (availableWidth <= 0 || availableHeight <= 0) {
      console.warn('容器空间不足，使用紧凑布局');
      return generateCompactPosition(cardWidth, cardHeight, existingPositions.length, containerWidth, containerHeight);
    }

    // 多策略随机生成算法 - 调整权重，更倾向于完全随机
    const strategies = [
      // 策略1: 完全随机分布 (80%的尝试)
      {
        name: '完全随机',
        weight: 0.8,
        generate: () => ({
          x: margin + Math.random() * availableWidth,
          y: margin + Math.random() * availableHeight
        })
      },
      // 策略2: 网格随机分布 (15%的尝试)
      {
        name: '网格随机',
        weight: 0.15,
        generate: () => {
          const gridCols = Math.max(3, Math.floor(containerWidth / 150));
          const gridRows = Math.max(3, Math.floor(containerHeight / 150));
          const col = Math.floor(Math.random() * gridCols);
          const row = Math.floor(Math.random() * gridRows);
          const cellWidth = containerWidth / gridCols;
          const cellHeight = containerHeight / gridRows;

          return {
            x: col * cellWidth + Math.random() * Math.max(0, cellWidth - cardWidth - margin),
            y: row * cellHeight + Math.random() * Math.max(0, cellHeight - cardHeight - margin)
          };
        }
      },
      // 策略3: 边缘优先分布 (4%的尝试)
      {
        name: '边缘优先',
        weight: 0.04,
        generate: () => {
          const side = Math.floor(Math.random() * 4); // 0:上, 1:右, 2:下, 3:左
          const edgeOffset = 50; // 距离边缘的偏移量

          switch (side) {
            case 0: // 上边区域
              return {
                x: margin + Math.random() * availableWidth,
                y: margin + Math.random() * Math.min(edgeOffset, availableHeight)
              };
            case 1: // 右边区域
              return {
                x: Math.max(margin, containerWidth - margin - cardWidth - edgeOffset) + Math.random() * edgeOffset,
                y: margin + Math.random() * availableHeight
              };
            case 2: // 下边区域
              return {
                x: margin + Math.random() * availableWidth,
                y: Math.max(margin, containerHeight - margin - cardHeight - edgeOffset) + Math.random() * edgeOffset
              };
            case 3: // 左边区域
              return {
                x: margin + Math.random() * Math.min(edgeOffset, availableWidth),
                y: margin + Math.random() * availableHeight
              };
            default:
              return { x: margin + Math.random() * availableWidth, y: margin + Math.random() * availableHeight };
          }
        }
      },
      // 策略4: 避让已有卡片的智能分布 (1%的尝试)
      {
        name: '智能避让',
        weight: 0.01,
        generate: () => {
          if (existingPositions.length === 0) {
            return { x: margin + Math.random() * availableWidth, y: margin + Math.random() * availableHeight };
          }

          // 找到已有卡片的重心
          let centerX = 0, centerY = 0;
          existingPositions.forEach(pos => {
            centerX += pos.x + pos.width / 2;
            centerY += pos.y + pos.height / 2;
          });
          centerX /= existingPositions.length;
          centerY /= existingPositions.length;

          // 在重心的对角方向生成位置
          const oppositeX = containerWidth - centerX;
          const oppositeY = containerHeight - centerY;

          return {
            x: Math.max(margin, Math.min(oppositeX - cardWidth/2 + (Math.random() - 0.5) * 100, containerWidth - margin - cardWidth)),
            y: Math.max(margin, Math.min(oppositeY - cardHeight/2 + (Math.random() - 0.5) * 100, containerHeight - margin - cardHeight))
          };
        }
      }
    ];

    // 根据权重选择策略
    const selectStrategy = () => {
      const random = Math.random();
      let cumulative = 0;
      for (const strategy of strategies) {
        cumulative += strategy.weight;
        if (random <= cumulative) {
          return strategy;
        }
      }
      return strategies[0]; // 默认返回第一个策略
    };

    // 尝试生成不重叠的位置
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      const strategy = selectStrategy();
      const position = strategy.generate();

      const x = Math.max(margin, Math.min(position.x, containerWidth - margin - cardWidth));
      const y = Math.max(margin, Math.min(position.y, containerHeight - margin - cardHeight));

      const newRect = { x, y, width: cardWidth, height: cardHeight };

      // 检查是否与现有卡片重叠
      let hasCollision = false;
      let minDistanceToExisting = Infinity;

      for (const existingPos of existingPositions) {
        const spacing = calculateCardSpacing(
          { width: cardWidth, height: cardHeight },
          existingPos
        );

        // 计算到现有卡片的距离
        const centerX1 = x + cardWidth / 2;
        const centerY1 = y + cardHeight / 2;
        const centerX2 = existingPos.x + existingPos.width / 2;
        const centerY2 = existingPos.y + existingPos.height / 2;
        const distance = Math.sqrt(
          Math.pow(centerX1 - centerX2, 2) + Math.pow(centerY1 - centerY2, 2)
        );

        minDistanceToExisting = Math.min(minDistanceToExisting, distance);

        if (isRectangleColliding(newRect, existingPos, spacing)) {
          hasCollision = true;
          break;
        }
      }

      // 检查是否超出边界
      if (!hasCollision && !isOutOfBounds(newRect, containerWidth, containerHeight, margin)) {
        // 额外检查：确保与最近卡片的距离足够
        const minRequiredDistance = 100; // 最小中心距离100rpx
        if (existingPositions.length === 0 || minDistanceToExisting >= minRequiredDistance) {
          console.log(`卡片${existingPositions.length + 1}: ${strategy.name}策略定位成功 -> 位置(${Math.round(x)}, ${Math.round(y)}) [尝试${attempt + 1}次] [最近距离${Math.round(minDistanceToExisting)}rpx]`);
          return { x: Math.round(x), y: Math.round(y) };
        } else {
          console.log(`卡片${existingPositions.length + 1}: 位置(${Math.round(x)}, ${Math.round(y)})距离过近(${Math.round(minDistanceToExisting)}rpx < ${minRequiredDistance}rpx)，继续尝试...`);
        }
      }
    }

    // 如果所有尝试都失败，使用紧凑布局
    console.warn(`随机定位失败，使用紧凑布局放置第${existingPositions.length + 1}张卡片`);
    return generateCompactPosition(cardWidth, cardHeight, existingPositions.length, containerWidth, containerHeight);
  };

  // 紧凑布局后备方案
  const generateCompactPosition = (cardWidth, cardHeight, cardIndex, containerWidth, containerHeight) => {
    const margin = 25;
    const spacing = getCompactSpacing(); // 使用统一的紧凑间距

    console.log(`紧凑布局: 卡片${cardIndex + 1}, 尺寸${cardWidth}×${cardHeight}, 间距${spacing}rpx`);

    // 计算网格参数
    const availableWidth = containerWidth - margin * 2;
    const cellWidth = cardWidth + spacing;
    const cellHeight = cardHeight + spacing;

    const cols = Math.max(3, Math.floor(availableWidth / cellWidth));
    const col = cardIndex % cols;
    const row = Math.floor(cardIndex / cols);

    let x = margin + col * cellWidth;
    let y = margin + row * cellHeight;

    // 确保不超出边界
    x = Math.max(margin, Math.min(x, containerWidth - cardWidth - margin));
    y = Math.max(margin, Math.min(y, containerHeight - cardHeight - margin));

    console.log(`卡片${cardIndex + 1}: 紧凑布局 -> 位置(${Math.round(x)}, ${Math.round(y)})`);

    return { x: Math.round(x), y: Math.round(y) };
  };

  /**
   * 为已有的gameBoard数据添加随机坐标
   * @param {Array} gameBoardData - 已有的游戏板数据
   * @param {number} containerWidth - 容器宽度
   * @param {number} containerHeight - 容器高度
   * @returns {Array} 添加了随机坐标的游戏板数据
   */
  const addRandomPositionsToGameBoard = (gameBoardData, containerWidth, containerHeight) => {
    console.log('🎯 为gameBoard数据添加随机坐标...')

    const cardWidth = 160; // 固定卡片宽度
    const cardHeight = 35; // 固定卡片高度
    const existingPositions = [];

    // 为每张卡片生成随机位置
    const updatedGameBoard = gameBoardData.map((card, index) => {
      console.log(`🔄 为第${index + 1}张卡片生成位置: ${card.type} - ${card.word ? card.word[card.type] : 'unknown'}`)

      // 生成随机位置
      const position = generateRandomPosition(
        cardWidth,
        cardHeight,
        existingPositions,
        containerWidth,
        containerHeight
      );

      // 记录位置
      existingPositions.push({
        x: position.x,
        y: position.y,
        width: cardWidth,
        height: cardHeight,
      });

      // 返回更新后的卡片数据
      return {
        ...card,
        position: position,
        cardSize: { width: cardWidth, height: cardHeight }
      };
    });

    console.log('✅ 随机坐标添加完成')
    return updatedGameBoard;
  };

  /**
   * 批量处理游戏数据，确保所有计算在渲染前完成
   * @param {Array} cards - 卡片数据
   * @param {number} containerWidth - 容器宽度
   * @param {number} containerHeight - 容器高度
   * @returns {Promise<Array>} 处理完成的卡片数据
   */
  const batchProcessGameData = async (cards, containerWidth, containerHeight) => {
    console.log('🔄 开始批量处理游戏数据...')

    // 1. 验证卡片布局质量
    console.log('📊 验证卡片布局质量...')
    validateCardLayout(cards, containerWidth, containerHeight);

    // 2. 打乱卡片顺序（保持位置不变）
    console.log('🔀 打乱卡片内容分配...')
    const englishCards = cards.filter(card => card.type === 'english');
    const chineseCards = cards.filter(card => card.type === 'chinese');

    // 打乱英文卡片的内容分配
    for (let i = englishCards.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      const tempWord = englishCards[i].word;
      const tempColor = englishCards[i].color;
      const tempPairId = englishCards[i].pairId;

      englishCards[i].word = englishCards[j].word;
      englishCards[i].color = englishCards[j].color;
      englishCards[i].pairId = englishCards[j].pairId;

      englishCards[j].word = tempWord;
      englishCards[j].color = tempColor;
      englishCards[j].pairId = tempPairId;
    }

    // 打乱中文卡片的内容分配
    for (let i = chineseCards.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      const tempWord = chineseCards[i].word;
      const tempColor = chineseCards[i].color;
      const tempPairId = chineseCards[i].pairId;

      chineseCards[i].word = chineseCards[j].word;
      chineseCards[i].color = chineseCards[j].color;
      chineseCards[i].pairId = chineseCards[j].pairId;

      chineseCards[j].word = tempWord;
      chineseCards[j].color = tempColor;
      chineseCards[j].pairId = tempPairId;
    }

    // 3. 让出控制权，避免阻塞UI
    await new Promise(resolve => setTimeout(resolve, 10));

    console.log('✅ 游戏数据批量处理完成')
    console.log(`📊 英文卡片 (${englishCards.length}张), 中文卡片 (${chineseCards.length}张)`)

    return cards;
  };

  /**
   * 网格系统 - 将画布分割为相同大小的矩形
   */
  class GridSystem {
    constructor(containerWidth, containerHeight, cardCount) {
      this.containerWidth = containerWidth;
      this.containerHeight = containerHeight;
      this.cardCount = cardCount;
      this.cardWidth = 160; // 固定卡片宽度
      this.cardHeight = 35; // 固定卡片高度
      this.padding = 20; // 网格内边距

      // 计算网格布局
      this.calculateGrid();

      // 初始化网格占用状态
      this.occupiedGrids = new Set();
    }

    /**
     * 计算最优网格布局
     */
    calculateGrid() {
      // 计算可用空间
      const availableWidth = this.containerWidth - this.padding * 2;
      const availableHeight = this.containerHeight - this.padding * 2;

      // 计算网格尺寸（确保卡片能完全放入）
      this.gridWidth = this.cardWidth + 40; // 卡片宽度 + 间距
      this.gridHeight = (this.cardHeight + 40) * 1.5; // 卡片高度 + 间距，然后乘以1.5

      // 计算网格行列数
      this.cols = Math.floor(availableWidth / this.gridWidth);
      this.rows = Math.floor(availableHeight / this.gridHeight);

      // 确保有足够的网格容纳所有卡片
      const totalGrids = this.cols * this.rows;
      if (totalGrids < this.cardCount) {
        // 如果网格不够，调整网格尺寸
        this.adjustGridSize();
      }

      console.log(`📐 网格系统: ${this.cols}列 × ${this.rows}行 = ${totalGrids}个网格，需要${this.cardCount}个`);
      console.log(`📏 网格尺寸: ${this.gridWidth} × ${this.gridHeight} (高度已调整为1.5倍)`);
    }

    /**
     * 调整网格尺寸以容纳所有卡片
     */
    adjustGridSize() {
      const availableWidth = this.containerWidth - this.padding * 2;
      const availableHeight = this.containerHeight - this.padding * 2;

      // 计算理想的网格数量（稍微多一些以提供选择空间）
      const idealGridCount = Math.ceil(this.cardCount * 1.5);

      // 尝试不同的行列组合
      let bestCols = 4;
      let bestRows = 4;
      let bestGridWidth = availableWidth / bestCols;
      let bestGridHeight = availableHeight / bestRows;

      for (let cols = 3; cols <= 8; cols++) {
        for (let rows = 3; rows <= 8; rows++) {
          if (cols * rows >= idealGridCount) {
            const gridWidth = availableWidth / cols;
            const gridHeight = availableHeight / rows;

            // 检查网格是否能容纳卡片（考虑1.5倍高度）
            if (gridWidth >= this.cardWidth + 20 && gridHeight >= (this.cardHeight + 20) * 1.5) {
              bestCols = cols;
              bestRows = rows;
              bestGridWidth = gridWidth;
              bestGridHeight = gridHeight;
              break;
            }
          }
        }
      }

      this.cols = bestCols;
      this.rows = bestRows;
      this.gridWidth = bestGridWidth;
      this.gridHeight = bestGridHeight;

      console.log(`🔧 调整后网格: ${this.cols}列 × ${this.rows}行，网格尺寸: ${Math.round(this.gridWidth)} × ${Math.round(this.gridHeight)}`);
    }

    /**
     * 获取随机可用网格位置
     * @returns {Object|null} 网格位置信息或null
     */
    getRandomAvailableGrid() {
      const totalGrids = this.cols * this.rows;
      const availableGrids = [];

      // 收集所有可用网格
      for (let i = 0; i < totalGrids; i++) {
        if (!this.occupiedGrids.has(i)) {
          availableGrids.push(i);
        }
      }

      if (availableGrids.length === 0) {
        console.warn('⚠️ 没有可用的网格位置');
        return null;
      }

      // 随机选择一个可用网格
      const randomIndex = Math.floor(Math.random() * availableGrids.length);
      const gridIndex = availableGrids[randomIndex];

      // 标记为已占用
      this.occupiedGrids.add(gridIndex);

      // 计算网格的行列位置
      const row = Math.floor(gridIndex / this.cols);
      const col = gridIndex % this.cols;

      // 计算网格的实际坐标
      const gridX = this.padding + col * this.gridWidth;
      const gridY = this.padding + row * this.gridHeight;

      return {
        gridIndex,
        row,
        col,
        gridX,
        gridY,
        gridWidth: this.gridWidth,
        gridHeight: this.gridHeight
      };
    }

    /**
     * 在网格内随机定位卡片
     * @param {Object} grid - 网格信息
     * @returns {Object} 卡片位置
     */
    getRandomPositionInGrid(grid) {
      // 计算卡片在网格内的可移动范围
      const maxOffsetX = grid.gridWidth - this.cardWidth;
      const maxOffsetY = grid.gridHeight - this.cardHeight;

      // 确保有足够的空间
      const safeOffsetX = Math.max(0, maxOffsetX);
      const safeOffsetY = Math.max(0, maxOffsetY);

      // 在网格内随机定位
      const offsetX = Math.random() * safeOffsetX;
      const offsetY = Math.random() * safeOffsetY;

      const x = grid.gridX + offsetX;
      const y = grid.gridY + offsetY;

      console.log(`📍 网格(${grid.row},${grid.col}) -> 卡片位置(${Math.round(x)}, ${Math.round(y)})`);

      return {
        x: Math.round(x),
        y: Math.round(y)
      };
    }

    /**
     * 重置网格占用状态
     */
    reset() {
      this.occupiedGrids.clear();
      console.log('🔄 网格系统已重置');
    }
  }

  /**
   * 检查渲染准备状态
   * @returns {boolean} 是否准备就绪
   */
  const checkRenderReadiness = () => {
    console.log('🔍 检查渲染准备状态...')

    // 首先检查基础数据
    console.log('📊 基础数据检查:')
    console.log('  - wordsForCurrentLevel.length:', wordsForCurrentLevel.value.length)
    console.log('  - currentLevel:', currentLevel.value)
    console.log('  - gameBoard.length:', gameBoard.value.length)

    // 检查词汇数据
    if (wordsForCurrentLevel.value.length < 8) {
      console.error('❌ 词汇数据不足，需要8个词汇，当前:', wordsForCurrentLevel.value.length)
      debugGameState()
      return false;
    }

    // 检查游戏数据
    if (gameBoard.value.length === 0) {
      console.error('❌ gameBoard数据为空')
      debugGameState()
      return false;
    }

    if (gameBoard.value.length !== 16) {
      console.error('❌ gameBoard数据不完整，期望16张卡片，实际:', gameBoard.value.length)
      debugGameState()
      return false;
    }

    // 检查每张卡片的必要属性
    for (let i = 0; i < gameBoard.value.length; i++) {
      const card = gameBoard.value[i];
      if (!card.position || !card.cardSize || !card.word) {
        console.error(`❌ 卡片${i + 1}数据不完整:`, card)
        return false;
      }

      if (typeof card.position.x !== 'number' || typeof card.position.y !== 'number') {
        console.error(`❌ 卡片${i + 1}位置数据无效:`, card.position)
        return false;
      }
    }

    // 检查关卡数据
    if (!currentLevel.value) {
      console.error('❌ 当前关卡数据为空')
      debugGameState()
      return false;
    }

    console.log('✅ 渲染准备状态检查通过')
    return true;
  };

  /**
   * 模拟游戏加载进度
   * @param {number} duration - 加载持续时间（毫秒）
   * @returns {Promise} 加载完成的Promise
   */
  const simulateGameLoading = async (duration = 1500) => {
    console.log('🎮 开始模拟游戏加载进度...')

    const steps = [
      { progress: 0, message: '初始化游戏引擎...' },
      { progress: 20, message: '加载游戏资源...' },
      { progress: 40, message: '准备卡片数据...' },
      { progress: 60, message: '生成游戏布局...' },
      { progress: 80, message: '优化游戏性能...' },
      { progress: 95, message: '最终检查...' },
      { progress: 100, message: '加载完成！' }
    ];

    const stepDuration = duration / steps.length;

    for (let i = 0; i < steps.length; i++) {
      const step = steps[i];
      loadingProgress.value = step.progress;
      loadingMessage.value = step.message;

      console.log(`📊 加载进度: ${step.progress}% - ${step.message}`)

      // 等待一段时间再进入下一步
      await new Promise(resolve => setTimeout(resolve, stepDuration));
    }

    console.log('✅ 游戏加载进度模拟完成')
  };

  /**
   * 模拟重玩加载进度
   * @param {number} duration - 加载持续时间（毫秒）
   * @returns {Promise} 加载完成的Promise
   */
  const simulateReplayLoading = async (duration = 1200) => {
    console.log('🔄 开始模拟重玩加载进度...')

    const steps = [
      { progress: 0, message: '重置游戏状态...' },
      { progress: 25, message: '清理旧数据...' },
      { progress: 50, message: '重新生成布局...' },
      { progress: 75, message: '优化卡片位置...' },
      { progress: 100, message: '重玩准备完成！' }
    ];

    const stepDuration = duration / steps.length;

    for (let i = 0; i < steps.length; i++) {
      const step = steps[i];
      loadingProgress.value = step.progress;
      loadingMessage.value = step.message;

      console.log(`🔄 重玩进度: ${step.progress}% - ${step.message}`)

      // 等待一段时间再进入下一步
      await new Promise(resolve => setTimeout(resolve, stepDuration));
    }

    console.log('✅ 重玩加载进度模拟完成')
  };

  /**
   * 基于网格系统生成卡片位置
   * @param {Array} cards - 卡片数据数组
   * @param {number} containerWidth - 容器宽度
   * @param {number} containerHeight - 容器高度
   * @returns {Array} 带有位置信息的卡片数组
   */
  const generateGridBasedPositions = (cards, containerWidth, containerHeight) => {
    console.log('🎯 开始基于网格系统生成卡片位置...')
    console.log('📊 输入参数检查:')
    console.log('  - cards.length:', cards.length)
    console.log('  - containerWidth:', containerWidth)
    console.log('  - containerHeight:', containerHeight)
    console.log('  - cards:', cards)

    // 输入验证
    if (!Array.isArray(cards) || cards.length === 0) {
      console.error('❌ 无效的卡片数组:', cards)
      return []
    }

    if (!containerWidth || !containerHeight) {
      console.error('❌ 无效的容器尺寸:', { containerWidth, containerHeight })
      return cards // 返回原始卡片数组
    }

    // 创建网格系统
    const gridSystem = new GridSystem(containerWidth, containerHeight, cards.length);

    // 保存网格系统实例用于调试
    currentGridSystem.value = gridSystem;

    // 为每张卡片分配网格位置
    const updatedCards = cards.map((card, index) => {
      console.log(`🔄 为第${index + 1}张卡片分配网格位置: ${card.type} - ${card.word ? (card.type === 'english' ? card.word.english : card.word.chinese) : 'unknown'}`)

      // 获取随机可用网格
      const grid = gridSystem.getRandomAvailableGrid();

      if (!grid) {
        console.error(`❌ 无法为卡片${index + 1}分配网格位置`);
        // 降级到容器中心位置
        return {
          ...card,
          position: {
            x: Math.round(containerWidth / 2 - 80),
            y: Math.round(containerHeight / 2 - 17.5)
          },
          cardSize: { width: 160, height: 35 }
        };
      }

      // 在网格内随机定位卡片
      const position = gridSystem.getRandomPositionInGrid(grid);

      return {
        ...card,
        position: position,
        cardSize: { width: 160, height: 35 },
        gridInfo: {
          gridIndex: grid.gridIndex,
          row: grid.row,
          col: grid.col
        }
      };
    });

    console.log('✅ 网格位置分配完成')
    console.log(`📊 使用了${gridSystem.occupiedGrids.size}个网格，总共${gridSystem.cols * gridSystem.rows}个可用网格`)

    // 验证返回数据的完整性
    console.log('🔍 验证返回数据:')
    console.log('  - updatedCards.length:', updatedCards.length)
    console.log('  - 前3张卡片:', updatedCards.slice(0, 3))

    // 检查每张卡片是否有必要的属性
    const invalidCards = updatedCards.filter(card =>
      !card.position ||
      typeof card.position.x !== 'number' ||
      typeof card.position.y !== 'number' ||
      !card.cardSize ||
      !card.word ||
      !card.type
    )

    if (invalidCards.length > 0) {
      console.error('❌ 发现无效卡片:', invalidCards)
    } else {
      console.log('✅ 所有卡片数据验证通过')
    }

    return updatedCards;
  };

  /**
   * 获取网格线信息用于调试显示
   * @returns {Array} 网格线数组
   */
  const getGridLines = () => {
    if (!currentGridSystem.value) return [];

    const gridSystem = currentGridSystem.value;
    const gridLines = [];

    for (let row = 0; row < gridSystem.rows; row++) {
      for (let col = 0; col < gridSystem.cols; col++) {
        const gridIndex = row * gridSystem.cols + col;
        const x = gridSystem.padding + col * gridSystem.gridWidth;
        const y = gridSystem.padding + row * gridSystem.gridHeight;

        gridLines.push({
          x: Math.round(x),
          y: Math.round(y),
          width: Math.round(gridSystem.gridWidth),
          height: Math.round(gridSystem.gridHeight),
          label: `${row},${col}`
        });
      }
    }

    return gridLines;
  };

  // 验证卡片布局质量 - 使用动态间距验证
  const validateCardLayout = (cards, containerWidth, containerHeight) => {
    let overlappingCount = 0;
    let outOfBoundsCount = 0;
    const margin = 30; // 边距

    console.log(`开始验证 ${cards.length} 张卡片的布局质量...`);

    for (let i = 0; i < cards.length; i++) {
      const card1 = cards[i];

      // 检查是否超出边界（考虑边距）
      const cardRight = card1.position.x + card1.cardSize.width;
      const cardBottom = card1.position.y + card1.cardSize.height;

      if (card1.position.x < margin || card1.position.y < margin ||
          cardRight > containerWidth - margin || cardBottom > containerHeight - margin) {
        outOfBoundsCount++;
        console.error(`❌ 卡片${i + 1}(${card1.type}:${card1.word[card1.type]})超出安全边界:`, {
          position: card1.position,
          size: card1.cardSize,
          cardRight: cardRight,
          cardBottom: cardBottom,
          containerWidth: containerWidth,
          containerHeight: containerHeight,
          margin: margin,
          超出左边界: card1.position.x < margin,
          超出上边界: card1.position.y < margin,
          超出右边界: cardRight > containerWidth - margin,
          超出下边界: cardBottom > containerHeight - margin
        });
      }

      // 检查与其他卡片的距离
      for (let j = i + 1; j < cards.length; j++) {
        const card2 = cards[j];
        const rect1 = {
          x: card1.position.x,
          y: card1.position.y,
          width: card1.cardSize.width,
          height: card1.cardSize.height
        };
        const rect2 = {
          x: card2.position.x,
          y: card2.position.y,
          width: card2.cardSize.width,
          height: card2.cardSize.height
        };

        // 使用动态间距验证
        const requiredSpacing = calculateCardSpacing(card1.cardSize, card2.cardSize);
        if (isOverlapping(rect1, rect2, requiredSpacing)) {
          overlappingCount++;
          const distance = Math.sqrt(
            Math.pow(card1.position.x - card2.position.x, 2) +
            Math.pow(card1.position.y - card2.position.y, 2)
          );
          console.error(`❌ 卡片${i + 1}(${card1.type})和卡片${j + 1}(${card2.type})间距不足:`,
            `实际距离: ${Math.round(distance)}rpx < 要求间距: ${requiredSpacing}rpx`,
            `卡片1: (${card1.position.x}, ${card1.position.y}) 尺寸(${card1.cardSize.width}×${card1.cardSize.height})`,
            `卡片2: (${card2.position.x}, ${card2.position.y}) 尺寸(${card2.cardSize.width}×${card2.cardSize.height})`
          );
        }
      }
    }

    // 输出验证结果
    console.log(`📊 布局质量检查结果:`);
    console.log(`   - 总卡片数: ${cards.length}`);
    console.log(`   - 超出边界: ${outOfBoundsCount}张`);
    console.log(`   - 距离过近: ${overlappingCount}对`);
    console.log(`   - 容器尺寸: ${containerWidth}×${containerHeight}rpx`);

    if (overlappingCount === 0 && outOfBoundsCount === 0) {
      console.log('✅ 卡片布局质量完美，无重叠和越界问题');
      return true;
    } else {
      console.warn('⚠️ 卡片布局存在问题，需要优化算法');
      return false;
    }
  };

  const initializeGameBoard = () => {
    const words = wordsForCurrentLevel.value;
    if (words.length < 8) {
      uni.showToast({ title: "词汇数量不足", icon: "none" });
      return;
    }

    // 获取动态游戏区域尺寸
    const { containerWidth, containerHeight } = getGameBoardDimensions();

    console.log(`游戏区域尺寸: ${containerWidth}rpx × ${containerHeight}rpx`);

    // 创建分离的英文和中文卡片
    let cards = [];

    // 预先计算所有卡片的尺寸和颜色（固定尺寸）
    const cardWidth = 160; // 固定宽度
    const cardHeight = 35; // 固定高度
    const cardSize = { width: cardWidth, height: cardHeight };

    const cardData = words.slice(0, 8).map(word => ({
      word,
      cardSize: cardSize, // 所有卡片使用相同尺寸
      color: generateRandomLightColor() // 每对单词使用相同颜色
    }));

    // 由于所有卡片尺寸相同，不需要排序
    console.log('所有卡片使用固定尺寸:', cardSize);

    // 创建所有卡片数据（不包含位置信息）
    for (let i = 0; i < 8; i++) {
      const { word, cardSize, color } = cardData[i];

      // 创建英文卡片数据
      cards.push({
        id: tileIdCounter++,
        word: word,
        color: color,
        selected: false,
        matched: false,
        pairId: i, // 用于标识配对
        type: 'english', // 标识为英文卡片
        cardSize: cardSize,
      });

      // 创建中文卡片数据
      cards.push({
        id: tileIdCounter++,
        word: word,
        color: color,
        selected: false,
        matched: false,
        pairId: i, // 相同的pairId表示是一对
        type: 'chinese', // 标识为中文卡片
        cardSize: cardSize,
      });
    }

    console.log('卡片数据创建完成，开始分配网格位置...')

    // 使用网格系统分配位置
    const _cards = generateGridBasedPositions(cards, containerWidth, containerHeight);

    console.log(_cards, '_cards')

    // 分别打乱英文和中文卡片的顺序（保持位置不变）
    const englishCards = _cards.filter(card => card.type === 'english');
    const chineseCards = _cards.filter(card => card.type === 'chinese');

    // 打乱英文卡片的内容分配
    for (let i = englishCards.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      // 只交换卡片的单词内容和颜色，不交换位置
      const tempWord = englishCards[i].word;
      const tempColor = englishCards[i].color;
      const tempPairId = englishCards[i].pairId;

      englishCards[i].word = englishCards[j].word;
      englishCards[i].color = englishCards[j].color;
      englishCards[i].pairId = englishCards[j].pairId;

      englishCards[j].word = tempWord;
      englishCards[j].color = tempColor;
      englishCards[j].pairId = tempPairId;
    }

    // 打乱中文卡片的内容分配
    for (let i = chineseCards.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      // 只交换卡片的单词内容和颜色，不交换位置
      const tempWord = chineseCards[i].word;
      const tempColor = chineseCards[i].color;
      const tempPairId = chineseCards[i].pairId;

      chineseCards[i].word = chineseCards[j].word;
      chineseCards[i].color = chineseCards[j].color;
      chineseCards[i].pairId = chineseCards[j].pairId;

      chineseCards[j].word = tempWord;
      chineseCards[j].color = tempColor;
      chineseCards[j].pairId = tempPairId;
    }

    // 将处理后的卡片重新组合
    cards = [...englishCards, ...chineseCards];

    // 验证卡片布局质量
    validateCardLayout(cards, containerWidth, containerHeight);

    // 所有计算完成后，一次性设置游戏状态
    console.log('所有计算完成，设置游戏状态...')
    gameBoard.value = cards;
    totalPairs.value = 8;
    matchedPairs.value = 0;
    console.log('游戏状态设置完成')

    // 输出调试信息
    console.log(`成功生成 ${cards.length} 张卡片，位置分布:`);

    console.log(`英文卡片 (${englishCards.length}张):`);
    englishCards.forEach((card, index) => {
      console.log(`  英文${index + 1}: ${card.word.english} (pairId:${card.pairId}) 位置(${card.position.x}, ${card.position.y})`);
    });

    console.log(`中文卡片 (${chineseCards.length}张):`);
    chineseCards.forEach((card, index) => {
      console.log(`  中文${index + 1}: ${card.word.chinese} (pairId:${card.pairId}) 位置(${card.position.x}, ${card.position.y})`);
    });
  };

  // 异步版本的initializeGameBoard（用于优化渲染）
  const initializeGameBoardAsync = async () => {
    console.log('🎲 开始异步初始化游戏棋盘...')

    // 详细的数据检查
    console.log('📊 棋盘初始化数据检查:')
    console.log('  - currentLevel:', currentLevel.value)
    console.log('  - currentLevelDetail:', currentLevelDetail.value)
    console.log('  - selectedLibraryInfo:', selectedLibraryInfo.value)
    console.log('  - useMockData:', useMockData.value)

    const words = wordsForCurrentLevel.value;
    console.log('  - wordsForCurrentLevel:', words)
    console.log('  - words.length:', words.length)

    if (words.length < 8) {
      const errorMsg = `词汇数量不足: 需要8个词汇，当前只有${words.length}个`;
      console.error('❌', errorMsg);
      console.error('📊 详细状态信息:')
      debugGameState()
      throw new Error(errorMsg);
    }

    // 获取动态游戏区域尺寸
    const { containerWidth, containerHeight } = getGameBoardDimensions();
    console.log(`📐 游戏区域尺寸: ${containerWidth}rpx × ${containerHeight}rpx`);

    // 创建分离的英文和中文卡片
    let cards = [];

    // 预先计算所有卡片的尺寸和颜色（固定尺寸）
    console.log('🎨 预计算卡片尺寸和颜色...')
    const cardWidth = 160; // 固定宽度
    const cardHeight = 35; // 固定高度
    const cardSize = { width: cardWidth, height: cardHeight };

    const cardData = words.slice(0, 8).map(word => ({
      word,
      cardSize: cardSize, // 所有卡片使用相同尺寸
      color: generateRandomLightColor()
    }));

    // 由于所有卡片尺寸相同，不需要排序
    console.log('📏 所有卡片使用固定尺寸:', cardSize);

    // 创建所有卡片数据（不包含位置信息）
    console.log('📍 开始创建卡片数据...')

    for (let i = 0; i < 8; i++) {
      const { word, cardSize, color } = cardData[i];

      console.log(`🔄 创建第${i + 1}对卡片数据: ${word.english} / ${word.chinese}`)

      // 创建英文卡片数据
      cards.push({
        id: tileIdCounter++,
        word: word,
        color: color,
        selected: false,
        matched: false,
        pairId: i,
        type: 'english',
        cardSize: cardSize,
      });

      // 创建中文卡片数据
      cards.push({
        id: tileIdCounter++,
        word: word,
        color: color,
        selected: false,
        matched: false,
        pairId: i,
        type: 'chinese',
        cardSize: cardSize,
      });

      // 每创建几张卡片后让出控制权，避免阻塞UI
      if (i % 2 === 1) {
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    }

    console.log('✅ 卡片数据创建完成，开始分配网格位置...')

    // 使用网格系统分配位置
    cards = generateGridBasedPositions(cards, containerWidth, containerHeight);

    // 批量处理游戏数据
    console.log('🔄 开始批量处理游戏数据...')
    await batchProcessGameData(cards, containerWidth, containerHeight);

    console.log(`🎉 成功生成 ${cards.length} 张卡片，位置分布完成`)

    // 所有计算完成后，一次性设置游戏状态
    console.log('🎯 所有计算完成，设置游戏状态...')
    gameBoard.value = cards;
    totalPairs.value = 8;
    matchedPairs.value = 0;
    console.log('✅ 游戏状态设置完成，准备渲染')
  };

  const handleTileClick = (index) => {
    // 如果正在检查匹配或游戏结束，不允许点击
    if (isChecking.value || isGameEndModalVisible.value) return;

    // 播放点击音效
    audioManager.playSoundEffect('click')

    const tile = gameBoard.value[index];

    // 如果卡片已经匹配，不允许点击
    if (tile.matched) return;

    // 如果已经选择了两张卡片，不允许再选择
    if (selectedTiles.value.length >= 2) return;

    // 如果点击的是已经选中的卡片，取消选择
    if (tile.selected) {
      tile.selected = false;
      selectedTiles.value = selectedTiles.value.filter(
        (item) => item.index !== index
      );
      return;
    }

    // 选择卡片
    tile.selected = true;
    selectedTiles.value.push({ index, tile });

    // 如果选择了两张卡片，检查是否匹配
    if (selectedTiles.value.length === 2) {
      isChecking.value = true;
      setTimeout(() => {
        checkMatch();
      }, 500); // 延迟0.5秒让用户看清楚选择
    }
  };

  const checkMatch = () => {
    const [tile1, tile2] = selectedTiles.value;

    // 检查是否为英文和中文的配对（不能是相同类型）
    const isValidPair = (tile1.tile.type !== tile2.tile.type) &&
                       (tile1.tile.pairId === tile2.tile.pairId);

    if (isValidPair) {
      // 匹配成功：英文和对应的中文配对
      tile1.tile.matched = true;
      tile2.tile.matched = true;
      tile1.tile.selected = false;
      tile2.tile.selected = false;

      matchedPairs.value++;

      const englishWord = tile1.tile.type === 'english' ? tile1.tile.word.english : tile2.tile.word.english;
      const chineseWord = tile1.tile.type === 'chinese' ? tile1.tile.word.chinese : tile2.tile.word.chinese;

      // 播放成功音效和震动
      audioManager.playSoundEffect('success')
      audioManager.vibrate('short')

      console.log(`配对成功！已完成 ${matchedPairs.value}/${totalPairs.value} 对`)

      // 检查是否完成游戏
      if (matchedPairs.value === totalPairs.value) {
        console.log("🎉 恭喜！所有配对完成！")

        // 停止游戏计时
        stopGameTimer()

        // 计算星级评定
        gameStars.value = calculateStars(finalGameTime.value, currentLevelDetail.value)
        console.log(`⭐ 游戏完成时间: ${finalGameTime.value}秒, 获得星级: ${gameStars.value}`)

        // 播放完成音效和长震动
        setTimeout(() => {
          audioManager.playSoundEffect('complete')
          audioManager.vibrate('long')
        }, 500)

        setTimeout(() => {
          endGame(true);
        }, 1000);
      }
    } else {
      // 匹配失败，直接重置当前关卡

      // 播放失败音效
      audioManager.playSoundEffect('fail')

      // 显示重置提示
      showError("匹配错误，正在重置本关...", 1500)

      // 延迟重置游戏，让用户看到提示信息
      setTimeout(async () => {
        await replayGame()
      }, 1500)
    }

    // 清除选择状态
    selectedTiles.value = [];
    isChecking.value = false;
  };

  /**
   * 切换当前关卡的收藏状态
   */
  const toggleCurrentLevelFavorite = async () => {
    try {
      // 播放点击音效
      audioManager.playSoundEffect('click')

      const levelId = currentLevelDetail.value?.id || selectedLevelData.value?.id || currentLevelId.value
      if (!levelId) {
        console.warn('无法获取关卡ID，跳过收藏操作')
        return
      }

      if (isCurrentLevelFavorited.value) {
        // 取消收藏
        await weixinApi.removeFavorite(levelId)
        isCurrentLevelFavorited.value = false

        uni.showToast({
          title: '已取消收藏',
          icon: 'success',
          duration: 1500
        })
      } else {
        // 添加收藏
        await weixinApi.addFavorite(levelId)
        isCurrentLevelFavorited.value = true

        uni.showToast({
          title: '已添加收藏',
          icon: 'success',
          duration: 1500
        })
      }

      // 播放成功音效
      audioManager.playSoundEffect('complete')
    } catch (error) {
      console.error('收藏操作失败:', error)

      // 播放失败音效
      audioManager.playSoundEffect('fail')

      uni.showToast({
        title: '操作失败，请重试',
        icon: 'none',
        duration: 1500
      })
    }
  }

  /**
   * 检查当前关卡的收藏状态
   */
  const checkCurrentLevelFavoriteStatus = async () => {
    try {
      const levelId = currentLevelDetail.value?.id || selectedLevelData.value?.id || currentLevelId.value
      if (!levelId) {
        return
      }

      // 获取用户收藏列表
      const favorites = await weixinApi.getUserFavorites()
      isCurrentLevelFavorited.value = favorites.favorites.some(level => level.id === levelId)
    } catch (error) {
      console.warn('检查收藏状态失败:', error)
      isCurrentLevelFavorited.value = false
    }
  }

  // 返回首页
  const goBackHome = () => {
    uni.navigateBack({ delta: 1 });
  };

  /**
   * 检测开发环境
   */
  const checkDevelopmentEnvironment = () => {
    try {
      isDevelopment.value = shouldShowDebugButtons()
      console.log('开发环境检测结果:', isDevelopment.value)
    } catch (error) {
      console.error('检测开发环境失败:', error)
      // 默认不显示调试按钮
      isDevelopment.value = false
    }
  }

  /**
   * 跳转到调试页面
   */
  const goToDebug = () => {
    // 播放点击音效
    audioManager.playSoundEffect('click')

    uni.navigateTo({
      url: '/pages/debug/index'
    })
  }

  /**
   * 显示设置弹窗
   */
  const showSettings = () => {
    // 播放点击音效
    audioManager.playSoundEffect('click')

    showSettingsModal.value = true
    console.log('显示设置弹窗')
  }

  /**
   * 关闭设置弹窗
   */
  const closeSettings = () => {
    showSettingsModal.value = false
    console.log('关闭设置弹窗')
  }

  /**
   * 处理设置变更
   */
  const handleSettingsChange = (newSettings: GameSettings) => {
    gameSettings.value = { ...newSettings }
    console.log('设置已更新:', newSettings)

    // 如果背景音乐设置发生变化
    if (newSettings.backgroundMusic !== gameSettings.value.backgroundMusic) {
      if (newSettings.backgroundMusic) {
        // 开启背景音乐
        audioManager.playBackgroundMusic('game')
      } else {
        // 关闭背景音乐
        audioManager.stopBackgroundMusic()
      }
    }
  }

  // 重置游戏
  // 同步版本的resetGame（保持兼容性）
  const resetGame = () => {
    selectedTiles.value = [];
    matchedPairs.value = 0;
    isChecking.value = false;
    isGameEndModalVisible.value = false;
    initializeGameBoard();
  };

  // 异步版本的resetGame（用于优化渲染）
  const resetGameAsync = async () => {
    console.log('🔄 开始重置游戏状态...')
    selectedTiles.value = [];
    matchedPairs.value = 0;
    isChecking.value = false;
    isGameEndModalVisible.value = false;

    // 异步初始化游戏棋盘
    await initializeGameBoardAsync();
    console.log('✅ 游戏重置完成')
  };

  /**
   * 重玩游戏 - 完整的loading和渲染流程
   */
  const replayGame = async () => {
    try {
      console.log('🎮 开始重玩游戏...')

      // 1. 设置重玩状态
      isReplaying.value = true
      isGameReady.value = false
      isGameInitializing.value = false
      isCalculatingPositions.value = true
      isPreparingRender.value = false
      isLoadingGame.value = false

      // 重置游戏状态
      selectedTiles.value = [];
      matchedPairs.value = 0;
      isChecking.value = false;
      isGameEndModalVisible.value = false;

      // 显示位置计算状态
      console.log('🧮 重新计算卡片位置...')
      await new Promise(resolve => setTimeout(resolve, 100)) // 确保UI更新

      // 2. 重新初始化游戏棋盘
      await initializeGameBoardAsync()

      // 3. 准备渲染阶段
      console.log('🎨 准备重新渲染...')
      isCalculatingPositions.value = false
      isPreparingRender.value = true

      await new Promise(resolve => setTimeout(resolve, 100))

      // 检查渲染准备状态
      if (!checkRenderReadiness()) {
        throw new Error('重玩数据生成失败')
      }

      // 4. 游戏加载阶段
      console.log('🎮 重新加载游戏...')
      isPreparingRender.value = false
      isLoadingGame.value = true

      // 重置加载进度
      loadingProgress.value = 0
      loadingMessage.value = '正在重新启动游戏...'

      // 模拟重玩加载过程（稍短一些）
      await simulateReplayLoading(1200)

      // 5. 完成重玩
      console.log('🎉 重玩完成，开始渲染')
      isLoadingGame.value = false
      isReplaying.value = false
      isGameReady.value = true

      // 6. 重新开始计时
      console.log('⏱️ 重新开始游戏计时...')
      startGameTimer()

      console.log('✨ 重玩成功，欢迎再次挑战！')

    } catch (error) {
      console.error('❌ 重玩失败:', error)
      isReplaying.value = false
      isGameInitializing.value = false
      isCalculatingPositions.value = false
      isPreparingRender.value = false
      isLoadingGame.value = false
      isGameReady.value = false

      showError(`重玩失败: ${error.message || '未知错误'}`)
    }
  };

  // 下一关
  const nextLevel = () => {
    // 如果是从关卡选择页面进入的，返回关卡选择页面让用户选择下一关
    if (currentLevelDetail.value) {
      console.log('🎯 从扩展关卡系统返回关卡选择页面')
      uni.navigateBack({
        delta: 1,
        success: () => {
          showSuccess('请选择下一关卡', 1000)
        },
        fail: () => {
          // 如果无法返回，则跳转到关卡选择页面
          uni.redirectTo({
            url: '/pages/level-selection/index'
          })
        }
      })
      return
    }

    // 词库系统的下一关逻辑
    if (canGoToNextLevel.value) {
      currentLevelId.value++;
      currentLevel.value = {
        id: currentLevelId.value,
        name: `第${currentLevelId.value}关`,
        wordsCount: 8,
      };

      // 保存进度
      if (selectedLibraryInfo.value) {
        saveCurrentLevelId(selectedLibraryInfo.value.id, currentLevelId.value);
      }

      // 重置游戏
      resetGame();

      showSuccess(`进入第${currentLevelId.value}关`, 1000)
    } else {
      showError("已经是最后一关了", 1000)
    }
  };

  const endGame = async (won) => {
    gameWon.value = won

    if (won) {
      gameResultText.value = "恭喜过关！"
      // 游戏胜利，调用通关API获取服务端星级
      await handleGameCompletion()
    } else {
      // 游戏失败（时间到期或其他原因）
      gameResultText.value = isTimeUp.value ? "时间到期！" : "挑战失败！"
      // 失败时也调用通关API记录尝试
      await handleGameFailure()
    }

    // 显示游戏结束弹窗
    isGameEndModalVisible.value = true
  }

  /**
   * 处理游戏完成逻辑
   */
  const handleGameCompletion = async () => {
    try {
      isProgressSyncing.value = true

      // 1. 保存本地进度（保持兼容性）
      const selectedLevel = uni.getStorageSync("selectedLevel")
      if (selectedLevel) {
        try {
          const levelData = JSON.parse(selectedLevel)
          const completedKey = `level_${levelData.id}_completed`
          uni.setStorageSync(completedKey, "true")
          console.log(`Level ${levelData.id} marked as completed locally`)
        } catch (e) {
          console.error("Failed to save local level completion:", e)
        }
      }

      // 2. 调用通关接口
      await callCompleteLevelAPI()

      // 3. 刷新用户信息
      await refreshUserInfo()

    } catch (error) {
      console.error('处理游戏完成失败:', error)
      // 即使同步失败，也不影响游戏体验
    } finally {
      isProgressSyncing.value = false
    }
  }

  /**
   * 处理游戏失败逻辑
   */
  const handleGameFailure = async () => {
    try {
      isProgressSyncing.value = true

      // 记录失败尝试（可选，根据需要决定是否调用API）
      console.log('游戏失败，记录尝试:', {
        finalTime: finalGameTime.value,
        isTimeUp: isTimeUp.value,
        stars: gameStars.value
      })

      // 这里可以调用失败记录API，如果后端支持的话
      // await weixinApi.recordGameFailure(...)

    } catch (error) {
      console.error('处理游戏失败失败:', error)
      // 失败处理失败也不影响用户体验
    } finally {
      isProgressSyncing.value = false
    }
  }

  /**
   * 调用通关关卡接口
   */
  const callCompleteLevelAPI = async () => {
    try {
      if (!userInfo.value || !userInfo.value.id) {
        console.warn('用户信息不存在，跳过通关接口调用')
        return
      }

      let levelId = ''

      // 优先使用关卡详情中的ID
      if (currentLevelDetail.value) {
        levelId = currentLevelDetail.value.id
      }
      // 备用：使用选择的关卡数据
      else if (selectedLevelData.value) {
        levelId = selectedLevelData.value.id
      }
      // 最后备用：使用当前关卡ID
      else if (currentLevel.value) {
        levelId = currentLevel.value.id.toString()
      }

      if (!levelId) {
        console.warn('未找到关卡ID，跳过通关接口调用')
        return
      }

      console.log('调用通关接口:', {
        userId: userInfo.value.id,
        levelId: levelId,
        completionTime: finalGameTime.value,
        stars: gameStars.value
      })

      // 直接调用通关接口获取完整的响应信息
      const completeLevelResponse = await weixinApi.completeLevel(
        weixinApi.getOpenid(),
        levelId,
        finalGameTime.value
      )
      console.log('通关接口调用成功:', completeLevelResponse)

      // 使用服务端返回的星级更新客户端显示
      if (completeLevelResponse.stars) {
        gameStars.value = completeLevelResponse.stars
        console.log(`⭐ 服务端返回星级: ${completeLevelResponse.stars}`)
      }

      // 更新关卡详情的完成状态
      if (currentLevelDetail.value) {
        currentLevelDetail.value.isCompleted = true
        // 如果是扩展关卡信息，更新星级
        const extendedLevel = currentLevelDetail.value as any
        if ('userStars' in extendedLevel) {
          extendedLevel.userStars = Math.max(extendedLevel.userStars || 0, gameStars.value)
          extendedLevel.bestTime = Math.min(extendedLevel.bestTime || Infinity, finalGameTime.value)
        }
      }

      // 检查是否解锁了新关卡
      if (completeLevelResponse.hasUnlockedNewLevel) {
        // 播放解锁音效
        audioManager.playSoundEffect('unlock')

        // 显示解锁新关卡的提示
        uni.showModal({
          title: '恭喜通关！',
          content: `${completeLevelResponse.message}\n已解锁 ${completeLevelResponse.unlockedLevels} 关！`,
          showCancel: false,
          confirmText: '太棒了'
        })
      } else {
        // 检查每日解锁限制
        if (!completeLevelResponse.isVip && completeLevelResponse.remainingUnlocks <= 0) {
          uni.showModal({
            title: '通关成功',
            content: `${completeLevelResponse.message}\n今日解锁次数已用完，明天再来或分享获得额外机会！`,
            showCancel: true,
            cancelText: '明天再来',
            confirmText: '立即分享',
            success: (res) => {
              if (res.confirm) {
                // 触发分享
                uni.showShareMenu({
                  withShareTicket: true
                })
              }
            }
          })
        } else {
          // 显示通关成功提示
          showSuccess('恭喜通关！进度已同步', 2000)
        }
      }

      // 刷新用户信息以获取最新状态
      await refreshUserInfo()

    } catch (error) {
      console.error('调用通关接口失败:', error)
      // 不抛出错误，避免影响游戏体验
      showError('通关记录失败，但不影响游戏', 2000)
    }
  }



  /**
   * 刷新用户信息
   */
  const refreshUserInfo = async () => {
    try {
      const freshUserInfo = await weixinApi.refreshUserInfo()
      if (freshUserInfo) {
        userInfo.value = freshUserInfo

        // 保存更新后的用户信息到本地存储
        uni.setStorageSync('userInfo', JSON.stringify(freshUserInfo))

        console.log('用户信息已刷新并保存到本地:', freshUserInfo)
      }
    } catch (error) {
      console.error('刷新用户信息失败:', error)
    }
  }

  /**
   * 处理游戏页面分享
   * 根据uniapp官方文档实现
   */
  const onShareAppMessage = async (options: any) => {
    console.log('游戏页面分享触发:', options)

    // 构建分享参数
    const shareParams = {
      page: 'pages/game/index',
      levelId: currentLevelDetail.value?.id || (selectedLevelData.value as any)?.id,
      userId: userInfo.value?.id
    }

    // 处理分享奖励
    if (userInfo.value?.id) {
      handleGameShareReward(options, shareParams)
    }

    return await shareUtils.handleShareAppMessage(options, shareParams)
  }

  /**
   * 检查今日是否已经获取过分享奖励
   */
  const checkDailyShareReward = (userId: string): boolean => {
    try {
      const today = new Date().toDateString() // 获取今日日期字符串，如 "Mon Dec 25 2023"
      const storageKey = `daily_share_reward_${userId}_${today}`
      const hasSharedToday = uni.getStorageSync(storageKey)

      console.log(`检查每日分享奖励状态 - 用户: ${userId}, 日期: ${today}, 已分享: ${!!hasSharedToday}`)
      return !!hasSharedToday
    } catch (error) {
      console.error('检查每日分享奖励状态失败:', error)
      return false
    }
  }

  /**
   * 标记今日已获取分享奖励
   */
  const markDailyShareReward = (userId: string): void => {
    try {
      const today = new Date().toDateString()
      const storageKey = `daily_share_reward_${userId}_${today}`
      uni.setStorageSync(storageKey, true)

      console.log(`标记每日分享奖励完成 - 用户: ${userId}, 日期: ${today}`)
    } catch (error) {
      console.error('标记每日分享奖励失败:', error)
    }
  }

  /**
   * 处理游戏页面分享奖励
   */
  const handleGameShareReward = async (options: any, shareParams: any) => {
    try {
      if (!userInfo.value?.id) {
        console.warn('用户信息不存在，无法获取分享奖励')
        return
      }

      // 防止重复执行
      if (isHandlingGameShareReward) {
        console.log('游戏分享奖励正在处理中，跳过重复请求')
        return
      }

      // 检查今日是否已经获取过分享奖励
      const hasSharedToday = checkDailyShareReward(userInfo.value.id)
      if (hasSharedToday) {
        console.log('今日已获取过分享奖励，跳过本次请求')
        uni.showToast({
          title: '今日已获得分享奖励',
          icon: 'none',
          duration: 2000
        })
        return
      }

      // 标记开始处理
      isHandlingGameShareReward = true
      console.log('开始处理游戏页面分享奖励:', options)

      // 延迟获取奖励，确保分享完成
      setTimeout(async () => {
        try {
          const rewardResponse = await weixinApi.getShareReward()

          if (rewardResponse.success) {
            // 标记今日已获取分享奖励
            markDailyShareReward(userInfo.value!.id)

            // 更新本地用户信息
            if (rewardResponse.userInfo) {
              userInfo.value = rewardResponse.userInfo
              // 保存到本地存储
              uni.setStorageSync('userInfo', JSON.stringify(rewardResponse.userInfo))
            }

            // 显示奖励获得提示
            uni.showModal({
              title: '分享奖励',
              content: `恭喜获得${rewardResponse.reward.description}！可以继续挑战更多关卡了！今日分享奖励已领取完毕。`,
              showCancel: false,
              confirmText: '太棒了',
              success: () => {
                console.log('游戏分享奖励提示已显示')
              }
            })

            console.log('游戏分享奖励获取成功:', rewardResponse.reward)
          } else {
            console.log('游戏分享奖励获取失败:', rewardResponse.message)

            // 如果服务端返回今日已领取的消息，也标记本地状态
            if (rewardResponse.message.includes('今日') || rewardResponse.message.includes('已领取')) {
              markDailyShareReward(userInfo.value!.id)
            }
          }
        } catch (error) {
          console.error('获取游戏分享奖励失败:', error)
          // 不显示错误提示，避免影响用户体验
        } finally {
          // 重置处理状态
          isHandlingGameShareReward = false
          console.log('游戏分享奖励处理完成，重置状态')
        }
      }, 2000) // 延迟2秒，确保分享完成
    } catch (error) {
      console.error('处理游戏分享奖励失败:', error)
      // 重置处理状态
      isHandlingGameShareReward = false
    }
  }

  // 导出分享函数供小程序使用
  defineExpose({
    onShareAppMessage
  })
</script>

<style scoped>
  .page-box {
    height: calc(100vh - 44px);
    background-color: #fdf9f9; 
  }

  .game-page-container {
    padding: 16rpx;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
  }

  .card {
    /* Common card style */
    background-color: white;
    border-radius: 24rpx; /* rounded-xl or 2xl */
    padding: 16rpx;
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  }

  .library-name {
    display: block;
    font-size: 36rpx; /* text-xl */
    font-weight: 600; /* font-semibold */
    color: #111; /* text-purple-700 */
    margin-bottom: 8rpx;
  }

  /* 删除了关卡选择相关的样式 */
  .game-area {
    /* padding already from .card */
    margin-top: 32rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
  }
  .game-info-bar {
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 100%;
    padding: 10rpx 0;
    margin-bottom: 20rpx;
    font-size: 28rpx;
    color: #333;
  }

  .sync-status {
    font-size: 24rpx !important;
    color: #74b9ff !important;
    font-weight: 500;
  }

  /* H5环境提示样式 */
  .h5-mock-tip {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 8rpx 16rpx;
    border-radius: 12rpx;
    font-size: 20rpx;
    font-weight: bold;
    box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
    animation: h5Glow 2s ease-in-out infinite alternate;
  }

  @keyframes h5Glow {
    0% { box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3); }
    100% { box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.6); }
  }

  .replay-btn {
    background: linear-gradient(135deg, #fd79a8, #e84393);
    color: white;
    padding: 8rpx 16rpx;
    border-radius: 16rpx;
    font-size: 24rpx;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 60rpx;
    text-align: center;
    box-shadow: 0 2rpx 8rpx rgba(232, 67, 147, 0.3);
  }

  .replay-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 4rpx 12rpx rgba(232, 67, 147, 0.4);
  }

  .replay-btn-text {
    color: white;
    font-size: 24rpx;
    font-weight: 500;
  }

  .replay-btn-disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .replay-btn-disabled:hover {
    transform: none;
    box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
  }

  .debug-btn {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    padding: 8rpx 16rpx;
    border-radius: 16rpx;
    font-size: 20rpx;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 60rpx;
    text-align: center;
    box-shadow: 0 2rpx 8rpx rgba(108, 117, 125, 0.3);
  }

  .debug-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 4rpx 12rpx rgba(108, 117, 125, 0.4);
  }

  .debug-btn-text {
    color: white;
    font-size: 20rpx;
    font-weight: 500;
  }

  /* 网格调试按钮样式 */
  .grid-debug-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 12rpx 20rpx;
    border-radius: 20rpx;
    font-size: 20rpx;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2rpx 8rpx rgba(40, 167, 69, 0.3);
    margin-left: 10rpx;
  }

  .grid-debug-btn:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 4rpx 12rpx rgba(40, 167, 69, 0.4);
  }

  .grid-debug-btn-text {
    color: white;
    font-size: 20rpx;
    font-weight: 500;
  }

  /* 网格线样式 */
  .grid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
  }

  .grid-line {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .grid-label {
    font-size: 20rpx;
    color: rgba(255, 0, 0, 0.8);
    font-weight: bold;
    text-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.8);
  }
  .game-board {
    flex: 1;
    width: 100%;
    max-width: 750rpx;
    min-height: 800rpx; /* 增加最小高度，确保有足够空间 */
    height: auto;
    box-sizing: border-box;
    position: relative;
    margin: 0 auto 20rpx;
    padding: 25rpx; /* 增加内边距 */
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20rpx;
    overflow: visible; /* 改为visible，避免裁剪卡片 */
    box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    border: 2rpx solid rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
  }

  /* 检查匹配状态的样式 */
  .game-board.checking-match {
    opacity: 0.8;
    pointer-events: none; /* 禁用所有点击事件 */
  }

  .game-board.checking-match::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    z-index: 10;
    animation: checkingPulse 0.5s ease-in-out infinite alternate;
    border-radius: 20rpx;
  }

  @keyframes checkingPulse {
    0% { opacity: 0.1; }
    100% { opacity: 0.3; }
  }

  /* 加载状态样式 */
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 750rpx;
    min-height: 800rpx;
    height: auto;
    box-sizing: border-box;
    margin: 0 auto 20rpx;
    padding: 25rpx;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20rpx;
    box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  }

  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 6rpx solid rgba(0, 0, 0, 0.1);
    border-top: 6rpx solid #007AFF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 30rpx;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .loading-text {
    color: #666;
    font-size: 32rpx;
    text-align: center;
    font-weight: 500;
  }

  /* 错误状态样式 */
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 750rpx;
    min-height: 800rpx;
    height: auto;
    box-sizing: border-box;
    margin: 0 auto 20rpx;
    padding: 25rpx;
    background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
    border-radius: 20rpx;
    box-shadow: inset 0 2rpx 8rpx rgba(255, 0, 0, 0.1);
  }

  .error-text {
    color: #e53e3e;
    font-size: 32rpx;
    text-align: center;
    font-weight: 500;
    margin-bottom: 40rpx;
  }

  .retry-btn {
    background: linear-gradient(135deg, #e53e3e, #c53030);
    color: white;
    padding: 20rpx 40rpx;
    border-radius: 25rpx;
    font-size: 28rpx;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4rpx 12rpx rgba(229, 62, 62, 0.3);
  }

  .retry-btn:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 6rpx 16rpx rgba(229, 62, 62, 0.4);
  }

  .retry-btn-text {
    color: white;
    font-size: 28rpx;
    font-weight: 500;
  }

  /* 游戏加载状态样式 */
  .game-loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 750rpx;
    min-height: 800rpx;
    height: auto;
    box-sizing: border-box;
    margin: 0 auto 20rpx;
    padding: 50rpx 25rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20rpx;
    box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
  }

  .game-loading-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.1) 75%);
    background-size: 40rpx 40rpx;
    animation: moveBackground 2s linear infinite;
  }

  @keyframes moveBackground {
    0% { transform: translateX(0); }
    100% { transform: translateX(40rpx); }
  }

  .game-loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 1;
    position: relative;
  }

  .game-loading-spinner {
    position: relative;
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 40rpx;
  }

  .spinner-ring {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 6rpx solid transparent;
    border-radius: 50%;
    animation: spinRing 2s linear infinite;
  }

  .spinner-ring:nth-child(1) {
    border-top-color: #ffffff;
    animation-delay: 0s;
  }

  .spinner-ring:nth-child(2) {
    border-right-color: #ffffff;
    animation-delay: 0.5s;
    width: 80%;
    height: 80%;
    top: 10%;
    left: 10%;
  }

  .spinner-ring:nth-child(3) {
    border-bottom-color: #ffffff;
    animation-delay: 1s;
    width: 60%;
    height: 60%;
    top: 20%;
    left: 20%;
  }

  @keyframes spinRing {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .game-loading-title {
    color: white;
    font-size: 40rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 20rpx;
    text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
  }

  .game-loading-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 28rpx;
    text-align: center;
    margin-bottom: 60rpx;
    text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.2);
  }

  .loading-progress {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 400rpx;
  }

  .progress-bar {
    width: 100%;
    height: 8rpx;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4rpx;
    overflow: hidden;
    margin-bottom: 20rpx;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #ffffff, #f0f8ff);
    border-radius: 4rpx;
    transition: width 0.3s ease-out;
    box-shadow: 0 0 10rpx rgba(255, 255, 255, 0.5);
    position: relative;
    overflow: hidden;
  }

  .progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: progressShine 2s ease-in-out infinite;
  }

  @keyframes progressShine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  .progress-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 24rpx;
    text-align: center;
  }

  .board-tile {
    border-radius: 16rpx;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.15), 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
    border: 2rpx solid rgba(255, 255, 255, 0.8);
    padding: 12rpx 8rpx;
    backdrop-filter: blur(10rpx);
    position: absolute;
    z-index: 2; /* 确保卡片在网格线之上 */
    width: 160rpx;
    height: 35rpx;
    line-height: 35rpx;
  }

  .board-tile:hover {
    transform: scale(1.05) translateZ(0);
    box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.2), 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
    z-index: 2;
  }

  .board-tile.selected {
    transform: scale(1.08) translateZ(0);
    border-color: #ff6b35;
    box-shadow: 0 12rpx 24rpx rgba(255, 107, 53, 0.4), 0 6rpx 12rpx rgba(255, 107, 53, 0.2);
    z-index: 3;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 107, 53, 0.1));
  }

  .board-tile.matched {
    opacity: 0.7;
    transform: scale(0.95) translateZ(0);
    border-color: #28a745;
    box-shadow: 0 4rpx 12rpx rgba(40, 167, 69, 0.3);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(40, 167, 69, 0.1));
    z-index: 0;
  }

  /* 调试模式样式 */
  .board-tile.debug-mode {
    border: 3rpx dashed #dc3545 !important;
    background: rgba(220, 53, 69, 0.1) !important;
  }

  .board-tile.debug-mode::before {
    content: attr(data-position);
    position: absolute;
    top: -25rpx;
    left: 0;
    font-size: 16rpx;
    color: #dc3545;
    background: rgba(255, 255, 255, 0.9);
    padding: 2rpx 6rpx;
    border-radius: 8rpx;
    white-space: nowrap;
    z-index: 10;
  }

  .tile-word {
    font-weight: bold;
    color: #333;
    margin-bottom: 6rpx;
    text-align: center;
    line-height: 1.2;
    word-break: break-word;
  }

  /* 根据卡片大小调整英文字体大小 */
  .tile-short .tile-word {
    font-size: 24rpx;
  }

  .tile-medium .tile-word {
    font-size: 22rpx;
  }

  .tile-long .tile-word {
    font-size: 20rpx;
  }

  .tile-chinese {
    color: #666;
    text-align: center;
    line-height: 1.2;
    word-break: break-word;
  }

  /* 中文卡片专用样式 */
  .tile-chinese-only {
    font-weight: bold;
    color: #333;
    text-align: center;
    line-height: 1.2;
    word-break: break-word;
    font-size: 28rpx;
  }



  /* 根据卡片大小调整中文字体大小 */
  .tile-short .tile-chinese {
    font-size: 20rpx;
  }

  .tile-medium .tile-chinese {
    font-size: 18rpx;
  }

  .tile-long .tile-chinese {
    font-size: 16rpx;
  }

  /* 中文卡片字体大小调整 */
  .tile-short .tile-chinese-only {
    font-size: 26rpx;
  }

  .tile-medium .tile-chinese-only {
    font-size: 24rpx;
  }

  .tile-long .tile-chinese-only {
    font-size: 22rpx;
  }
  .game-controls {
    display: flex;
    justify-content: center;
    gap: 24rpx;
    width: 100%;
    margin-top: 20rpx;
  }
  .control-button {
    padding: 12rpx 24rpx;
    font-size: 24rpx;
    border-radius: 20rpx;
    border: none;
    background-color: #007aff;
    color: white;
    transition: background-color 0.3s ease;
    min-width: 120rpx;
  }

  .control-button:disabled {
    background-color: #ccc;
    color: #999;
  }

  .control-button:hover:not(:disabled) {
    background-color: #0056b3;
  }
  .game-start-text {
    display: block;
    font-size: 36rpx; /* text-xl */
    font-weight: bold;
  }
  /* Modal Styles (simplified) */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  .modal-content {
    background-color: white;
    padding: 40rpx;
    border-radius: 16rpx;
    text-align: center;
    min-width: 500rpx;
  }
  .modal-title {
    font-size: 36rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    display: block;
  }

  /* 游戏完成信息样式 */
  .game-completion-info {
    margin: 24rpx 0;
    padding: 24rpx;
    background: linear-gradient(135deg, #fff9e6, #fff3cd);
    border-radius: 16rpx;
    border: 2rpx solid rgba(255, 193, 7, 0.2);
  }

  .stars-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20rpx;
  }

  .stars-label {
    font-size: 24rpx;
    color: #856404;
    margin-bottom: 12rpx;
    font-weight: 500;
  }

  .stars-container {
    display: flex;
    gap: 8rpx;
  }

  .star-icon {
    font-size: 32rpx;
    color: #ddd;
    transition: color 0.3s;
  }

  .star-filled {
    color: #ffd700;
  }

  .completion-time {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12rpx 0;
    border-top: 1rpx solid rgba(255, 193, 7, 0.3);
  }

  .time-label {
    font-size: 24rpx;
    color: #856404;
    font-weight: 500;
  }

  .time-value {
    font-size: 28rpx;
    color: #6c5ce7;
    font-weight: bold;
  }

  /* 游戏计时器样式 */
  .game-timer {
    color: #6c5ce7;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  /* 时间警告样式（剩余10秒时） */
  .game-timer.time-warning {
    color: #e74c3c;
    font-weight: bold;
    animation: timeWarning 1s infinite;
  }

  @keyframes timeWarning {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
  }

  /* 收藏功能样式 */
  .favorite-section {
    margin-top: 20rpx;
    padding-top: 20rpx;
    border-top: 1rpx solid rgba(255, 193, 7, 0.3);
  }

  .favorite-btn {
    width: 100%;
    height: 80rpx;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 2rpx solid #dee2e6;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    transition: all 0.3s;
  }

  .favorite-btn.favorited {
    background: linear-gradient(135deg, #ffe6f0, #ffd6e7);
    border-color: #ff69b4;
  }

  .favorite-btn:active {
    transform: scale(0.98);
    opacity: 0.9;
  }

  .favorite-icon {
    font-size: 28rpx;
    transition: all 0.3s;
  }

  .favorite-text {
    font-size: 26rpx;
    color: #495057;
    font-weight: 500;
  }

  .favorite-btn.favorited .favorite-text {
    color: #d63384;
  }

  .favorite-btn.favorited .favorite-icon {
    animation: heartbeat 0.6s ease-in-out;
  }

  @keyframes heartbeat {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
  }
  .modal-buttons {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    margin-top: 32rpx;
  }

  .modal-button {
    padding: 16rpx 32rpx;
    font-size: 28rpx;
    border-radius: 24rpx;
    border: none;
    background-color: #f0f0f0;
    color: #333;
    transition: background-color 0.3s ease;
  }

  .modal-button.primary {
    background-color: #007aff;
    color: white;
  }

  .modal-button:hover {
    opacity: 0.8;
  }

  /* 关卡详情信息样式 */
  .level-detail-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 24rpx;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
  }

  .level-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12rpx;
  }

  .level-name {
    font-size: 32rpx;
    font-weight: bold;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  }

  .level-difficulty {
    font-size: 24rpx;
    background: rgba(255, 255, 255, 0.2);
    padding: 6rpx 12rpx;
    border-radius: 12rpx;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  }

  .level-description {
    font-size: 26rpx;
    opacity: 0.9;
    margin-bottom: 16rpx;
    line-height: 1.4;
  }

  .level-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .stats-item {
    font-size: 22rpx;
    background: rgba(255, 255, 255, 0.15);
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  }

  /* 备用：词库信息样式 */
  .selected-library-info {
    color: white;
    padding: 20rpx;
    border-radius: 15rpx;
    margin-bottom: 20rpx;
    text-align: center;
  }

  .library-name {
    font-size: 32rpx;
    font-weight: bold;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  }

  /* 右上角设置按钮样式 */
  .floating-settings-btn {
     width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 998;
    transition: all 0.3s ease;
    position: absolute;
    top: 20rpx;
    right: 20rpx;
  }

  .floating-settings-btn:active {
    transform: scale(0.9);
  }

  .settings-icon {
    font-size: 32rpx;
    color: #ffffff;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  }

  /* 悬浮调试按钮样式 */
  .floating-debug-btn {
    position: fixed;
    bottom: 100rpx;
    right: 40rpx;
    width: 100rpx;
    height: 100rpx;
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 20rpx rgba(255, 107, 107, 0.4);
    z-index: 999;
    transition: all 0.3s ease;
    animation: debugPulse 2s infinite;
  }

  .floating-debug-btn:active {
    transform: scale(0.9);
    box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.6);
  }

  .debug-icon {
    font-size: 36rpx;
    color: #ffffff;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  }

  @keyframes debugPulse {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 8rpx 20rpx rgba(255, 107, 107, 0.4);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 12rpx 24rpx rgba(255, 107, 107, 0.6);
    }
  }
</style>
