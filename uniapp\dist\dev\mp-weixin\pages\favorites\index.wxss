
.favorites-container.data-v-b2f01737 {
  min-height: 100vh;
  background: #fdf9f9;
  padding: 32rpx 24rpx;
}

/* 页面标题 */
.page-header.data-v-b2f01737 {
  text-align: center;
  margin-bottom: 48rpx;
  padding: 32rpx 24rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.page-title.data-v-b2f01737 {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.page-subtitle.data-v-b2f01737 {
  display: block;
  font-size: 28rpx;
  color: #4a5568;
  font-weight: 500;
}

/* 加载和错误状态 */
.loading-container.data-v-b2f01737, .error-container.data-v-b2f01737, .empty-container.data-v-b2f01737 {
  text-align: center;
  padding: 80rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  margin: 24rpx 0;
}
.loading-spinner.data-v-b2f01737 {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #ffffff;
  border-radius: 50%;
  animation: spin-b2f01737 1s linear infinite;
  margin: 0 auto 24rpx;
}
@keyframes spin-b2f01737 {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.loading-text.data-v-b2f01737, .error-title.data-v-b2f01737, .error-text.data-v-b2f01737, .empty-title.data-v-b2f01737, .empty-text.data-v-b2f01737 {
  color: #2d3748;
  font-size: 28rpx;
  margin-bottom: 16rpx;
  font-weight: 500;
}
.error-icon.data-v-b2f01737, .empty-icon.data-v-b2f01737 {
  font-size: 64rpx;
  margin-bottom: 24rpx;
}
.empty-title.data-v-b2f01737 {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}
.empty-text.data-v-b2f01737 {
  font-size: 26rpx;
  color: #4a5568;
  margin-bottom: 12rpx;
  line-height: 1.4;
  font-weight: 400;
}
.empty-hint.data-v-b2f01737 {
  font-size: 24rpx;
  color: #718096;
  margin-bottom: 40rpx;
  line-height: 1.4;
  font-weight: 400;
}
.empty-actions.data-v-b2f01737 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  align-items: center;
}
.retry-btn.data-v-b2f01737, .browse-btn.data-v-b2f01737 {
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  font-size: 26rpx;
  min-width: 200rpx;
  transition: all 0.2s;
}
.browse-btn.primary.data-v-b2f01737 {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #ffffff;
  border-color: transparent;
}
.browse-btn.secondary.data-v-b2f01737 {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: #ffffff;
  border-color: transparent;
}
.browse-btn.data-v-b2f01737:active {
  transform: scale(0.98);
  opacity: 0.9;
}
.retry-text.data-v-b2f01737, .browse-text.data-v-b2f01737 {
  color: inherit;
}

/* 收藏列表 */
.favorites-list.data-v-b2f01737 {
  flex: 1;
}
.list-header.data-v-b2f01737 {
  margin-bottom: 24rpx;
}
.list-title.data-v-b2f01737 {
  color: #4a5568;
  font-size: 26rpx;
  font-weight: 600;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
}
.levels-grid.data-v-b2f01737 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24rpx;
}
.level-card.data-v-b2f01737 {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 20rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(102, 126, 234, 0.15);
  transition: all 0.2s;
  position: relative;
}
.level-card.data-v-b2f01737:active {
  transform: scale(0.98);
  opacity: 0.9;
  border-color: rgba(102, 126, 234, 0.3);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.12);
}
.level-locked.data-v-b2f01737 {
  opacity: 0.6;
}
.level-number.data-v-b2f01737 {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 28rpx;
  font-weight: bold;
  flex-shrink: 0;
}
.level-info.data-v-b2f01737 {
  flex: 1;
}
.level-name.data-v-b2f01737 {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #1a202c;
  margin-bottom: 8rpx;
  letter-spacing: 0.5rpx;
}
.level-desc.data-v-b2f01737 {
  display: block;
  font-size: 24rpx;
  color: #4a5568;
  margin-bottom: 12rpx;
  font-weight: 400;
}

/* 关卡标签样式 */
.level-tags.data-v-b2f01737 {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}
.tag-item.data-v-b2f01737 {
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  background: #f0f0f0;
}
.tag-vip.data-v-b2f01737 {
  background: linear-gradient(135deg, #ffd700, #ffb347);
}
.tag-text.data-v-b2f01737 {
  font-size: 20rpx;
  color: #666;
}
.tag-vip .tag-text.data-v-b2f01737 {
  color: #8b4513;
  font-weight: 500;
}
.tag-vip-icon.data-v-b2f01737 {
  font-size: 16rpx;
}

/* 关卡状态样式 */
.level-status.data-v-b2f01737 {
  flex-shrink: 0;
}
.status-badge.data-v-b2f01737 {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  text-align: center;
}
.status-badge.locked.data-v-b2f01737 {
  background: #f0f0f0;
}
.status-badge.completed.data-v-b2f01737 {
  background: #d1f2eb;
}
.status-badge.available.data-v-b2f01737 {
  background: #e3f2fd;
}
.status-text.data-v-b2f01737 {
  font-size: 22rpx;
  font-weight: 500;
}

/* 星级显示样式 */
.level-stars.data-v-b2f01737 {
  display: flex;
  align-items: center;
  gap: 4rpx;
}
.star.data-v-b2f01737 {
  font-size: 20rpx;
  color: #ddd;
  transition: color 0.2s;
}
.star-filled.data-v-b2f01737 {
  color: #ffd700;
}

/* 取消收藏按钮 */
.unfavorite-btn.data-v-b2f01737 {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transition: all 0.2s;
}
.unfavorite-btn.data-v-b2f01737:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 1);
}
.unfavorite-icon.data-v-b2f01737 {
  font-size: 24rpx;
}

/* 底部按钮 */
.bottom-actions.data-v-b2f01737 {
  display: flex;
  justify-content: center;
  padding-top: 32rpx;
}
.back-btn.data-v-b2f01737 {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: #ffffff;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
  box-shadow: 0 8rpx 20rpx rgba(116, 185, 255, 0.3);
}
.back-btn.data-v-b2f01737:active {
  transform: scale(0.98);
  opacity: 0.9;
}
.back-text.data-v-b2f01737 {
  color: inherit;
}
