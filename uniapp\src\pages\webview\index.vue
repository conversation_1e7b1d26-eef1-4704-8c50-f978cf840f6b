<template>
  <view class="webview-container">
    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载...</text>
    </view>

    <!-- 错误状态 -->
    <view v-if="error" class="error-container">
      <view class="error-icon">⚠️</view>
      <text class="error-title">加载失败</text>
      <text class="error-message">{{ error }}</text>
      <button class="retry-btn" @click="retryLoad">重试</button>
      <button class="back-btn" @click="goBack">返回</button>
    </view>

    <!-- WebView -->
    <web-view 
      v-if="!isLoading && !error && url" 
      :src="url"
      @message="handleMessage"
      @load="handleLoad"
      @error="handleError"
    ></web-view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 页面参数
const url = ref('')
const title = ref('')
const isLoading = ref(true)
const error = ref('')

/**
 * 页面加载时获取参数
 */
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = (currentPage as any).options || {}
  
  console.log('WebView页面参数:', options)
  
  // 解析URL参数
  if (options.url) {
    url.value = decodeURIComponent(options.url)
  }
  
  if (options.title) {
    title.value = decodeURIComponent(options.title)
    // 设置导航栏标题
    uni.setNavigationBarTitle({
      title: title.value
    })
  }
  
  // 验证URL
  if (!url.value) {
    error.value = '缺少URL参数'
    isLoading.value = false
    return
  }
  
  // 验证URL格式
  if (!isValidUrl(url.value)) {
    error.value = '无效的URL格式'
    isLoading.value = false
    return
  }
  
  console.log('准备加载URL:', url.value)
})

/**
 * 验证URL格式
 */
const isValidUrl = (urlString: string): boolean => {
  try {
    const urlObj = new URL(urlString)
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:'
  } catch {
    return false
  }
}

/**
 * WebView加载完成
 */
const handleLoad = (event: any) => {
  console.log('WebView加载完成:', event)
  isLoading.value = false
  error.value = ''
}

/**
 * WebView加载错误
 */
const handleError = (event: any) => {
  console.error('WebView加载错误:', event)
  isLoading.value = false
  error.value = '页面加载失败，请检查网络连接'
}

/**
 * 处理WebView消息
 */
const handleMessage = (event: any) => {
  console.log('收到WebView消息:', event)
  // 可以在这里处理来自WebView的消息
}

/**
 * 重试加载
 */
const retryLoad = () => {
  if (!url.value) {
    error.value = '缺少URL参数'
    return
  }
  
  isLoading.value = true
  error.value = ''
  
  // 重新加载页面
  setTimeout(() => {
    if (isLoading.value) {
      // 如果超时仍在加载，显示错误
      isLoading.value = false
      error.value = '加载超时，请重试'
    }
  }, 10000) // 10秒超时
}

/**
 * 返回上一页
 */
const goBack = () => {
  uni.navigateBack({
    delta: 1
  })
}
</script>

<style lang="scss" scoped>
.webview-container {
  width: 100%;
  height: 100vh;
  background: #f5f5f5;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #ffffff;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e5e5;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 32rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 40rpx;
  background: #ffffff;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.error-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
}

.error-message {
  font-size: 24rpx;
  color: #666666;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.retry-btn, .back-btn {
  width: 200rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  margin: 8rpx;
  border: none;
  transition: all 0.2s;
}

.retry-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #ffffff;
}

.retry-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.back-btn {
  background: #f8f9fa;
  color: #666666;
  border: 2rpx solid #e9ecef;
}

.back-btn:active {
  background: #e9ecef;
  transform: scale(0.95);
}
</style>
