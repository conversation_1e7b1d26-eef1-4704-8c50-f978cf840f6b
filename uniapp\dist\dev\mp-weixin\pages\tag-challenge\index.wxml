<view class="tag-challenge-container data-v-ddedaf0e"><view class="page-header data-v-ddedaf0e"><view class="header-content data-v-ddedaf0e"><text class="page-title data-v-ddedaf0e">🏷️ 标签闯关</text><text class="page-subtitle data-v-ddedaf0e">选择标签分类，挑战相关主题的关卡</text></view><view wx:if="{{a}}" class="header-stats data-v-ddedaf0e"><text class="stats-item data-v-ddedaf0e">总完成: {{b}}关</text><text class="stats-item data-v-ddedaf0e">总星级: {{c}}⭐</text></view></view><view wx:if="{{d}}" class="loading-container data-v-ddedaf0e"><view class="loading-spinner data-v-ddedaf0e"></view><text class="loading-text data-v-ddedaf0e">正在加载标签...</text></view><view wx:elif="{{e}}" class="error-container data-v-ddedaf0e"><view class="error-icon data-v-ddedaf0e">⚠️</view><text class="error-title data-v-ddedaf0e">加载失败</text><text class="error-text data-v-ddedaf0e">{{f}}</text><button class="retry-btn data-v-ddedaf0e" bindtap="{{g}}"><text class="retry-text data-v-ddedaf0e">重试</text></button></view><view wx:else class="tags-container data-v-ddedaf0e"><view class="tags-grid data-v-ddedaf0e"><view wx:for="{{h}}" wx:for-item="tag" wx:key="h" class="{{['tag-card', 'data-v-ddedaf0e', tag.i && 'tag-vip', tag.j && 'tag-active', tag.k && 'tag-locked']}}" style="{{'border-left-color:' + tag.l}}" bindtap="{{tag.m}}"><view class="tag-info data-v-ddedaf0e"><view class="tag-header data-v-ddedaf0e"><text class="tag-name data-v-ddedaf0e">{{tag.a}}</text><view class="tag-badges data-v-ddedaf0e"><view wx:if="{{tag.b}}" class="vip-badge data-v-ddedaf0e"><text class="vip-text data-v-ddedaf0e">VIP</text></view><view wx:if="{{tag.c}}" class="lock-badge data-v-ddedaf0e"><text class="lock-text data-v-ddedaf0e">🔒</text></view></view></view><text class="tag-desc data-v-ddedaf0e">{{tag.d}}</text><view class="level-count data-v-ddedaf0e"><text class="count-text data-v-ddedaf0e">{{tag.e}} 个关卡</text></view></view><view class="tag-status data-v-ddedaf0e"><view class="{{['status-dot', 'data-v-ddedaf0e', tag.f && 'active']}}"></view><text class="status-text data-v-ddedaf0e">{{tag.g}}</text></view></view></view></view><view class="bottom-actions data-v-ddedaf0e"><button class="back-btn data-v-ddedaf0e" bindtap="{{i}}"><text class="back-text data-v-ddedaf0e">返回首页</text></button></view></view>