"use strict";
const common_vendor = require("../common/vendor.js");
const utils_env = require("./env.js");
function shouldShowDebugFeatures() {
  const currentEnv = utils_env.getCurrentEnvironment();
  const isDebugEnabled = currentEnv === "development";
  try {
    const accountInfo = common_vendor.index.getAccountInfoSync();
    const envVersion = accountInfo.miniProgram.envVersion;
    const isInDevTool = envVersion === "develop";
    return isDebugEnabled && isInDevTool;
  } catch (error) {
    if (isDebugEnabled) {
      console.warn("获取微信小程序环境信息失败:", error);
    }
    return false;
  }
  return isDebugEnabled;
}
function shouldShowDebugLogs() {
  return shouldShowDebugFeatures();
}
function shouldShowDebugButtons() {
  return shouldShowDebugFeatures();
}
function shouldShowDebugPages() {
  return shouldShowDebugFeatures();
}
function debugLog(message, ...args) {
  if (shouldShowDebugLogs()) {
    console.log(`[DEBUG] ${message}`, ...args);
  }
}
function debugError(message, ...args) {
  if (shouldShowDebugLogs()) {
    console.error(`[DEBUG ERROR] ${message}`, ...args);
  }
}
function getDebugEnvironmentInfo() {
  const currentEnv = utils_env.getCurrentEnvironment();
  const shouldShow = shouldShowDebugFeatures();
  let platform = "unknown";
  let details = {};
  platform = "mp-weixin";
  try {
    const accountInfo = common_vendor.index.getAccountInfoSync();
    details = {
      envVersion: accountInfo.miniProgram.envVersion,
      version: accountInfo.miniProgram.version
    };
  } catch (error) {
    details = { error: "Failed to get account info" };
  }
  return {
    environment: currentEnv,
    shouldShowDebug: shouldShow,
    platform,
    details
  };
}
exports.debugError = debugError;
exports.debugLog = debugLog;
exports.getDebugEnvironmentInfo = getDebugEnvironmentInfo;
exports.shouldShowDebugButtons = shouldShowDebugButtons;
exports.shouldShowDebugPages = shouldShowDebugPages;
