/**
 * 构建环境设置脚本
 * 在构建时替换环境配置文件
 */

const fs = require('fs')
const path = require('path')

// 获取构建环境
const buildEnv = process.env.NODE_ENV || 'development'

console.log(`🔧 设置构建环境: ${buildEnv}`)

// 配置文件路径
const buildEnvPath = path.join(__dirname, '../src/config/build-env.ts')
const buildEnvProdPath = path.join(__dirname, '../src/config/build-env.prod.ts')
const buildEnvStagingPath = path.join(__dirname, '../src/config/build-env.staging.ts')
const buildEnvDevPath = path.join(__dirname, '../src/config/build-env.dev.ts')

try {
  if (buildEnv === 'production') {
    // 生产环境：使用生产配置替换
    if (fs.existsSync(buildEnvProdPath)) {
      const prodContent = fs.readFileSync(buildEnvProdPath, 'utf8')
      fs.writeFileSync(buildEnvPath, prodContent)
      console.log('✅ 已切换到生产环境配置')
    } else {
      console.warn('⚠️  生产环境配置文件不存在，使用默认配置')
    }
  } else if (buildEnv === 'staging') {
    // 预发环境：使用预发配置替换
    if (fs.existsSync(buildEnvStagingPath)) {
      const stagingContent = fs.readFileSync(buildEnvStagingPath, 'utf8')
      fs.writeFileSync(buildEnvPath, stagingContent)
      console.log('✅ 已切换到预发环境配置')
    } else {
      console.warn('⚠️  预发环境配置文件不存在，使用默认配置')
    }
  } else {
    // 开发环境：恢复开发配置
    if (fs.existsSync(buildEnvDevPath)) {
      const devContent = fs.readFileSync(buildEnvDevPath, 'utf8')
      fs.writeFileSync(buildEnvPath, devContent)
      console.log('✅ 已切换到开发环境配置')
    } else {
      // 如果开发配置文件不存在，创建默认的开发配置
      const defaultDevContent = `/**
 * 构建时环境配置
 * 这个文件会在构建时被替换或修改
 */

// 这个值会在构建脚本中被替换
export const BUILD_ENVIRONMENT: 'development' | 'staging' | 'production' = 'development'

/**
 * 检查是否为生产构建
 */
export function isProductionBuild(): boolean {
  return BUILD_ENVIRONMENT === 'production'
}

/**
 * 检查是否为开发构建
 */
export function isDevelopmentBuild(): boolean {
  return BUILD_ENVIRONMENT === 'development'
}
`
      fs.writeFileSync(buildEnvPath, defaultDevContent)
      console.log('✅ 已创建并切换到开发环境配置')
    }
  }
} catch (error) {
  console.error('❌ 环境配置设置失败:', error.message)
  process.exit(1)
}
