"use strict";
const common_vendor = require("../common/vendor.js");
function createLoadingState() {
  return common_vendor.reactive({
    isLoading: false,
    error: null
  });
}
async function withLoading(loadingState, asyncFn, options) {
  const { showToast = true, toastDuration = 2e3, errorMessage } = options || {};
  try {
    loadingState.isLoading = true;
    loadingState.error = null;
    const result = await asyncFn();
    return result;
  } catch (error) {
    const apiError = error;
    const message = errorMessage || apiError.message || "操作失败";
    loadingState.error = message;
    if (showToast) {
      common_vendor.index.showToast({
        title: message,
        icon: "none",
        duration: toastDuration
      });
    }
    console.error("异步操作失败:", error);
    return null;
  } finally {
    loadingState.isLoading = false;
  }
}
function showSuccess(title, duration = 1500) {
  common_vendor.index.showToast({
    title,
    icon: "success",
    duration
  });
}
function showError(title, duration = 2e3) {
  common_vendor.index.showToast({
    title,
    icon: "none",
    duration
  });
}
function buildQueryString(params) {
  const queryParts = [];
  for (const [key, value] of Object.entries(params)) {
    if (value !== void 0 && value !== null && value !== "") {
      queryParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`);
    }
  }
  return queryParts.join("&");
}
function appendQueryToUrl(baseUrl, params) {
  const queryString = buildQueryString(params);
  if (!queryString) {
    return baseUrl;
  }
  const separator = baseUrl.includes("?") ? "&" : "?";
  return `${baseUrl}${separator}${queryString}`;
}
exports.appendQueryToUrl = appendQueryToUrl;
exports.createLoadingState = createLoadingState;
exports.showError = showError;
exports.showSuccess = showSuccess;
exports.withLoading = withLoading;
