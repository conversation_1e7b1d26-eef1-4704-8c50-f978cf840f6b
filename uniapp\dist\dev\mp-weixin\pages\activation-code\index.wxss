
.activation-code-container.data-v-9c6f8688 {
  min-height: 100vh;
  background: #fdf9f9;
  padding: 32rpx 24rpx;
}

/* 页面标题 */
.page-header.data-v-9c6f8688 {
  text-align: center;
  margin-bottom: 48rpx;
  padding: 32rpx 24rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.page-title.data-v-9c6f8688 {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.page-subtitle.data-v-9c6f8688 {
  display: block;
  font-size: 28rpx;
  color: #4a5568;
  font-weight: 500;
}

/* 输入区域 */
.input-section.data-v-9c6f8688 {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(102, 126, 234, 0.15);
}
.input-container.data-v-9c6f8688 {
  margin-bottom: 32rpx;
}
.input-label.data-v-9c6f8688 {
  display: block;
  font-size: 28rpx;
  color: #1a202c;
  font-weight: 600;
  margin-bottom: 16rpx;
  letter-spacing: 0.5rpx;
}
.code-input.data-v-9c6f8688 {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  font-size: 32rpx;
  color: #1a202c;
  background: #ffffff;
  box-sizing: border-box;
  font-weight: 500;
  letter-spacing: 1rpx;
}
.code-input.data-v-9c6f8688:focus {
  border-color: #667eea;
  outline: none;
}
.redeem-btn.data-v-9c6f8688 {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}
.redeem-btn.data-v-9c6f8688:active {
  transform: scale(0.98);
}
.redeem-btn-disabled.data-v-9c6f8688 {
  opacity: 0.6;
  transform: none !important;
}
.redeem-btn-text.data-v-9c6f8688 {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 500;
}

/* 信息区域 */
.info-section.data-v-9c6f8688 {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-bottom: 32rpx;
}
.info-card.data-v-9c6f8688 {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(102, 126, 234, 0.1);
}
.info-title.data-v-9c6f8688 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #1a202c;
  margin-bottom: 20rpx;
  letter-spacing: 0.5rpx;
}
.privilege-list.data-v-9c6f8688, .instruction-list.data-v-9c6f8688 {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.privilege-item.data-v-9c6f8688, .instruction-item.data-v-9c6f8688 {
  font-size: 26rpx;
  color: #2d3748;
  line-height: 1.5;
  font-weight: 400;
}

/* 结果弹窗 */
.modal-overlay.data-v-9c6f8688 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.modal-content.data-v-9c6f8688 {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 48rpx;
  margin: 0 32rpx;
  max-width: 600rpx;
  text-align: center;
}
.result-icon.data-v-9c6f8688 {
  margin-bottom: 24rpx;
}
.icon.data-v-9c6f8688 {
  font-size: 80rpx;
}
.result-title.data-v-9c6f8688 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 16rpx;
}
.result-message.data-v-9c6f8688 {
  display: block;
  font-size: 28rpx;
  color: #4a5568;
  margin-bottom: 32rpx;
  line-height: 1.5;
}
.package-info.data-v-9c6f8688 {
  background: #f7fafc;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
}
.package-name.data-v-9c6f8688 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 8rpx;
}
.package-desc.data-v-9c6f8688 {
  display: block;
  font-size: 26rpx;
  color: #4a5568;
  margin-bottom: 8rpx;
}
.package-duration.data-v-9c6f8688 {
  display: block;
  font-size: 24rpx;
  color: #718096;
}
.modal-btn.data-v-9c6f8688 {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-btn-text.data-v-9c6f8688 {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 500;
}

/* 底部按钮 */
.bottom-actions.data-v-9c6f8688 {
  display: flex;
  justify-content: center;
  padding-top: 32rpx;
}
.back-btn.data-v-9c6f8688 {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: #ffffff;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
  box-shadow: 0 8rpx 20rpx rgba(116, 185, 255, 0.3);
}
.back-btn.data-v-9c6f8688:active {
  transform: scale(0.98);
  opacity: 0.9;
}
.back-text.data-v-9c6f8688 {
  color: inherit;
}
