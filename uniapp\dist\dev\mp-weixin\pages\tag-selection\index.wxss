
.tag-selection-container.data-v-774f4f28 {
  min-height: 100vh;
  background: #fdf9f9;
  padding: 32rpx 24rpx;
}

/* 页面标题 */
.page-header.data-v-774f4f28 {
  text-align: center;
  margin-bottom: 48rpx;
  padding: 32rpx 24rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.page-title.data-v-774f4f28 {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.page-subtitle.data-v-774f4f28 {
  display: block;
  font-size: 28rpx;
  color: #4a5568;
  font-weight: 500;
}

/* 加载和错误状态 */
.loading-container.data-v-774f4f28, .error-container.data-v-774f4f28, .empty-container.data-v-774f4f28 {
  text-align: center;
  padding: 80rpx 0;
}
.loading-spinner.data-v-774f4f28 {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #ffffff;
  border-radius: 50%;
  animation: spin-774f4f28 1s linear infinite;
  margin: 0 auto 24rpx;
}
@keyframes spin-774f4f28 {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.loading-text.data-v-774f4f28, .error-title.data-v-774f4f28, .error-text.data-v-774f4f28, .empty-title.data-v-774f4f28, .empty-text.data-v-774f4f28 {
  color: #111;
  font-size: 28rpx;
  margin-bottom: 16rpx;
}
.error-icon.data-v-774f4f28, .empty-icon.data-v-774f4f28 {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}
.retry-btn.data-v-774f4f28 {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: #ffffff;
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  border: none;
  font-size: 26rpx;
  box-shadow: 0 8rpx 20rpx rgba(116, 185, 255, 0.3);
}
.retry-text.data-v-774f4f28 {
  color: inherit;
}

/* 标签列表 */
.tags-list.data-v-774f4f28 {
  flex: 1;
}
.list-header.data-v-774f4f28 {
  margin-bottom: 24rpx;
}
.list-title.data-v-774f4f28 {
  color: #718096;
  font-size: 26rpx;
  font-weight: 500;
}
.tags-grid.data-v-774f4f28 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  padding: 0 8rpx;
}
.tag-card.data-v-774f4f28 {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 16rpx;
  padding: 20rpx 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(102, 126, 234, 0.1);
  transition: all 0.2s;
  position: relative;
}
.tag-card.data-v-774f4f28:active {
  transform: scale(0.98);
  opacity: 0.9;
}
.tag-vip.data-v-774f4f28 {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 179, 71, 0.1));
  border: 2rpx solid rgba(255, 215, 0, 0.3);
}
.tag-inactive.data-v-774f4f28 {
  opacity: 0.6;
}
.tag-icon.data-v-774f4f28 {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex-shrink: 0;
}
.icon-text.data-v-774f4f28 {
  font-size: 32rpx;
  color: #ffffff;
}
.vip-crown.data-v-774f4f28 {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  font-size: 20rpx;
  background: #ffd700;
  border-radius: 50%;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tag-info.data-v-774f4f28 {
  flex: 1;
}

/* 新的标签内容样式 */
.tag-content.data-v-774f4f28 {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
}
.tag-content .tag-name.data-v-774f4f28 {
  font-size: 24rpx;
  font-weight: 500;
  color: #1a202c;
  margin-bottom: 4rpx;
  line-height: 1.2;
}
.tag-count.data-v-774f4f28 {
  font-size: 20rpx;
  color: #667eea;
  font-weight: 400;
}

/* VIP标识 */
.vip-badge.data-v-774f4f28 {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background: linear-gradient(135deg, #ffd700, #ffb347);
  border-radius: 8rpx;
  padding: 2rpx 6rpx;
}
.vip-text.data-v-774f4f28 {
  font-size: 16rpx;
  color: #8b4513;
  font-weight: bold;
}

/* 保留原有样式以兼容 */
.tag-name.data-v-774f4f28 {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #1a202c;
  margin-bottom: 8rpx;
  letter-spacing: 0.5rpx;
}
.tag-desc.data-v-774f4f28 {
  display: block;
  font-size: 24rpx;
  color: #4a5568;
  margin-bottom: 12rpx;
  font-weight: 400;
}
.tag-stats.data-v-774f4f28 {
  display: flex;
  gap: 24rpx;
}
.stat-item.data-v-774f4f28 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-label.data-v-774f4f28 {
  font-size: 20rpx;
  color: #a0aec0;
  margin-bottom: 4rpx;
}
.stat-value.data-v-774f4f28 {
  font-size: 24rpx;
  font-weight: bold;
  color: #4a5568;
}

/* 标签状态样式 */
.tag-status.data-v-774f4f28 {
  flex-shrink: 0;
}
.status-badge.data-v-774f4f28 {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  text-align: center;
}
.status-badge.inactive.data-v-774f4f28 {
  background: #f0f0f0;
}
.status-badge.vip-only.data-v-774f4f28 {
  background: linear-gradient(135deg, #ffd700, #ffb347);
}
.status-badge.available.data-v-774f4f28 {
  background: #e3f2fd;
}
.status-text.data-v-774f4f28 {
  font-size: 22rpx;
  font-weight: 500;
}

/* 底部按钮 */
.bottom-actions.data-v-774f4f28 {
  display: flex;
  justify-content: center;
  padding-top: 32rpx;
}
.back-btn.data-v-774f4f28 {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: #ffffff;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
  box-shadow: 0 8rpx 20rpx rgba(116, 185, 255, 0.3);
}
.back-btn.data-v-774f4f28:active {
  transform: scale(0.98);
  opacity: 0.9;
}
.back-text.data-v-774f4f28 {
  color: inherit;
}
