<template>
  <view class="tag-challenge-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="header-content">
        <text class="page-title">🏷️ 标签闯关</text>
        <text class="page-subtitle">选择标签分类，挑战相关主题的关卡</text>
      </view>
      <view class="header-stats" v-if="userStats">
        <text class="stats-item">总完成: {{ userStats.completedLevels }}关</text>
        <text class="stats-item">总星级: {{ userStats.totalStars }}⭐</text>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载标签...</text>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="error" class="error-container">
      <view class="error-icon">⚠️</view>
      <text class="error-title">加载失败</text>
      <text class="error-text">{{ error }}</text>
      <button class="retry-btn" @click="loadTags">
        <text class="retry-text">重试</text>
      </button>
    </view>

    <!-- 标签网格 -->
    <view v-else class="tags-container">
      <view class="tags-grid">
        <view
          v-for="tag in tags"
          :key="tag.id"
          class="tag-card"
          :class="{
            'tag-vip': tag.isVip,
            'tag-active': tag.status === 'active',
            'tag-locked': tag.isVip && !isVip
          }"
          :style="{ borderLeftColor: tag.color || '#667eea' }"
          @click="selectTag(tag)"
        >
          <!-- 标签信息 -->
          <view class="tag-info">
            <view class="tag-header">
              <text class="tag-name">{{ tag.name }}</text>
              <view class="tag-badges">
                <view v-if="tag.isVip" class="vip-badge">
                  <text class="vip-text">VIP</text>
                </view>
                <view v-if="tag.isVip && !isVip" class="lock-badge">
                  <text class="lock-text">🔒</text>
                </view>
              </view>
            </view>
            <text class="tag-desc">{{ tag.description || '暂无描述' }}</text>

            <!-- 关卡数量 -->
            <view class="level-count">
              <text class="count-text">{{ tag.levelCount || 0 }} 个关卡</text>
            </view>
          </view>

          <!-- 标签状态 -->
          <view class="tag-status">
            <view class="status-dot" :class="{ 'active': tag.status === 'active' }"></view>
            <text class="status-text">{{ tag.status === 'active' ? '可用' : '维护中' }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部返回按钮 -->
    <view class="bottom-actions">
      <button class="back-btn" @click="goBack">
        <text class="back-text">返回首页</text>
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import weixinApi from '../../api/weixin'
import { showError, showSuccess } from '../../api/utils'
import { audioManager } from '../../utils/audio'
import type { LevelTag, UserStarStats, DailyStatusResponse } from '../../api/types'

// 响应式数据
const isLoading = ref(true)
const error = ref('')
const tags = ref<LevelTag[]>([])
const userStats = ref<UserStarStats | null>(null)
const dailyStatus = ref<DailyStatusResponse | null>(null)
const isVip = ref(false)

// 计算属性
const activeTagsCount = computed(() => {
  return tags.value.filter(tag => tag.status === 'active').length
})

const vipTagsCount = computed(() => {
  return tags.value.filter(tag => tag.isVip).length
})

/**
 * 加载标签列表
 */
const loadTags = async () => {
  try {
    isLoading.value = true
    error.value = ''

    console.log('�️ 开始加载标签列表...')

    // 并行获取数据
    const [tagsData, dailyStatusData] = await Promise.all([
      weixinApi.getTags(),
      weixinApi.getDailyStatus()
    ])

    tags.value = tagsData
    dailyStatus.value = dailyStatusData
    isVip.value = dailyStatusData.isVip

    console.log('✅ 标签列表加载成功:', tagsData.length, '个标签')
    console.log('✅ VIP状态:', dailyStatusData.isVip ? 'VIP用户' : '普通用户')

    // 获取用户统计信息
    try {
      const stats = await weixinApi.getUserStarStats()
      userStats.value = stats
      console.log('✅ 用户统计信息加载成功:', stats)
    } catch (statsError) {
      console.warn('⚠️ 用户统计信息加载失败:', statsError)
      // 统计信息加载失败不影响主要功能
    }

  } catch (err) {
    console.error('❌ 数据加载失败:', err)
    error.value = err instanceof Error ? err.message : '加载数据失败'
  } finally {
    isLoading.value = false
  }
}

/**
 * 选择标签
 */
const selectTag = async (tag: LevelTag) => {
  try {
    // 播放点击音效
    audioManager.playSoundEffect('click')

    console.log('🎯 选择标签:', tag.name)

    // 检查标签状态
    if (tag.status !== 'active') {
      showError('该标签暂时不可用')
      return
    }

    // 检查VIP权限
    if (tag.isVip && !isVip.value) {
      console.log('🔒 需要VIP权限访问标签:', tag.name)
      showVipUpgradeModal()
      return
    }

    // 跳转到标签关卡页面
    uni.navigateTo({
      url: `/pages/tag-levels/index?tagId=${tag.id}&tagName=${encodeURIComponent(tag.name)}`
    })

  } catch (err) {
    console.error('❌ 选择标签失败:', err)
    showError('进入标签关卡失败')
  }
}

/**
 * 显示VIP升级提示
 */
const showVipUpgradeModal = () => {
  uni.showModal({
    title: 'VIP专享内容',
    content: '该标签为VIP专享内容，开通VIP会员即可畅玩所有标签关卡！',
    confirmText: '开通VIP',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        // 跳转到会员中心
        uni.navigateTo({
          url: '/pages/member-center/index'
        })
      }
    }
  })
}

/**
 * 返回首页
 */
const goBack = () => {
  // 播放点击音效
  audioManager.playSoundEffect('click')
  
  uni.navigateBack({
    delta: 1,
    fail: () => {
      // 如果无法返回，则跳转到首页
      uni.switchTab({
        url: '/pages/index/index'
      })
    }
  })
}

// 页面生命周期
onLoad((options) => {
  console.log('📱 标签闯关页面加载:', options)
})

onMounted(() => {
  loadTags()
})
</script>

<style scoped>
.tag-challenge-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20rpx;
}

/* 页面头部 */
.page-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
}

.header-content {
  margin-bottom: 16rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  display: block;
  margin-bottom: 8rpx;
}

.page-subtitle {
  font-size: 28rpx;
  color: #7f8c8d;
  line-height: 1.4;
}

.header-stats {
  display: flex;
  gap: 24rpx;
  padding-top: 16rpx;
  border-top: 2rpx solid #ecf0f1;
}

.stats-item {
  font-size: 24rpx;
  color: #667eea;
  font-weight: 500;
}

/* 加载和错误状态 */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #ecf0f1;
  border-top: 6rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text,
.error-title {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.error-text {
  font-size: 24rpx;
  color: #7f8c8d;
  margin-bottom: 32rpx;
  line-height: 1.4;
}

.retry-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #ffffff;
  border: none;
  border-radius: 44rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.retry-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 标签网格 */
.tags-container {
  flex: 1;
}

.tags-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding-bottom: 120rpx;
}

.tag-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  border-left: 8rpx solid #667eea;
  padding: 32rpx;
  position: relative;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
}

.tag-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.12);
}

.tag-card.tag-vip {
  background: linear-gradient(135deg, #fff8e1, #ffecb3);
  border-left-color: #ffc107;
}

.tag-card.tag-active {
  opacity: 1;
}

.tag-card:not(.tag-active) {
  opacity: 0.6;
  background: rgba(200, 200, 200, 0.3);
}

.tag-card.tag-locked {
  opacity: 0.7;
  position: relative;
}

.tag-card.tag-locked::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 20rpx;
}

.tag-info {
  flex: 1;
}

.tag-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.tag-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  flex: 1;
}

.tag-badges {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.vip-badge {
  background: linear-gradient(135deg, #ffc107, #ff8f00);
  color: #ffffff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  font-weight: bold;
}

.vip-text {
  font-size: 18rpx;
}

.lock-badge {
  background: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  font-size: 16rpx;
}

.lock-text {
  font-size: 16rpx;
}

.tag-desc {
  font-size: 22rpx;
  color: #7f8c8d;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.level-count {
  margin-bottom: 16rpx;
}

.count-text {
  font-size: 20rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

/* 标签状态 */
.tag-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #bdc3c7;
  transition: background 0.3s ease;
}

.status-dot.active {
  background: #27ae60;
}

.status-text {
  font-size: 20rpx;
  color: #7f8c8d;
}

/* 底部按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  padding: 24rpx;
  border-top: 2rpx solid #ecf0f1;
  backdrop-filter: blur(10rpx);
}

.back-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #95a5a6, #7f8c8d);
  color: #ffffff;
  border: none;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}
</style>
