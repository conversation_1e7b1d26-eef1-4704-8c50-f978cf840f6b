"use strict";
const common_vendor = require("../../common/vendor.js");
const api_weixin = require("../../api/weixin.js");
const utils_audio = require("../../utils/audio.js");
const utils_auth = require("../../utils/auth.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const tagId = common_vendor.ref("");
    const isLoading = common_vendor.ref(false);
    const error = common_vendor.ref(null);
    const tagInfo = common_vendor.ref(null);
    const levels = common_vendor.ref([]);
    const completedCount = common_vendor.computed(() => {
      return levels.value.filter((level) => level.isCompleted).length;
    });
    const formattedLevels = common_vendor.computed(() => {
      return levels.value.map((level, index) => ({
        ...level,
        levelNumber: String(index + 1).padStart(2, "0"),
        locked: !level.isUnlocked,
        completed: level.isCompleted,
        isFavorited: level.isFavorited || false
      }));
    });
    common_vendor.onLoad(async (options) => {
      const isLoggedIn = await utils_auth.checkLoginAndRedirect({
        toastMessage: "请先登录以查看标签关卡",
        redirectUrl: "/pages/login/index"
      });
      if (!isLoggedIn) {
        return;
      }
      if (options == null ? void 0 : options.tagId) {
        tagId.value = options.tagId;
        await loadTagLevels();
      } else {
        error.value = "缺少标签ID参数";
      }
    });
    const loadTagLevels = async () => {
      if (!tagId.value) {
        error.value = "缺少标签ID参数";
        return;
      }
      try {
        isLoading.value = true;
        error.value = null;
        const tags = await api_weixin.weixinApi.getActiveTags();
        const currentTag = tags.find((tag) => tag.id === tagId.value);
        if (currentTag) {
          tagInfo.value = currentTag;
        }
        const response = await api_weixin.weixinApi.getTagLevels(tagId.value);
        levels.value = response.levels;
        console.log("标签关卡列表加载成功:", response);
      } catch (err) {
        console.error("加载标签关卡列表失败:", err);
        error.value = "加载失败，请重试";
      } finally {
        isLoading.value = false;
      }
    };
    const selectLevel = async (level) => {
      if (level.locked) {
        utils_audio.audioManager.playSoundEffect("fail");
        common_vendor.index.showToast({
          title: "该关卡尚未解锁",
          icon: "none",
          duration: 1500
        });
        return;
      }
      utils_audio.audioManager.playSoundEffect("click");
      console.log("Selected level:", level);
      common_vendor.index.setStorageSync("selectedLevel", JSON.stringify(level));
      common_vendor.index.navigateTo({
        url: "/pages/game/index"
      });
    };
    const toggleFavorite = async (level) => {
      try {
        utils_audio.audioManager.playSoundEffect("click");
        if (level.isFavorited) {
          await api_weixin.weixinApi.removeFavorite(level.id);
          level.isFavorited = false;
          common_vendor.index.showToast({
            title: "已取消收藏",
            icon: "success",
            duration: 1500
          });
        } else {
          await api_weixin.weixinApi.addFavorite(level.id);
          level.isFavorited = true;
          common_vendor.index.showToast({
            title: "已添加收藏",
            icon: "success",
            duration: 1500
          });
        }
        utils_audio.audioManager.playSoundEffect("complete");
      } catch (error2) {
        console.error("收藏操作失败:", error2);
        utils_audio.audioManager.playSoundEffect("fail");
        common_vendor.index.showToast({
          title: "操作失败，请重试",
          icon: "none",
          duration: 1500
        });
      }
    };
    return (_ctx, _cache) => {
      var _a, _b;
      return common_vendor.e({
        a: common_vendor.t(((_a = tagInfo.value) == null ? void 0 : _a.name) || "标签关卡"),
        b: common_vendor.t(((_b = tagInfo.value) == null ? void 0 : _b.description) || "挑战该标签下的所有关卡"),
        c: common_vendor.t(levels.value.length),
        d: common_vendor.t(completedCount.value),
        e: isLoading.value
      }, isLoading.value ? {} : error.value ? {
        g: common_vendor.t(error.value),
        h: common_vendor.o(loadTagLevels)
      } : {
        i: common_vendor.f(formattedLevels.value, (level, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(level.levelNumber),
            b: common_vendor.t(level.name),
            c: common_vendor.t(level.description),
            d: level.locked
          }, level.locked ? {} : level.completed ? {
            f: common_vendor.f(level.userStars || 0, (star, k1, i1) => {
              return {
                a: star
              };
            })
          } : {}, {
            e: level.completed,
            g: common_vendor.t(level.isFavorited ? "💖" : "🤍"),
            h: level.isFavorited ? 1 : "",
            i: common_vendor.o(($event) => toggleFavorite(level), level.id),
            j: level.id,
            k: level.locked ? 1 : "",
            l: level.completed ? 1 : "",
            m: common_vendor.o(($event) => selectLevel(level), level.id)
          });
        })
      }, {
        f: error.value
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-2ed8a332"]]);
wx.createPage(MiniProgramPage);
