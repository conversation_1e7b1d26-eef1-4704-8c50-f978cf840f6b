/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.webview-container.data-v-f397c225 {
  width: 100%;
  height: 100vh;
  background: #f5f5f5;
}
.loading-container.data-v-f397c225 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #ffffff;
}
.loading-spinner.data-v-f397c225 {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e5e5;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin-f397c225 1s linear infinite;
  margin-bottom: 32rpx;
}
@keyframes spin-f397c225 {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.loading-text.data-v-f397c225 {
  font-size: 28rpx;
  color: #666666;
}
.error-container.data-v-f397c225 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 40rpx;
  background: #ffffff;
}
.error-icon.data-v-f397c225 {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}
.error-title.data-v-f397c225 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
}
.error-message.data-v-f397c225 {
  font-size: 24rpx;
  color: #666666;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 40rpx;
}
.retry-btn.data-v-f397c225, .back-btn.data-v-f397c225 {
  width: 200rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  margin: 8rpx;
  border: none;
  transition: all 0.2s;
}
.retry-btn.data-v-f397c225 {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #ffffff;
}
.retry-btn.data-v-f397c225:active {
  transform: scale(0.95);
  opacity: 0.8;
}
.back-btn.data-v-f397c225 {
  background: #f8f9fa;
  color: #666666;
  border: 2rpx solid #e9ecef;
}
.back-btn.data-v-f397c225:active {
  background: #e9ecef;
  transform: scale(0.95);
}