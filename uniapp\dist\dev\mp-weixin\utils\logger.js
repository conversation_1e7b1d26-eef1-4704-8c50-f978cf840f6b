"use strict";
const utils_env = require("./env.js");
function shouldLog() {
  return utils_env.getCurrentEnvironment() === "development";
}
const logger = {
  /**
   * 普通日志
   */
  log: (...args) => {
    if (shouldLog()) {
      console.log(...args);
    }
  },
  /**
   * 信息日志
   */
  info: (...args) => {
    if (shouldLog()) {
      console.info(...args);
    }
  },
  /**
   * 警告日志
   */
  warn: (...args) => {
    if (shouldLog()) {
      console.warn(...args);
    }
  },
  /**
   * 错误日志（总是输出，但在生产环境下简化）
   */
  error: (...args) => {
    if (shouldLog()) {
      console.error(...args);
    } else {
      console.error("An error occurred");
    }
  },
  /**
   * 调试日志
   */
  debug: (...args) => {
    if (shouldLog()) {
      console.debug("[DEBUG]", ...args);
    }
  },
  /**
   * 性能测试
   */
  time: (label) => {
    if (shouldLog()) {
      console.time(label);
    }
  },
  /**
   * 结束性能测试
   */
  timeEnd: (label) => {
    if (shouldLog()) {
      console.timeEnd(label);
    }
  },
  /**
   * 分组开始
   */
  group: (label) => {
    if (shouldLog()) {
      console.group(label);
    }
  },
  /**
   * 分组结束
   */
  groupEnd: () => {
    if (shouldLog()) {
      console.groupEnd();
    }
  },
  /**
   * 表格输出
   */
  table: (data) => {
    if (shouldLog()) {
      console.table(data);
    }
  },
  /**
   * 断言
   */
  assert: (condition, ...args) => {
    if (shouldLog()) {
      console.assert(condition, ...args);
    }
  }
};
function createLogger(prefix) {
  return {
    log: (...args) => logger.log(`[${prefix}]`, ...args),
    info: (...args) => logger.info(`[${prefix}]`, ...args),
    warn: (...args) => logger.warn(`[${prefix}]`, ...args),
    error: (...args) => logger.error(`[${prefix}]`, ...args),
    debug: (...args) => logger.debug(`[${prefix}]`, ...args),
    time: (label) => logger.time(`[${prefix}] ${label}`),
    timeEnd: (label) => logger.timeEnd(`[${prefix}] ${label}`),
    group: (label) => logger.group(label ? `[${prefix}] ${label}` : `[${prefix}]`),
    groupEnd: () => logger.groupEnd(),
    table: (data) => logger.table(data),
    assert: (condition, ...args) => logger.assert(condition, `[${prefix}]`, ...args)
  };
}
const pageLogger = {
  login: createLogger("登录")
};
exports.pageLogger = pageLogger;
