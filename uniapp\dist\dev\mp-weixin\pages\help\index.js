"use strict";
const common_vendor = require("../../common/vendor.js");
const composables_useGlobalConfig = require("../../composables/useGlobalConfig.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const helpUrl = common_vendor.ref("");
    const isLoading = common_vendor.ref(true);
    const {
      helpPageConfig,
      fetchGlobalConfig
    } = composables_useGlobalConfig.useGlobalConfig();
    common_vendor.onMounted(async () => {
      await loadHelpContent();
    });
    const loadHelpContent = async () => {
      isLoading.value = true;
      try {
        await fetchGlobalConfig();
        const helpPageUrl = helpPageConfig.value;
        console.log(helpPageUrl, "helpConfig.url");
        helpUrl.value = helpPageUrl;
        isLoading.value = false;
      } catch (err) {
        console.error("加载帮助内容失败:", err);
        isLoading.value = false;
      }
    };
    return (_ctx, _cache) => {
      return {
        a: helpUrl.value
      };
    };
  }
});
wx.createPage(_sfc_main);
