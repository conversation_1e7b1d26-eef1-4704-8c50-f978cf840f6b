<view class="test-audio-container data-v-0a20da84"><view class="header data-v-0a20da84"><text class="title data-v-0a20da84">音频功能测试</text><text class="subtitle data-v-0a20da84">测试新的 InnerAudioContext API</text></view><view class="status-card data-v-0a20da84"><text class="status-title data-v-0a20da84">播放状态</text><view class="status-info data-v-0a20da84"><text class="status-item data-v-0a20da84">正在播放: {{a}}</text><text class="status-item data-v-0a20da84">已暂停: {{b}}</text><text class="status-item data-v-0a20da84">音乐类型: {{c}}</text><text class="status-item data-v-0a20da84">音频源: {{d}}</text></view></view><view class="control-section data-v-0a20da84"><text class="section-title data-v-0a20da84">🎵 背景音乐控制</text><view class="button-grid data-v-0a20da84"><button class="control-btn primary data-v-0a20da84" bindtap="{{e}}">播放主页音乐</button><button class="control-btn primary data-v-0a20da84" bindtap="{{f}}">播放游戏音乐</button><button class="control-btn primary data-v-0a20da84" bindtap="{{g}}">播放菜单音乐</button><button class="control-btn secondary data-v-0a20da84" bindtap="{{h}}">暂停音乐</button><button class="control-btn secondary data-v-0a20da84" bindtap="{{i}}">恢复音乐</button><button class="control-btn danger data-v-0a20da84" bindtap="{{j}}">停止音乐</button></view></view><view class="control-section data-v-0a20da84"><text class="section-title data-v-0a20da84">🔊 音效测试</text><view class="button-grid data-v-0a20da84"><button class="control-btn effect data-v-0a20da84" bindtap="{{k}}">点击音效</button><button class="control-btn effect data-v-0a20da84" bindtap="{{l}}">成功音效</button><button class="control-btn effect data-v-0a20da84" bindtap="{{m}}">失败音效</button><button class="control-btn effect data-v-0a20da84" bindtap="{{n}}">解锁音效</button><button class="control-btn effect data-v-0a20da84" bindtap="{{o}}">完成音效</button></view></view><view class="control-section data-v-0a20da84"><text class="section-title data-v-0a20da84">📳 震动测试</text><view class="button-grid data-v-0a20da84"><button class="control-btn vibrate data-v-0a20da84" bindtap="{{p}}">短震动</button><button class="control-btn vibrate data-v-0a20da84" bindtap="{{q}}">长震动</button></view></view><view class="control-section data-v-0a20da84"><text class="section-title data-v-0a20da84">⚙️ 设置控制</text><view class="settings-list data-v-0a20da84"><view class="setting-item data-v-0a20da84"><text class="setting-label data-v-0a20da84">背景音乐</text><switch class="data-v-0a20da84" checked="{{r}}" bindchange="{{s}}" color="#667eea"/></view><view class="setting-item data-v-0a20da84"><text class="setting-label data-v-0a20da84">音效</text><switch class="data-v-0a20da84" checked="{{t}}" bindchange="{{v}}" color="#667eea"/></view><view class="setting-item data-v-0a20da84"><text class="setting-label data-v-0a20da84">震动</text><switch class="data-v-0a20da84" checked="{{w}}" bindchange="{{x}}" color="#667eea"/></view></view></view><view class="control-section data-v-0a20da84"><text class="section-title data-v-0a20da84">🌐 全局配置测试</text><view class="config-info data-v-0a20da84"><text class="config-item data-v-0a20da84">主页音乐URL: {{y}}</text><text class="config-item data-v-0a20da84">游戏音乐URL: {{z}}</text><text class="config-item data-v-0a20da84">菜单音乐URL: {{A}}</text></view><view class="button-grid data-v-0a20da84"><button class="control-btn config data-v-0a20da84" bindtap="{{B}}">测试全局配置音乐</button><button class="control-btn config data-v-0a20da84" bindtap="{{C}}">刷新配置</button></view></view><view class="back-btn-container data-v-0a20da84"><button class="back-btn data-v-0a20da84" bindtap="{{D}}"><text class="back-text data-v-0a20da84">返回</text></button></view></view>