"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
const common_vendor = require("../common/vendor.js");
let innerAudioContext = null;
let currentAudioSrc = "";
let isPlaying = false;
let isPaused = false;
let currentMusicType = "";
const DEFAULT_BACKGROUND_MUSIC = {
  main: "/static/audio/background-main.mp3",
  // 主背景音乐
  game: "/static/audio/background-game.mp3",
  // 游戏背景音乐
  menu: "/static/audio/background-menu.mp3"
  // 菜单背景音乐
};
const SOUND_EFFECTS = {
  click: "/static/audio/click.mp3",
  // 点击音效
  success: "/static/audio/success.mp3",
  // 成功音效
  fail: "/static/audio/fail.mp3",
  // 失败音效
  unlock: "/static/audio/unlock.mp3",
  // 解锁音效
  complete: "/static/audio/complete.mp3"
  // 完成音效
};
const AUDIO_ERROR_CODES = {
  "10001": "系统错误",
  "10002": "网络错误",
  "10003": "文件错误",
  "10004": "格式错误",
  "-1": "未知错误",
  "-99": "音频实例冲突错误"
};
const soundEffectInstances = /* @__PURE__ */ new Map();
const _AudioManager = class _AudioManager {
  constructor() {
    __publicField(this, "settings", {
      backgroundMusic: true,
      soundEffects: true,
      vibration: true
    });
    this.loadSettings();
  }
  /**
   * 获取单例实例
   */
  static getInstance() {
    if (!_AudioManager.instance) {
      _AudioManager.instance = new _AudioManager();
    }
    return _AudioManager.instance;
  }
  /**
   * 加载设置
   */
  loadSettings() {
    try {
      const savedSettings = common_vendor.index.getStorageSync("gameSettings");
      if (savedSettings) {
        this.settings = { ...this.settings, ...JSON.parse(savedSettings) };
      }
      console.log("音频设置加载成功:", this.settings);
    } catch (error) {
      console.error("加载音频设置失败:", error);
    }
  }
  /**
   * 保存设置
   */
  saveSettings() {
    try {
      common_vendor.index.setStorageSync("gameSettings", JSON.stringify(this.settings));
      console.log("音频设置保存成功:", this.settings);
    } catch (error) {
      console.error("保存音频设置失败:", error);
    }
  }
  /**
   * 获取当前设置
   */
  getSettings() {
    return { ...this.settings };
  }
  /**
   * 更新设置
   */
  updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings };
    this.saveSettings();
    if (!this.settings.backgroundMusic && isPlaying) {
      this.stopBackgroundMusic();
    }
    console.log("音频设置已更新:", this.settings);
  }
  /**
   * 创建内部音频上下文
   */
  createInnerAudioContext(src) {
    if (innerAudioContext && currentAudioSrc === src) {
      return innerAudioContext;
    }
    this.destroyInnerAudioContext();
    innerAudioContext = common_vendor.index.createInnerAudioContext();
    currentAudioSrc = src;
    innerAudioContext.src = src;
    innerAudioContext.loop = true;
    innerAudioContext.autoplay = false;
    innerAudioContext.volume = 0.5;
    innerAudioContext.obeyMuteSwitch = true;
    this.setupAudioEventListeners(innerAudioContext);
    console.log("创建内部音频上下文:", src);
    return innerAudioContext;
  }
  /**
   * 设置音频事件监听器
   */
  setupAudioEventListeners(audioContext) {
    audioContext.onCanplay(() => {
      console.log("音频可以播放:", currentAudioSrc);
    });
    audioContext.onPlay(() => {
      isPlaying = true;
      isPaused = false;
      console.log("背景音乐开始播放:", currentAudioSrc);
    });
    audioContext.onPause(() => {
      isPaused = true;
      console.log("背景音乐暂停:", currentAudioSrc);
    });
    audioContext.onStop(() => {
      isPlaying = false;
      isPaused = false;
      console.log("背景音乐停止:", currentAudioSrc);
    });
    audioContext.onEnded(() => {
      isPlaying = false;
      isPaused = false;
      console.log("背景音乐播放结束:", currentAudioSrc);
    });
    audioContext.onError((error) => {
      const errorCode = error.errCode || error.code || "unknown";
      const errorMsg = AUDIO_ERROR_CODES[errorCode] || "未知错误";
      console.error("背景音乐播放错误:", {
        code: errorCode,
        message: errorMsg,
        src: currentAudioSrc,
        error
      });
      isPlaying = false;
      isPaused = false;
      if (errorCode === -99) {
        console.warn("检测到音频实例冲突，尝试重新创建");
        setTimeout(() => {
          this.destroyInnerAudioContext();
        }, 100);
      }
    });
    audioContext.onWaiting(() => {
      console.log("音频加载中:", currentAudioSrc);
    });
    audioContext.onTimeUpdate(() => {
    });
  }
  /**
   * 销毁内部音频上下文
   */
  destroyInnerAudioContext() {
    if (innerAudioContext) {
      try {
        innerAudioContext.stop();
        innerAudioContext.destroy();
        console.log("销毁音频上下文:", currentAudioSrc);
      } catch (error) {
        console.error("销毁音频上下文失败:", error);
      }
      innerAudioContext = null;
      currentAudioSrc = "";
      isPlaying = false;
      isPaused = false;
      currentMusicType = "";
    }
  }
  /**
   * 播放背景音乐
   */
  playBackgroundMusic(musicType = "main", customUrl) {
    if (!this.settings.backgroundMusic) {
      console.log("背景音乐已关闭，跳过播放");
      return;
    }
    try {
      const src = customUrl || DEFAULT_BACKGROUND_MUSIC[musicType] || DEFAULT_BACKGROUND_MUSIC.main;
      if (isPlaying && currentAudioSrc === src) {
        console.log("相同背景音乐正在播放中:", musicType);
        return;
      }
      if (isPaused && currentAudioSrc === src && innerAudioContext) {
        innerAudioContext.play();
        currentMusicType = musicType;
        return;
      }
      const context = this.createInnerAudioContext(src);
      context.play();
      currentMusicType = musicType;
      console.log("开始播放背景音乐:", musicType, src);
    } catch (error) {
      console.error("播放背景音乐失败:", error);
    }
  }
  /**
   * 暂停背景音乐
   */
  pauseBackgroundMusic() {
    if (innerAudioContext && isPlaying) {
      innerAudioContext.pause();
      console.log("背景音乐已暂停");
    }
  }
  /**
   * 恢复背景音乐
   */
  resumeBackgroundMusic() {
    if (innerAudioContext && isPaused && this.settings.backgroundMusic) {
      innerAudioContext.play();
      console.log("背景音乐已恢复");
    }
  }
  /**
   * 停止背景音乐
   */
  stopBackgroundMusic() {
    if (innerAudioContext) {
      innerAudioContext.stop();
      console.log("背景音乐已停止");
    }
  }
  /**
   * 播放音效
   */
  playSoundEffect(effectType) {
    if (!this.settings.soundEffects) {
      console.log("音效已关闭，跳过播放");
      return;
    }
    try {
      const src = SOUND_EFFECTS[effectType];
      let effectContext = soundEffectInstances.get(effectType);
      if (!effectContext) {
        effectContext = common_vendor.index.createInnerAudioContext();
        effectContext.src = src;
        effectContext.volume = 0.6;
        effectContext.loop = false;
        effectContext.obeyMuteSwitch = true;
        effectContext.onPlay(() => {
          console.log("音效播放:", effectType);
        });
        effectContext.onError((error) => {
          console.error("音效播放失败:", effectType, error);
          soundEffectInstances.delete(effectType);
        });
        effectContext.onEnded(() => {
          console.log("音效播放结束:", effectType);
        });
        soundEffectInstances.set(effectType, effectContext);
      }
      effectContext.play();
    } catch (error) {
      console.error("播放音效失败:", effectType, error);
    }
  }
  /**
   * 触发震动
   */
  vibrate(type = "short") {
    if (!this.settings.vibration) {
      console.log("震动已关闭，跳过震动");
      return;
    }
    try {
      if (type === "short") {
        common_vendor.index.vibrateShort({
          success: () => {
            console.log("短震动触发成功");
          },
          fail: (error) => {
            console.error("短震动触发失败:", error);
          }
        });
      } else {
        common_vendor.index.vibrateLong({
          success: () => {
            console.log("长震动触发成功");
          },
          fail: (error) => {
            console.error("长震动触发失败:", error);
          }
        });
      }
    } catch (error) {
      console.error("震动触发失败:", error);
    }
  }
  /**
   * 页面显示时恢复音频
   */
  onPageShow() {
    if (this.settings.backgroundMusic && isPaused) {
      this.resumeBackgroundMusic();
    }
  }
  /**
   * 页面隐藏时暂停音频
   */
  onPageHide() {
    if (isPlaying) {
      this.pauseBackgroundMusic();
    }
  }
  /**
   * 销毁音频资源
   */
  destroy() {
    this.destroyInnerAudioContext();
    soundEffectInstances.forEach((context, key) => {
      try {
        context.destroy();
      } catch (error) {
        console.error("销毁音效实例失败:", key, error);
      }
    });
    soundEffectInstances.clear();
    console.log("音频资源已销毁");
  }
  /**
   * 获取播放状态
   */
  getPlayStatus() {
    return {
      isPlaying,
      isPaused,
      currentSrc: currentAudioSrc,
      musicType: currentMusicType
    };
  }
};
__publicField(_AudioManager, "instance");
let AudioManager = _AudioManager;
const audioManager = AudioManager.getInstance();
exports.audioManager = audioManager;
