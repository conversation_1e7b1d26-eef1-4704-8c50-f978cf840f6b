"use strict";
const common_vendor = require("../../common/vendor.js");
const api_weixin = require("../../api/weixin.js");
const api_utils = require("../../api/utils.js");
const utils_audio = require("../../utils/audio.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const isLoading = common_vendor.ref(true);
    const error = common_vendor.ref("");
    const tags = common_vendor.ref([]);
    const userStats = common_vendor.ref(null);
    const dailyStatus = common_vendor.ref(null);
    const isVip = common_vendor.ref(false);
    common_vendor.computed(() => {
      return tags.value.filter((tag) => tag.status === "active").length;
    });
    common_vendor.computed(() => {
      return tags.value.filter((tag) => tag.isVip).length;
    });
    const loadTags = async () => {
      try {
        isLoading.value = true;
        error.value = "";
        console.log("�️ 开始加载标签列表...");
        const [tagsData, dailyStatusData] = await Promise.all([
          api_weixin.weixinApi.getTags(),
          api_weixin.weixinApi.getDailyStatus()
        ]);
        tags.value = tagsData;
        dailyStatus.value = dailyStatusData;
        isVip.value = dailyStatusData.isVip;
        console.log("✅ 标签列表加载成功:", tagsData.length, "个标签");
        console.log("✅ VIP状态:", dailyStatusData.isVip ? "VIP用户" : "普通用户");
        try {
          const stats = await api_weixin.weixinApi.getUserStarStats();
          userStats.value = stats;
          console.log("✅ 用户统计信息加载成功:", stats);
        } catch (statsError) {
          console.warn("⚠️ 用户统计信息加载失败:", statsError);
        }
      } catch (err) {
        console.error("❌ 数据加载失败:", err);
        error.value = err instanceof Error ? err.message : "加载数据失败";
      } finally {
        isLoading.value = false;
      }
    };
    const selectTag = async (tag) => {
      try {
        utils_audio.audioManager.playSoundEffect("click");
        console.log("🎯 选择标签:", tag.name);
        if (tag.status !== "active") {
          api_utils.showError("该标签暂时不可用");
          return;
        }
        if (tag.isVip && !isVip.value) {
          console.log("🔒 需要VIP权限访问标签:", tag.name);
          showVipUpgradeModal();
          return;
        }
        common_vendor.index.navigateTo({
          url: `/pages/tag-levels/index?tagId=${tag.id}&tagName=${encodeURIComponent(tag.name)}`
        });
      } catch (err) {
        console.error("❌ 选择标签失败:", err);
        api_utils.showError("进入标签关卡失败");
      }
    };
    const showVipUpgradeModal = () => {
      common_vendor.index.showModal({
        title: "VIP专享内容",
        content: "该标签为VIP专享内容，开通VIP会员即可畅玩所有标签关卡！",
        confirmText: "开通VIP",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.navigateTo({
              url: "/pages/member-center/index"
            });
          }
        }
      });
    };
    const goBack = () => {
      utils_audio.audioManager.playSoundEffect("click");
      common_vendor.index.navigateBack({
        delta: 1,
        fail: () => {
          common_vendor.index.switchTab({
            url: "/pages/index/index"
          });
        }
      });
    };
    common_vendor.onLoad((options) => {
      console.log("📱 标签闯关页面加载:", options);
    });
    common_vendor.onMounted(() => {
      loadTags();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: userStats.value
      }, userStats.value ? {
        b: common_vendor.t(userStats.value.completedLevels),
        c: common_vendor.t(userStats.value.totalStars)
      } : {}, {
        d: isLoading.value
      }, isLoading.value ? {} : error.value ? {
        f: common_vendor.t(error.value),
        g: common_vendor.o(loadTags)
      } : {
        h: common_vendor.f(tags.value, (tag, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(tag.name),
            b: tag.isVip
          }, tag.isVip ? {} : {}, {
            c: tag.isVip && !isVip.value
          }, tag.isVip && !isVip.value ? {} : {}, {
            d: common_vendor.t(tag.description || "暂无描述"),
            e: common_vendor.t(tag.levelCount || 0),
            f: tag.status === "active" ? 1 : "",
            g: common_vendor.t(tag.status === "active" ? "可用" : "维护中"),
            h: tag.id,
            i: tag.isVip ? 1 : "",
            j: tag.status === "active" ? 1 : "",
            k: tag.isVip && !isVip.value ? 1 : "",
            l: tag.color || "#667eea",
            m: common_vendor.o(($event) => selectTag(tag), tag.id)
          });
        })
      }, {
        e: error.value,
        i: common_vendor.o(goBack)
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ddedaf0e"]]);
wx.createPage(MiniProgramPage);
