# 音频文件说明

## 📁 音频文件目录结构

```
static/audio/
├── README.md                    # 本说明文件
├── background-main.mp3          # 主背景音乐（首页）
├── background-game.mp3          # 游戏背景音乐（游戏页面）
├── background-menu.mp3          # 菜单背景音乐（设置等页面）
├── click.mp3                    # 点击音效
├── success.mp3                  # 成功音效（配对成功）
├── fail.mp3                     # 失败音效（配对失败）
├── unlock.mp3                   # 解锁音效（解锁新关卡）
└── complete.mp3                 # 完成音效（通关完成）
```

## 🎵 音频文件要求

### 背景音乐
- **格式**: MP3
- **时长**: 30秒 - 2分钟（循环播放）
- **音质**: 128kbps - 320kbps
- **音量**: 适中，不要过于响亮
- **风格**: 轻松愉快，适合游戏场景

### 音效
- **格式**: MP3
- **时长**: 0.1秒 - 2秒
- **音质**: 128kbps
- **音量**: 比背景音乐稍小
- **特点**: 清脆明快，易于识别

## 🎧 音频文件说明

### 背景音乐

#### background-main.mp3
- **用途**: 首页背景音乐
- **特点**: 欢迎、温馨的氛围
- **循环**: 是

#### background-game.mp3
- **用途**: 游戏页面背景音乐
- **特点**: 专注、轻松的氛围，不干扰游戏
- **循环**: 是

#### background-menu.mp3
- **用途**: 设置页面等菜单背景音乐
- **特点**: 简洁、优雅的氛围
- **循环**: 是

### 音效

#### click.mp3
- **用途**: 按钮点击、卡片选择
- **特点**: 清脆的点击声
- **时长**: 0.1-0.3秒

#### success.mp3
- **用途**: 配对成功、操作成功
- **特点**: 愉快的成功提示音
- **时长**: 0.5-1秒

#### fail.mp3
- **用途**: 配对失败、操作失败
- **特点**: 温和的失败提示音（不要太刺耳）
- **时长**: 0.3-0.8秒

#### unlock.mp3
- **用途**: 解锁新关卡、获得奖励
- **特点**: 激动人心的解锁音效
- **时长**: 1-2秒

#### complete.mp3
- **用途**: 关卡完成、游戏胜利
- **特点**: 庆祝、胜利的音效
- **时长**: 1-3秒

## 🔧 技术要求

### 文件大小
- 背景音乐: 每个文件不超过 2MB
- 音效: 每个文件不超过 500KB
- 总音频文件大小不超过 10MB

### 兼容性
- 支持微信小程序
- 支持H5浏览器
- 支持iOS和Android设备

### 性能优化
- 使用适当的压缩率
- 避免过大的文件影响加载速度
- 考虑网络环境较差的用户

## 📝 使用说明

### 开发者
1. 将音频文件放置在对应目录
2. 确保文件名与代码中的路径一致
3. 测试所有音频文件的播放效果
4. 调整音量平衡

### 音频制作
1. 可以使用免费音效网站获取素材
2. 推荐网站：
   - Freesound.org
   - Zapsplat.com
   - Adobe Audition（付费）
   - Audacity（免费）

### 版权注意
- 确保使用的音频文件有合法授权
- 优先使用免费商用音频
- 标注音频来源和授权信息

## 🎮 游戏音频体验

### 音频层次
1. **背景音乐**: 持续播放，营造氛围
2. **交互音效**: 用户操作反馈
3. **状态音效**: 游戏状态变化提示

### 用户控制
- 用户可以在设置中开启/关闭背景音乐
- 用户可以在设置中开启/关闭音效
- 用户可以在设置中开启/关闭震动反馈

### 自动管理
- 页面切换时自动暂停/恢复背景音乐
- 应用进入后台时自动暂停音频
- 应用回到前台时自动恢复音频

## 🚀 未来扩展

### 可能的音频增强
- 不同关卡的专属背景音乐
- 更丰富的音效变化
- 音频可视化效果
- 自定义音频设置

### 技术优化
- 音频预加载机制
- 音频缓存策略
- 音频质量自适应
- 音频播放队列管理

---

**注意**: 当前目录中的音频文件为占位符，实际部署时需要替换为真实的音频文件。
