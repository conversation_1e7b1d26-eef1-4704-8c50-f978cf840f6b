/**
 * API相关的工具函数
 * 提供统一的错误处理、加载状态管理等功能
 */

import { ref, reactive } from 'vue'
import type { LoadingState, ApiError } from './types'

/**
 * 创建加载状态管理器
 */
export function createLoadingState(): LoadingState {
  return reactive({
    isLoading: false,
    error: null
  })
}

/**
 * 异步操作包装器，自动管理加载状态
 */
export async function withLoading<T>(
  loadingState: LoadingState,
  asyncFn: () => Promise<T>,
  options?: {
    showToast?: boolean
    toastDuration?: number
    errorMessage?: string
  }
): Promise<T | null> {
  const { showToast = true, toastDuration = 2000, errorMessage } = options || {}

  try {
    loadingState.isLoading = true
    loadingState.error = null

    const result = await asyncFn()
    return result
  } catch (error) {
    const apiError = error as ApiError
    const message = errorMessage || apiError.message || '操作失败'
    
    loadingState.error = message
    
    if (showToast) {
      uni.showToast({
        title: message,
        icon: 'none',
        duration: toastDuration
      })
    }
    
    console.error('异步操作失败:', error)
    return null
  } finally {
    loadingState.isLoading = false
  }
}

/**
 * 重试机制包装器
 */
export async function withRetry<T>(
  asyncFn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: any

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await asyncFn()
    } catch (error) {
      lastError = error
      
      if (attempt === maxRetries) {
        break
      }
      
      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, delay * (attempt + 1)))
    }
  }

  throw lastError
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: number | null = null

  return function (...args: Parameters<T>) {
    if (timeout !== null) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func.apply(this, args)
    }, wait)
  }
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let inThrottle = false

  return function (...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => {
        inThrottle = false
      }, wait)
    }
  }
}

/**
 * 格式化错误消息
 */
export function formatErrorMessage(error: any): string {
  if (typeof error === 'string') {
    return error
  }
  
  if (error && typeof error === 'object') {
    return error.message || error.error || '未知错误'
  }
  
  return '操作失败'
}

/**
 * 检查网络连接状态
 */
export function checkNetworkStatus(): Promise<boolean> {
  return new Promise((resolve) => {
    uni.getNetworkType({
      success: (res) => {
        resolve(res.networkType !== 'none')
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}

/**
 * 显示加载提示
 */
export function showLoading(title: string = '加载中...', mask: boolean = true) {
  uni.showLoading({
    title,
    mask
  })
}

/**
 * 隐藏加载提示
 */
export function hideLoading() {
  uni.hideLoading()
}

/**
 * 显示成功提示
 */
export function showSuccess(title: string, duration: number = 1500) {
  uni.showToast({
    title,
    icon: 'success',
    duration
  })
}

/**
 * 显示错误提示
 */
export function showError(title: string, duration: number = 2000) {
  uni.showToast({
    title,
    icon: 'none',
    duration
  })
}

/**
 * 显示确认对话框
 */
export function showConfirm(
  title: string,
  content?: string
): Promise<boolean> {
  return new Promise((resolve) => {
    uni.showModal({
      title,
      content: content || '',
      success: (res) => {
        resolve(res.confirm)
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}

/**
 * 安全的JSON解析
 */
export function safeJsonParse<T>(jsonString: string, defaultValue: T): T {
  try {
    return JSON.parse(jsonString)
  } catch (error) {
    console.warn('JSON解析失败:', error)
    return defaultValue
  }
}

/**
 * 安全的JSON字符串化
 */
export function safeJsonStringify(obj: any, defaultValue: string = '{}'): string {
  try {
    return JSON.stringify(obj)
  } catch (error) {
    console.warn('JSON字符串化失败:', error)
    return defaultValue
  }
}

/**
 * 格式化日期时间
 */
export function formatDateTime(dateString: string): string {
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return dateString
  }
}

/**
 * 计算通关率
 */
export function calculateCompletionRate(completed: number, total: number): number {
  if (total === 0) return 0
  return Math.round((completed / total) * 100)
}

/**
 * 构建查询字符串（兼容微信小程序）
 * 替代 URLSearchParams，因为微信小程序不支持该 API
 */
export function buildQueryString(params: Record<string, string | number | boolean | undefined | null>): string {
  const queryParts: string[] = []

  for (const [key, value] of Object.entries(params)) {
    if (value !== undefined && value !== null && value !== '') {
      queryParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`)
    }
  }

  return queryParts.join('&')
}

/**
 * 为 URL 添加查询参数
 */
export function appendQueryToUrl(baseUrl: string, params: Record<string, string | number | boolean | undefined | null>): string {
  const queryString = buildQueryString(params)
  if (!queryString) {
    return baseUrl
  }

  const separator = baseUrl.includes('?') ? '&' : '?'
  return `${baseUrl}${separator}${queryString}`
}
