"use strict";
const common_vendor = require("../common/vendor.js");
const api_weixin = require("../api/weixin.js");
const globalConfig = common_vendor.ref(null);
const isLoading = common_vendor.ref(false);
const error = common_vendor.ref(null);
function useGlobalConfig() {
  const fetchGlobalConfig = async (forceRefresh = false) => {
    if (globalConfig.value && !forceRefresh) {
      return globalConfig.value;
    }
    isLoading.value = true;
    error.value = null;
    try {
      const config = await api_weixin.weixinApi.getGlobalConfig();
      globalConfig.value = config;
      console.log("全局配置加载成功:", config);
      return config;
    } finally {
      isLoading.value = false;
    }
  };
  const resetConfig = () => {
    globalConfig.value = null;
    error.value = null;
    isLoading.value = false;
  };
  const getBackgroundMusicConfig = common_vendor.computed(() => {
    var _a;
    return (_a = globalConfig.value) == null ? void 0 : _a.backgroundMusicUrl;
  });
  const getHelpPageConfig = common_vendor.computed(() => {
    var _a;
    return (_a = globalConfig.value) == null ? void 0 : _a.helpUrl;
  });
  const getAppConfig = common_vendor.computed(() => {
    var _a;
    return (_a = globalConfig.value) == null ? void 0 : _a.app;
  });
  const getBackgroundMusicUrl = (pageType) => {
    const musicUrl = getBackgroundMusicConfig.value;
    return musicUrl;
  };
  const navigateToHelp = () => {
    const helpUrl = getHelpPageConfig.value;
    if (helpUrl.startsWith("http://") || helpUrl.startsWith("https://")) {
      common_vendor.index.navigateTo({
        url: `/pages/webview/index?url=${encodeURIComponent(helpUrl)}`
      });
    } else {
      common_vendor.index.navigateTo({
        url: helpUrl
      });
    }
  };
  const showHelpModal = () => {
    getHelpPageConfig.value;
    common_vendor.index.navigateTo({ url: "/pages/help/index" });
  };
  const initializeGlobalConfig = async () => {
    console.log("初始化全局配置...");
    await fetchGlobalConfig();
  };
  return {
    // 状态
    globalConfig: common_vendor.computed(() => globalConfig.value),
    isLoading: common_vendor.computed(() => isLoading.value),
    error: common_vendor.computed(() => error.value),
    // 计算属性
    backgroundMusicConfig: getBackgroundMusicConfig,
    helpPageConfig: getHelpPageConfig,
    appConfig: getAppConfig,
    // 方法
    fetchGlobalConfig,
    resetConfig,
    getBackgroundMusicUrl,
    navigateToHelp,
    showHelpModal,
    initializeGlobalConfig
  };
}
useGlobalConfig();
exports.useGlobalConfig = useGlobalConfig;
