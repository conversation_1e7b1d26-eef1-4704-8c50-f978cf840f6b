"use strict";
const common_vendor = require("../common/vendor.js");
require("../api/request.js");
const api_weixin = require("../api/weixin.js");
function isUserLoggedIn() {
  try {
    const userInfo = api_weixin.weixinApi.getLocalUserInfo();
    const openid = api_weixin.weixinApi.getOpenid();
    return !!(userInfo && openid && openid !== "openid");
  } catch (error) {
    console.error("检查登录状态失败:", error);
    return false;
  }
}
function getCurrentUser() {
  try {
    return api_weixin.weixinApi.getLocalUserInfo();
  } catch (error) {
    console.error("获取当前用户信息失败:", error);
    return null;
  }
}
async function checkLoginAndRedirect(options) {
  const {
    showToast = true,
    toastMessage = "请先登录",
    redirectUrl = "/pages/login/index"
  } = options || {};
  if (isUserLoggedIn()) {
    return true;
  }
  if (showToast) {
    common_vendor.index.showToast({
      title: toastMessage,
      icon: "none",
      duration: 2e3
    });
  }
  setTimeout(() => {
    common_vendor.index.navigateTo({
      url: redirectUrl
    });
  }, 1e3);
  return false;
}
function isWeixinCodeUsedError(error) {
  const errorMessage = error instanceof Error ? error.message : String(error);
  return errorMessage.includes("code已过期") || errorMessage.includes("code已经被使用") || errorMessage.includes("code been used") || errorMessage.includes("40163");
}
exports.checkLoginAndRedirect = checkLoginAndRedirect;
exports.getCurrentUser = getCurrentUser;
exports.isUserLoggedIn = isUserLoggedIn;
exports.isWeixinCodeUsedError = isWeixinCodeUsedError;
