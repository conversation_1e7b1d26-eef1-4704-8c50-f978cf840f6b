<template>
  <view v-if="visible" class="settings-modal-overlay" @click="handleOverlayClick">
    <view class="settings-modal" @click.stop>
      <!-- 标题栏 -->
      <view class="modal-header">
        <text class="modal-title">⚙️ 游戏设置</text>
        <view class="close-btn" @click="closeModal">
          <text class="close-icon">✕</text>
        </view>
      </view>

      <!-- 设置内容 -->
      <view class="modal-content">
        <!-- 背景音乐设置 -->
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-label">🎵 背景音乐</text>
            <text class="setting-desc">开启后在游戏中播放背景音乐</text>
          </view>
          <switch 
            :checked="settings.backgroundMusic" 
            @change="handleBackgroundMusicChange"
            color="#667eea"
          />
        </view>

        <!-- 音效设置 -->
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-label">🔊 游戏音效</text>
            <text class="setting-desc">开启后播放点击、成功等音效</text>
          </view>
          <switch 
            :checked="settings.soundEffects" 
            @change="handleSoundEffectsChange"
            color="#667eea"
          />
        </view>

        <!-- 震动设置 -->
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-label">📳 触觉反馈</text>
            <text class="setting-desc">开启后在特定操作时震动</text>
          </view>
          <switch 
            :checked="settings.vibration" 
            @change="handleVibrationChange"
            color="#667eea"
          />
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="modal-footer">
        <button class="test-btn" @click="testAudio">
          <text class="test-btn-text">🎧 测试音效</text>
        </button>
        <button class="confirm-btn" @click="closeModal">
          <text class="confirm-btn-text">确定</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { audioManager } from '../utils/audio'
import type { GameSettings } from '../api/types'

// Props
interface Props {
  visible: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  settingsChange: [settings: GameSettings]
}>()

// 设置数据
const settings = reactive<GameSettings>({
  backgroundMusic: true,
  soundEffects: true,
  vibration: true
})

// 初始化设置
const initSettings = () => {
  const currentSettings = audioManager.getSettings()
  Object.assign(settings, currentSettings)
}

// 监听弹窗显示状态
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    initSettings()
  }
})

// 处理背景音乐开关
const handleBackgroundMusicChange = (event: any) => {
  const enabled = event.detail.value
  settings.backgroundMusic = enabled
  
  // 更新音频管理器设置
  audioManager.updateSettings({ backgroundMusic: enabled })
  
  // 如果开启背景音乐，立即播放
  if (enabled) {
    audioManager.playBackgroundMusic('main')
  } else {
    audioManager.stopBackgroundMusic()
  }
  
  // 触发设置变更事件
  emit('settingsChange', { ...settings })
  
  console.log('背景音乐设置已更改:', enabled)
}

// 处理音效开关
const handleSoundEffectsChange = (event: any) => {
  const enabled = event.detail.value
  settings.soundEffects = enabled
  
  // 更新音频管理器设置
  audioManager.updateSettings({ soundEffects: enabled })
  
  // 触发设置变更事件
  emit('settingsChange', { ...settings })
  
  console.log('音效设置已更改:', enabled)
}

// 处理震动开关
const handleVibrationChange = (event: any) => {
  const enabled = event.detail.value
  settings.vibration = enabled
  
  // 更新音频管理器设置
  audioManager.updateSettings({ vibration: enabled })
  
  // 如果开启震动，立即测试
  if (enabled) {
    audioManager.vibrate('short')
  }
  
  // 触发设置变更事件
  emit('settingsChange', { ...settings })
  
  console.log('震动设置已更改:', enabled)
}

// 测试音效
const testAudio = () => {
  // 播放点击音效
  audioManager.playSoundEffect('click')
  
  // 延迟播放成功音效
  setTimeout(() => {
    audioManager.playSoundEffect('success')
  }, 500)
  
  // 触发震动
  audioManager.vibrate('short')
  
  uni.showToast({
    title: '音效测试完成',
    icon: 'none',
    duration: 1500
  })
}

// 关闭弹窗
const closeModal = () => {
  emit('close')
}

// 处理遮罩点击
const handleOverlayClick = () => {
  closeModal()
}

// 初始化
initSettings()
</script>

<style lang="scss" scoped>
.settings-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4rpx);
}

.settings-modal {
  width: 600rpx;
  max-height: 80vh;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50rpx) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #ffffff;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}

.close-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

.close-icon {
  font-size: 24rpx;
  font-weight: bold;
}

.modal-content {
  padding: 32rpx;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
  margin-right: 32rpx;
}

.setting-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 8rpx;
}

.setting-desc {
  display: block;
  font-size: 24rpx;
  color: #718096;
  line-height: 1.4;
}

.modal-footer {
  padding: 24rpx 32rpx 32rpx;
  display: flex;
  gap: 16rpx;
}

.test-btn {
  flex: 1;
  padding: 20rpx;
  background: linear-gradient(135deg, #48bb78, #38a169);
  border-radius: 16rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(72, 187, 120, 0.3);
}

.test-btn-text {
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 500;
}

.confirm-btn {
  flex: 1;
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.confirm-btn-text {
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 500;
}

.test-btn:active,
.confirm-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}
</style>
